{"name": "eslint-plugin-react-boundary", "version": "1.0.0", "description": "ESLint plugin to ensure React components are wrapped with Boundary", "main": "index.js", "scripts": {"test": "node test-plugin.js", "test-cases": "node test/test-cases.js"}, "keywords": ["eslint", "eslintplugin", "react", "boundary", "error-boundary", "suspense"], "author": "Your Name", "license": "MIT", "peerDependencies": {"eslint": ">=7.0.0"}, "engines": {"node": ">=12.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/eslint-plugin-react-boundary.git"}, "bugs": {"url": "https://github.com/yourusername/eslint-plugin-react-boundary/issues"}, "homepage": "https://github.com/yourusername/eslint-plugin-react-boundary#readme"}