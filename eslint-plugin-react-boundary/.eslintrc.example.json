{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react", "@typescript-eslint", "react-boundary"], "rules": {"react-boundary/require-boundary": ["error", {"boundaryComponent": "Boundary", "importSource": "react-suspense-boundary"}]}, "settings": {"react": {"version": "detect"}}}