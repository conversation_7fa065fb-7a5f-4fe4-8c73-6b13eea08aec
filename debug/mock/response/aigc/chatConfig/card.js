
/* eslint-disable max-lines */
/* eslint-disable max-len */
// @ts-nocheck

const stone = require('../../../utils/stone');
const feedCards = require('./feedCards');
const dayjs = require('dayjs');

const lpCard = {
    type: 100,
    payload: {
        field: 'page',
        value: {page: 'www.baidu2.com'},
        icgPromotionScene: 3,
        options: [
            {sourceType: 0, pageType: 1, pageUrl: 'www.baidu.com', picture: 'https://placehold.co/600x400', prompt: '选择【岚图FREE - 提表单】落地页'},
        ],
        cardParameters: {
            isMigration: true,
        },
    },
};

const yimeiLandingPage = {
    type: 141,
    payload: {
        field: 'page',
        value: {page: 'www.baidu2.com'},
        showOptionUrl: true,
        options: [
            {spuId: 1, pageType: 1, pageId: 1, pageName: '岚图FREE - 提表单', pageUrl: 'www.baidu.com', picture: 'https://s3-alpha-sig.figma.com/img/b83f/ac84/1679ddf53867503aad9ffce732db8e6a?Expires=1691366400&Signature=H0im-RAgLhgpEkLhaKYzPPX5LNBPipCILFS44uHFyq8dU3cT8w9L-asNrqPRO0aRZfkTBeG385H-cwW7ejn9Im5lMn4oxQpWSjLUH7Lf~C0El8ugKvmtlqwHKoghxQJ2xKy3FUTnGQH~sGkuEG23LndebUZn93qUIhH49i2ACL0Uit3UArayc2QEOgm6M2YbNmPZL6~sXOs4vxRkcFccPswN4Bs1oPfAdtElUWMpvesCd-LO0f81IUSsRlTXEZJP9yENUm4TFOfjLA3S8Jw1DWRqPiRWThf55Xi3~VxAUzfDoohIfh33zR3YMVTLzqh2qTfKNL~zJtidWm5BN5z-gw__&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4', prompt: '选择【岚图FREE - 提表单】落地页'},
            {pageType: 2, pageId: 2, pageName: '岚图FREE - 留咨询', pageUrl: 'www.baidu.com', picture: 'https://s3-alpha-sig.figma.com/img/b83f/ac84/1679ddf53867503aad9ffce732db8e6a?Expires=1691366400&Signature=H0im-RAgLhgpEkLhaKYzPPX5LNBPipCILFS44uHFyq8dU3cT8w9L-asNrqPRO0aRZfkTBeG385H-cwW7ejn9Im5lMn4oxQpWSjLUH7Lf~C0El8ugKvmtlqwHKoghxQJ2xKy3FUTnGQH~sGkuEG23LndebUZn93qUIhH49i2ACL0Uit3UArayc2QEOgm6M2YbNmPZL6~sXOs4vxRkcFccPswN4Bs1oPfAdtElUWMpvesCd-LO0f81IUSsRlTXEZJP9yENUm4TFOfjLA3S8Jw1DWRqPiRWThf55Xi3~VxAUzfDoohIfh33zR3YMVTLzqh2qTfKNL~zJtidWm5BN5z-gw__&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4', prompt: '选择【岚图FREE - 留咨询】落地页'},
            {pageType: 1, pageId: 3, pageName: '岚图FREE - 留咨询', pageUrl: 'www.baidu.com', picture: 'https://s3-alpha-sig.figma.com/img/b83f/ac84/1679ddf53867503aad9ffce732db8e6a?Expires=1691366400&Signature=H0im-RAgLhgpEkLhaKYzPPX5LNBPipCILFS44uHFyq8dU3cT8w9L-asNrqPRO0aRZfkTBeG385H-cwW7ejn9Im5lMn4oxQpWSjLUH7Lf~C0El8ugKvmtlqwHKoghxQJ2xKy3FUTnGQH~sGkuEG23LndebUZn93qUIhH49i2ACL0Uit3UArayc2QEOgm6M2YbNmPZL6~sXOs4vxRkcFccPswN4Bs1oPfAdtElUWMpvesCd-LO0f81IUSsRlTXEZJP9yENUm4TFOfjLA3S8Jw1DWRqPiRWThf55Xi3~VxAUzfDoohIfh33zR3YMVTLzqh2qTfKNL~zJtidWm5BN5z-gw__&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4', prompt: '选择【岚图FREE - 留咨询】落地页'},
            {pageType: 2, pageId: 4, pageName: '岚图FREE - 留咨询', pageUrl: 'www.baidu.com', picture: 'https://s3-alpha-sig.figma.com/img/b83f/ac84/1679ddf53867503aad9ffce732db8e6a?Expires=1691366400&Signature=H0im-RAgLhgpEkLhaKYzPPX5LNBPipCILFS44uHFyq8dU3cT8w9L-asNrqPRO0aRZfkTBeG385H-cwW7ejn9Im5lMn4oxQpWSjLUH7Lf~C0El8ugKvmtlqwHKoghxQJ2xKy3FUTnGQH~sGkuEG23LndebUZn93qUIhH49i2ACL0Uit3UArayc2QEOgm6M2YbNmPZL6~sXOs4vxRkcFccPswN4Bs1oPfAdtElUWMpvesCd-LO0f81IUSsRlTXEZJP9yENUm4TFOfjLA3S8Jw1DWRqPiRWThf55Xi3~VxAUzfDoohIfh33zR3YMVTLzqh2qTfKNL~zJtidWm5BN5z-gw__&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4', prompt: '选择【岚图FREE - 留咨询】落地页'},
            {pageType: 1, pageId: 5, pageName: '岚图FREE - 留咨询', pageUrl: 'www.baidu.com', picture: 'https://s3-alpha-sig.figma.com/img/b83f/ac84/1679ddf53867503aad9ffce732db8e6a?Expires=1691366400&Signature=H0im-RAgLhgpEkLhaKYzPPX5LNBPipCILFS44uHFyq8dU3cT8w9L-asNrqPRO0aRZfkTBeG385H-cwW7ejn9Im5lMn4oxQpWSjLUH7Lf~C0El8ugKvmtlqwHKoghxQJ2xKy3FUTnGQH~sGkuEG23LndebUZn93qUIhH49i2ACL0Uit3UArayc2QEOgm6M2YbNmPZL6~sXOs4vxRkcFccPswN4Bs1oPfAdtElUWMpvesCd-LO0f81IUSsRlTXEZJP9yENUm4TFOfjLA3S8Jw1DWRqPiRWThf55Xi3~VxAUzfDoohIfh33zR3YMVTLzqh2qTfKNL~zJtidWm5BN5z-gw__&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4', prompt: '选择【岚图FREE - 留咨询】落地页'},
            {pageType: 2, pageId: 6, pageName: '岚图FREE - 留咨询', pageUrl: 'www.baidu.com', picture: 'https://s3-alpha-sig.figma.com/img/b83f/ac84/1679ddf53867503aad9ffce732db8e6a?Expires=1691366400&Signature=H0im-RAgLhgpEkLhaKYzPPX5LNBPipCILFS44uHFyq8dU3cT8w9L-asNrqPRO0aRZfkTBeG385H-cwW7ejn9Im5lMn4oxQpWSjLUH7Lf~C0El8ugKvmtlqwHKoghxQJ2xKy3FUTnGQH~sGkuEG23LndebUZn93qUIhH49i2ACL0Uit3UArayc2QEOgm6M2YbNmPZL6~sXOs4vxRkcFccPswN4Bs1oPfAdtElUWMpvesCd-LO0f81IUSsRlTXEZJP9yENUm4TFOfjLA3S8Jw1DWRqPiRWThf55Xi3~VxAUzfDoohIfh33zR3YMVTLzqh2qTfKNL~zJtidWm5BN5z-gw__&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4', prompt: '选择【岚图FREE - 留咨询】落地页'},
            {pageType: 1, pageId: 7, pageName: '岚图FREE - 留咨询', pageUrl: 'www.baidu.com', picture: 'https://s3-alpha-sig.figma.com/img/b83f/ac84/1679ddf53867503aad9ffce732db8e6a?Expires=1691366400&Signature=H0im-RAgLhgpEkLhaKYzPPX5LNBPipCILFS44uHFyq8dU3cT8w9L-asNrqPRO0aRZfkTBeG385H-cwW7ejn9Im5lMn4oxQpWSjLUH7Lf~C0El8ugKvmtlqwHKoghxQJ2xKy3FUTnGQH~sGkuEG23LndebUZn93qUIhH49i2ACL0Uit3UArayc2QEOgm6M2YbNmPZL6~sXOs4vxRkcFccPswN4Bs1oPfAdtElUWMpvesCd-LO0f81IUSsRlTXEZJP9yENUm4TFOfjLA3S8Jw1DWRqPiRWThf55Xi3~VxAUzfDoohIfh33zR3YMVTLzqh2qTfKNL~zJtidWm5BN5z-gw__&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4', prompt: '选择【岚图FREE - 留咨询】落地页'},
            {pageType: 2, pageId: 8, pageName: '岚图FREE - 留咨询', pageUrl: 'www.baidu.com', picture: 'https://s3-alpha-sig.figma.com/img/b83f/ac84/1679ddf53867503aad9ffce732db8e6a?Expires=1691366400&Signature=H0im-RAgLhgpEkLhaKYzPPX5LNBPipCILFS44uHFyq8dU3cT8w9L-asNrqPRO0aRZfkTBeG385H-cwW7ejn9Im5lMn4oxQpWSjLUH7Lf~C0El8ugKvmtlqwHKoghxQJ2xKy3FUTnGQH~sGkuEG23LndebUZn93qUIhH49i2ACL0Uit3UArayc2QEOgm6M2YbNmPZL6~sXOs4vxRkcFccPswN4Bs1oPfAdtElUWMpvesCd-LO0f81IUSsRlTXEZJP9yENUm4TFOfjLA3S8Jw1DWRqPiRWThf55Xi3~VxAUzfDoohIfh33zR3YMVTLzqh2qTfKNL~zJtidWm5BN5z-gw__&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4', prompt: '选择【岚图FREE - 留咨询】落地页'},
            {pageType: 1, pageId: 9, pageName: '岚图FREE - 留咨询', pageUrl: 'www.baidu.com', picture: 'https://s3-alpha-sig.figma.com/img/b83f/ac84/1679ddf53867503aad9ffce732db8e6a?Expires=1691366400&Signature=H0im-RAgLhgpEkLhaKYzPPX5LNBPipCILFS44uHFyq8dU3cT8w9L-asNrqPRO0aRZfkTBeG385H-cwW7ejn9Im5lMn4oxQpWSjLUH7Lf~C0El8ugKvmtlqwHKoghxQJ2xKy3FUTnGQH~sGkuEG23LndebUZn93qUIhH49i2ACL0Uit3UArayc2QEOgm6M2YbNmPZL6~sXOs4vxRkcFccPswN4Bs1oPfAdtElUWMpvesCd-LO0f81IUSsRlTXEZJP9yENUm4TFOfjLA3S8Jw1DWRqPiRWThf55Xi3~VxAUzfDoohIfh33zR3YMVTLzqh2qTfKNL~zJtidWm5BN5z-gw__&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4', prompt: '选择【岚图FREE - 留咨询】落地页'},
        ],
    },
};

const icgGoodsMultiple = {
    type: 144,
    payload: {
        field: 'icgJytGoods',
        cardParameters: {
            manageProductUrl: 'https://b2bwork.baidu.com/api/auth/login?url64=aHR0cHM6Ly9iMmJ3b3JrLmJhaWR1LmNvbS9wcm9kdWN0L21hbmFnZT9mcm9tcz15enMmdWNpZD0zMTk5MTY4OSZ0YWI9YWxs',
            selectedGoods: [
                {
                    pageKey: 21080101,
                    pageName: '双诚环境螺旋板换热器-首页',
                    thumbnailUrl: 'https://b2b-web-memb-plat.bj.bcebos.com/KXn_TWw7HbmBhtNZIsMtMRAAMBk.',
                },
                {
                    pageKey: 43507101,
                    'pageName': 'publish-3-首页',
                    'thumbnailUrl': 'https://b2b-web-memb-plat.bj.bcebos.com/o2OQFp1XCazCwZ_ZjgljSxAAuLc.',
                },
            ],
            planId: 1243,
        },
    },
};

const icgGoodsWatch = {
    type: 164,
    payload: {
        field: 'icgJytGoods',
        cardParameters: {
            manageProductUrl: 'https://b2bwork.baidu.com/api/auth/login?url64=aHR0cHM6Ly9iMmJ3b3JrLmJhaWR1LmNvbS9wcm9kdWN0L21hbmFnZT9mcm9tcz15enMmdWNpZD0zMTk5MTY4OSZ0YWI9YWxs',
            selectedGoods: [
                {
                    pageKey: 21080101,
                    pageName: 'publish-3-首页',
                    score: 1,
                    thumbnailUrl: 'https://b2b-web-memb-plat.bj.bcebos.com/o2OQFp1XCazCwZ_ZjgljSxAAuLc.',
                },
                {
                    pageKey: 43507101,
                    score: 2,
                    pageName: 'publish-4-首页123123123123123123123123123123123123112312323423423432',
                    thumbnailUrl: 'https://b2b-web-memb-plat.bj.bcebos.com/o2OQFp1XCazCwZ_ZjgljSxAAuLc.',
                },
            ],
            planId: 1243,
        },
    },
};

const singleLpCard = {
    type: 117,
    payload: {
        field: 'urls',
        value: {page: 'www.baidu2.com'},
        options: [
            {
                title: '移动端',
                rejectReason: '理由理由理由理由理由理由理由理由理由',
                sourceType: 1,
                pageUrl: 'https://qingge.baidu.com/welcome?paramsA=a&paramsB=b&paramsC=c&paramsA=a&paramsB=b&paramsC=c&paramsA=a&paramsB=b&paramsC=c&paramsA=a&paramsB=b&paramsC=c',
                monitorUrl: 'https://s3-alpha-sig.figma.com/img/b83f/ac84/1679ddf53867503aad9ffce732db8e6a?Expires=1691366400&Signature=H0im-RAgLhgpEkLhaKYzPPX5LNBPipCILFS44uHFyq8dU3cT8w9L-asNrqPRO0aRZfkTBeG385H-cwW7ejn9Im5lMn4oxQpWSjLUH7Lf~C0El8ugKvmtlqwHKoghxQJ2xKy3FUTnGQH~sGkuEG23LndebUZn93qUIhH49i2ACL0Uit3UArayc2QEOgm6M2YbNmPZL6~sXOs4vxRkcFccPswN4Bs1oPfAdtElUWMpvesCd-LO0f81IUSsRlTXEZJP9yENUm4TFOfjLA3S8Jw1DWRqPiRWThf55Xi3~VxAUzfDoohIfh33zR3YMVTLzqh2qTfKNL~zJtidWm5BN5z-gw__&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4',
                'pageName': '我看看',
                'pageId': 62728983,
            },
            {
                title: '计算机端',
                rejectReason: '理由理由理由理由理由理由理由理由理由',
                sourceType: 0,
                pageUrl: 'https://qingge.baidu.com/welcome?paramsA=a&paramsB=b&paramsC=c&paramsA=a&paramsB=b&paramsC=c&paramsA=a&paramsB=b&paramsC=c&paramsA=a&paramsB=b&paramsC=c',
                'pageName': '我看看',
                'pageId': 62728983,
            },
        ],
    },
};

const radioCard = {
    type: 101, // 显示一个“优化目标”卡片
    payload: {
        field: 'campaignTransTypes',
        value: {campaignTransTypes: 3},
        options: [
            {value: 3, text: '表单提交成功', prompt: '我要将优化目标设置为表单提交成功'},
            {value: 4, text: '线索收集', prompt: '我要将优化目标设置为线索收集'},
            {value: 5, text: '表单提交成功', prompt: '我要将优化目标设置为表单提交成功'},
            {value: 6, text: '线索收集', prompt: '我要将优化目标设置为线索收集'},
            {value: 7, text: '表单提交成功', prompt: '我要将优化目标设置为表单提交成功'},
            {value: 8, text: '线索收集', prompt: '我要将优化目标设置为线索收集'},
            {value: 9, text: '表单提交成功', prompt: '我要将优化目标设置为表单提交成功'},
            {value: 10, text: '线索收集', prompt: '我要将优化目标设置为线索收集'},
            {value: 11, text: '表单提交成功', prompt: '我要将优化目标设置为表单提交成功'},
            {value: 12, text: '线索收集', prompt: '我要将优化目标设置为线索收集'},
        ],
    },
};

const business = {
    type: 140, // 业务点
    payload: {
        businessCategoryInfo: [
            {
                categoryName: '皮肤美容',
                childCategoryInfos: [
                    {categoryId: 9, categoryName: '除皱类', prompt: '除皱类'},
                    {categoryId: 1, categoryName: '去纹类', prompt: '去纹类'},
                    {categoryId: 2, categoryName: '祛妊娠纹', prompt: '祛妊娠纹'},
                    {categoryId: 3, categoryName: '射频类', prompt: '射频类'},
                    {categoryId: 4, categoryName: '手术类', prompt: '手术类'},
                    {categoryId: 5, categoryName: '注射类', prompt: '注射类'},
                    {categoryId: 6, categoryName: '针剂类', prompt: '针剂类'},
                ],
            },
            {
                categoryName: '减肥塑身',
                childCategoryInfos: [
                    {categoryId: 7, categoryName: '中医减肥', prompt: '中医减肥'},
                    {categoryId: 8, categoryName: '瘦身修复', prompt: '瘦身修复'},
                ],
            },
        ],
    },
};

const demandHotPointDescription = {
    type: 173, // 显示一个“优化目标”卡片
    payload: {
        field: 'appendSummaryFromHotWord',
        cardParameters: {
            candidateExpectKeywords: [
                {
                    'showWord': '营销要点1', // 前端只显示本字段
                    'pv': 123, // 流量
                    'candidateWordType': 1, // 类型，0-字面，1-非字面
                    'struct': ['abc', 'def'], // 结构化信息
                    'originate': 3, // 来源,
                    'flowLevel': 1,
                },
                {
                    'showWord': '营销要点2',
                    'pv': 23, // 流量
                    'candidateWordType': 0, // 类型，0-字面，1-非字面
                    'struct': ['abc'], // 结构化信息
                    'originate': 1, // 来源
                    'flowLevel': 2,
                },
            ],
        },
    },
};

const transTypeCard = {
    type: 118, // 显示一个“优化目标”卡片
    payload: {
        field: 'campaignTransTypes',
        value: {campaignTransTypes: 3},
        selectedOptions: [
            {
                'value': 1,
                'text': '咨询按钮点击',
                'prompt': '我要设置转化目标为咨询按钮点击',
            },
        ],
        options: [
            {
                'value': 1,
                'text': '咨询按钮点击',
                'prompt': '我要设置转化目标为咨询按钮点击',
            },
            {
                'value': 2,
                'text': '电话按钮点击',
                'prompt': '我要设置转化目标为电话按钮点击',
            },
            {
                'value': 3,
                'text': '表单提交成功',
                'prompt': '我要设置转化目标为表单提交成功',
            },
            {
                'value': 35,
                'text': '微信复制按钮点击',
                'prompt': '我要设置转化目标为微信复制按钮点击',
            },
            {
                'value': 67,
                'text': '微信调起按钮点击',
                'prompt': '我要设置转化目标为微信调起按钮点击',
            },
            {
                'value': 5,
                'text': '表单按钮点击',
                'prompt': '我要设置转化目标为表单按钮点击',
            },
            {
                'value': 72,
                'text': '聊到相关业务',
                'prompt': '我要设置转化目标为聊到相关业务',
            },
            {
                'value': 41,
                'text': '申请',
                'prompt': '我要设置转化目标为申请',
            },
            {
                'value': 73,
                'text': '回访-电话接通',
                'prompt': '我要设置转化目标为回访-电话接通',
            },
            {
                'value': 42,
                'text': '授信',
                'prompt': '我要设置转化目标为授信',
            },
            {
                'value': 74,
                'text': '回访-信息确认',
                'prompt': '我要设置转化目标为回访-信息确认',
            },
            {
                'value': 10,
                'text': '服务购买成功',
                'prompt': '我要设置转化目标为服务购买成功',
            },
            {
                'value': 75,
                'text': '回访-发现意向',
                'prompt': '我要设置转化目标为回访-发现意向',
            },
            {
                'value': 76,
                'text': '回访-高潜成交',
                'prompt': '我要设置转化目标为回访-高潜成交',
            },
            {
                'value': 77,
                'text': '回访-成单客户',
                'prompt': '我要设置转化目标为回访-成单客户',
            },
            {
                'value': 14,
                text: '订单提交成功',
                'prompt': '我要设置转化目标为订单提交成功',
            },
            {
                'value': 79,
                'text': '微信加粉成功',
                'prompt': '我要设置转化目标为微信加粉成功',
            },
            {
                'value': 17,
                'text': '三句话咨询',
                'prompt': '我要设置转化目标为三句话咨询',
            },
            {
                'value': 18,
                'text': '留线索',
                'prompt': '我要设置转化目标为留线索',
            },
            {
                'value': 50,
                'text': '预约',
                'prompt': '我要设置转化目标为预约',
            },
            {
                'value': 51,
                'text': '有意向客户',
                'prompt': '我要设置转化目标为有意向客户',
            },
            {
                'value': 56,
                'text': '到店',
                'prompt': '我要设置转化目标为到店',
            },
            {
                'value': 92,
                'text': '有效咨询',
                'prompt': '我要设置转化目标为有效咨询',
            },
            {
                'value': 30,
                'text': '电话拨通',
                'prompt': '我要设置转化目标为电话拨通',
            },
            {
                'value': 141,
                'text': '挂号',
                'prompt': '我要设置转化目标为挂号',
            },
            {
                'value': 999999,
                'text': '多目标转化',
                'prompt': '我要设置转化目标为多目标转化',
            },
        ],
    },
};

const timeSection = {
    type: 1003, // 显示一个“优化目标”卡片
    payload: {
        field: 'schedule',
        cardParameters: {
            selectedOptions: [
                {
                    'value': 1,
                    'text': '日间',
                    // label: '日间',
                    // 'prompt': '日间',
                },
            ],
        },
    },
};


const decisionCard = {
    type: 102, // 显示一个“选择A or B”卡片
    payload: {
        field: 'sex',
        options: [
            {
                value: 0, text: '不是，我需要指定性别', prompt: '不是，我需要指定性别',
                custom: {logger: {source: 'sex', field: 'no'}},
            },
            {
                value: 1, text: '是的，面向全性别', prompt: '是的，面向全性别',
                custom: {logger: {source: 'sex', field: 'yes'}},
            },
        ],
    },
};

const exampleCard = {
    type: 103, // 显示一个“示例”卡片
    payload: {
        example: {
            // title: '例如：',
            insert: 'insert',
            content: '447元。',
            text: '建议：根据您的业务和当前优化目标设置，为了保障广告竞争力，推荐您设置的出价不低于',
            type: 'substitution',
        },
        logger: {
            test: 1,
        },
    },
};

const dataInsights = {
    type: 126, // 显示一个“示例”卡片
    payload: {
        path: 'marsPro/GET/ReportDataService/getReportData', // 报告接口
        params: {token: 'xxx'},
        charts: [
            {
                type: 122, // 饼图性别
                field: 'genderId',
                indicator: 'cost',
            },
            {
                type: 125, // 横向柱状图                field: 'provinceName',
                xAxis: ['ageId'], // X轴类型
                yAxis: [{ // unit类型最多支持两种
                    name: 'cost',
                    type: 'value', // 数值用折线
                }], // Y轴类型
            },
            {
                type: 125, // 横向柱状图
                xAxis: ['provinceName'], // X轴类型
                yAxis: [{ // unit类型最多支持两种
                    name: 'cost',
                    type: 'value', // 数值用折线
                }], // Y轴类型
            },
        ],
    },
};

const scheduleCard = {
    type: 104,
    payload: {
        field: 'schedule',
        ...(Math.random() < 0.5 ? {} : {
            value: {
                schedule: [
                    {
                        weekDay: 1, // 1-7代表周一到周日
                        startHour: 0, // 暂停开始
                        endHour: 23, // 暂停结束
                    },
                    {
                        weekDay: 7, // 1-7代表周一到周日
                        startHour: 1, // 暂停开始
                        endHour: 24, // 暂停结束
                    },
                    {
                        weekDay: 6, // 1-7代表周一到周日
                        startHour: 5, // 暂停开始
                        endHour: 8, // 暂停结束
                    },
                    {
                        weekDay: 6, // 1-7代表周一到周日
                        startHour: 9, // 暂停开始
                        endHour: 10, // 暂停结束
                    },
                ],
            },
        }),
    },
};

const modOptAndTipCard = {
    type: 105, // 显示一个'提示操作'卡片
    payload: {
        campaignId: 13454,
        campaignName: '岚图free',
    },
};

const creativeImages = [
    {
        // 'picBosUrl': 'http://fc-video.cdn.bcebos.com/1652462e271bfc59139ac21dc94aef1e.jpg',
        'imageRatio': '3:1',
        'addFrom': 18,
        'tag': '版权图片推荐',
        'md5': '1652462e271bfc59139ac21dc94aef1e',
    },
    {
        imageId: 5232416865,
        width: 1280,
        height: 720,
        picBosUrl: 'https://fc-ccimage.baidu.com/0/pic/-1424770716_-1500585967_-1282019827.jpg',
        rawPicUrl: 'https://fc-ccimage.baidu.com/0/pic/-1424770716_-1500585967_-1282019827.jpg',
        auditStatus: 0, // 审核状态，0-审核通过，1-待审，2和3-审核拒绝
        errorMsg: '业务端报错了',
    },
    {
        imageId: 5302720444,
        width: 360,
        height: 360,
        picBosUrl: 'https://fc-ccimage.baidu.com/0/pic/-102726656_2096381705_203994559.jpg',
        rawPicUrl: 'https://fc-ccimage.baidu.com/0/pic/-102726656_2096381705_203994559.jpg',
        tag: '来自图片库',
        auditStatus: 2,
        errorMsg: '业务端报错了1',
    },
    {
        picBosUrl: 'https://vcg-image.cdn.bcebos.com/VCG211303661129_3207376639%2C267545524.jpeg',
        rawPicUrl: 'https://vcg-image.cdn.bcebos.com/VCG211303661129_3207376639%2C267545524.jpeg',
        tag: '来自图片库',
        addFrom: 18,
        md5: 'md5pic',
        auditStatus: 3,
        wholeReason: '审核拒绝理由测试一个超级长的，审核拒绝理由测试一个超级长的审核拒绝理由测试一个超级长的审核拒绝理由测试一个超级长的',
    },
];
const materialCard = {
    type: 106,
    payload: {
        field: 'pics',
        isRecommend: false,
        failInfo: true,
        aixProduct: [1],
        creativeImages,
        cardParameters: {
            equipmentType: 0,
        },
    },
};

const modCreativeImageCard = {
    type: 219,
    payload: {
        field: 'pics',
        isRecommend: false,
        failInfo: true,
        aixProduct: [1],
        creativeImages,
        pictureSegmentTypes: [
            {
                source: 0,
                segmentType: 101,
                auditContent: '{"score":6,"desc":"1","picUrl":"https://fc3tn.baidu.com/it/u=2171113738,3978856336&fm=203","segmentType":101,"rawPicUrl":"https://vcg-image.cdn.bcebos.com/VCG41N965015286_4041439866%2C2835535721.jpeg","imageId":"7681208622"}',
                addfrom: 524959,
                auditStatus: 3,
                // wholeReason: '审核拒绝理由测试一个超级长的，审核拒绝理由测试一个超级长的审核拒绝理由测试一个超级长的审核拒绝理由测试一个超级长的',
                wholeReason: JSON.stringify({'audit_detail': [{text: 'cuole'}]}),
            },
        ],
        cardParameters: {
            equipmentType: 0,
        },
    },
};

const creativeTexts = [
    {
        creativeTextId: 5566773,
        title: '岚图FREE真的比豪车还猛？这些数据证明她超乎你想象！',
        description1: '混动神器，岚图FREE增程，创新科技为你开辟更广阔的驾驭世界。',
        auditStatus: 3,
        wholeReason: '反动言论，赶紧换掉吧',
        errorMsg: '测试一下',
    },
    {
        creativeTextId: 5566774,
        title: '岚图FREE性能实测，零百加速4秒',
        description1: '一看就是愿意让你尽情驾驭的车，岚图FREE纯电，让你一秒',
        auditStatus: 0,
        addFrom: 19,
        md5: 'md51',
    },
    {
        creativeTextId: 5566778,
        title: '超燃！岚图FREE纯电动版，让你体验全新智能出行！',
        description1: '智能电动，掌控一切，岚图FREE增程，一键启动惊艳四座，自信展...',
        auditStatus: 2,
    },
    {
        creativeTextId: 5566779,
        title: '岚图FREE性能实测，零百加速4秒，带你直接飞上天！',
        description1: '一看就是愿意让你尽情驾驭的车，岚图FREE纯电，让你一秒爱上！',
        addFrom: 18,
        md5: 'md52',
    },
    {
        creativeTextId: 5566780,
        title: '岚图FREE性能实测，零百加速4秒，带你直接飞上天！',
        description1: '一看就是愿意让你尽情驾驭的车，岚图FREE纯电，让你一秒爱上！',
        addFrom: 18,
        md5: 'md52',
    },
    {
        creativeTextId: 5566781,
        title: '岚图FREE性能实测，零百加速4秒，带你直接飞上天11！',
        description1: '一看就是愿意让你尽情驾驭的车，岚图FREE纯电，让你一秒爱上！',
        addFrom: 18,
        md5: 'md52',
    },
];

const programTitles = [
    {
        creativeTextId: 5566773,
        content: '岚图FREE真的比豪车还猛？这些数据证明她超乎你想象！sdfshufhiudshfiusdiufhsuihf',
        auditStatus: 3,
        wholeReason: '反动言论，赶紧换掉吧',
        errorMsg: '测试一下',
    },
    {
        creativeTextId: 5566774,
        content: '岚图FREE性能实测，零百加速4秒',
        auditStatus: 0,
        addFrom: 19,
        md5: 'md51',
    },
    {
        creativeTextId: 5566778,
        content: '超燃！岚图FREE纯电动版，让你体验全新智能出行！',
        auditStatus: 2,
    },
    {
        creativeTextId: 5566779,
        content: '岚图FREE性能实测，零百加速4秒，带你直接飞上天！',
        addFrom: 18,
        md5: 'md52',
    },
    {
        creativeTextId: 5566780,
        content: '岚图FREE性能实测，零百加速4秒，带你直接飞上天1！',
        addFrom: 18,
        md5: 'md52',
    },
    {
        creativeTextId: 5566781,
        content: '岚图FREE性能实测，零百加速4秒，带你直接飞上天2！',
        addFrom: 18,
        md5: 'md52',
    },
    {
        creativeTextId: 5566782,
        content: '岚图FREE性能实测，零百加速4秒，带你直接飞上天3！',
        addFrom: 18,
        md5: 'md52',
    },
];

const programDescriptions = [
    {
        creativeTextId: 5566773,
        content: '混动神器，岚图FREE增程，创新科技为你开辟更广阔的驾驭世界。',
        auditStatus: 3,
        wholeReason: '反动言论，赶紧换掉吧',
        errorMsg: '测试一下',
    },
    {
        creativeTextId: 5566774,
        content: '一看就是愿意让你尽情驾驭的车，岚图FREE纯电，让你一秒',
        auditStatus: 0,
        addFrom: 19,
        md5: 'md51',
    },
    {
        creativeTextId: 5566778,
        content: '智能电动，掌控一切，岚图FREE增程，一键启动惊艳四座，自信展...',
        auditStatus: 2,
    },
    {
        creativeTextId: 5566779,
        content: '一看就是愿意让你尽情驾驭的车，岚图FREE纯电，让你一秒爱上！',
        addFrom: 18,
        md5: 'md52',
    },
];

const creativeTextsCheckGroupCard = {
    type: 107,
    payload: {
        field: 'creativeTexts',
        failInfo: false,
        creativeTexts,
        programTitles,
        programDescriptions,
        creativeTextType: 1,
        landPageUrl: 'https://qingge.baidu.com',
    },
};

const programCreativetextsSelector = {
    type: 186,
    payload: {
        field: 'creativeTexts',
        failInfo: false,
        cardParameters: {
            programTitles,
            programDescriptions,
            creativeTextType: 1,
        },
        landPageUrl: 'https://qingge.baidu.com',
    },
};
const creativeTextsGroupCard = {
    type: 108,
    payload: {
        field: 'creativeTexts',
        cardParameters: {
            creativeTexts,
            programTitles,
            programDescriptions,
            creativeTextType: 1,
        },
    },
};

const creativeMaterialsCard = {
    type: 109,
    payload: {
        cardParameters: {
            landPageUrl: 'https://qingge.baidu.com',
            fieldMap: {
                text: {field: 'creativeTexts', use: true},
                image: {field: 'pics', use: true},
                video: {field: 'videos', use: true},
            },
            creativeTextType: 0,
            equipmentType: 0,
        },
    },
};

const regionCard = {
    type: 110,
    payload: {
        field: 'region',
        value: {
            // eslint-disable-next-line
            region: [1000, 2000, 3000, 5000, 8296, 8300, 8301, 8302, 8303, 8484, 8485, 8486, 8487, 8488, 8489, 8490, 8491, 8492, 8493, 8521, 9127, 9128, 9130, 9131, 9132, 9133, 9134, 9135, 9136, 9137, 9138, 9139, 9140, 9141, 9142, 9143],
            // excludeRegionIds: [320312, 320321],
            regionTargetType: 1, // 0 自定义  默认以前都是0   1账户  2定向包
            // regionPackageId: 2,
            fcGeoLocation: 0,
            accountRegionIds: [], // 账户地域
        },
    },
};

const productList = [
    {id: '岚图FREE真的比豪车还猛？这些数据证明她超乎你想象！', text: '混动神器，岚图FREE增程，创新科技为你开辟更广阔的驾驭世界。'},
    {id: '岚图FREE性能实测，零百加速4秒', text: '一看就是愿意让你尽情驾驭的车，岚图FREE纯电，让你一秒爱上！'},
    {id: '超燃！岚图FREE纯电动版，让你体验全新智能出行！', text: '智能电动，掌控一切，岚图FREE增程，一键启动惊艳四座，自信展...'},
    {id: '岚图FREE性能实测，零百加速4秒，带你直接飞上天！', text: '一看就是愿意让你尽情驾驭的车，岚图FREE纯电，让你一秒爱上！'},
];

const productCard = {
    type: 111,
    payload: {
        field: 'product',
        options: productList,
    },
};

const crowdAgeCard = {
    type: 112,
    payload: {
        field: 'age',
        value: {age: [20, 55]},
    },
};

const creativeVideos = [
    {
        videoId: 483065947,
        videoUrl: 'http://nadvideo2.baidu.com/ee7bf23898c94e990d59eb926926e19d_1080_1920.mp4',
        thumbnail: 'http://nadvideo2.baidu.com/bfc696b45b88ded25f6faa9158932223.jpg',
        addFrom: 18,
        auditStatus: 2,
        wholeReason: '糟老头子没人看的，赶紧下课吧',
        errorMsg: '业务端报错1',
    },
    {
        videoId: 483059278,
        videoUrl: 'http://nadvideo2.baidu.com/6c956151d2822be48733529741d76caf_1920_1080.mp4',
        thumbnail: 'http://nadvideo2.baidu.com/25f35b6d13e96e97f2e6f768bab1c80e.jpg',
        addFrom: 19,
        auditStatus: 0,
    },
    {
        taskId: 666777,
        taskStatus: 1,
    },
    {
        thumbnail: 'http://nadvideo2.baidu.com/a374474b4f4831da163a4318936ea6c3.jpg',
        taskId: 221133,
        taskProgress: 59.7,
    },
    {
        thumbnail: 'http://nadecomimage.baidu.com/0/pic/1614657975_2023745744_-1648614348.jpg',
        taskId: 444555,
        taskStatus: 2,
    },
    {
        thumbnail: 'http://feed-image.baidu.com/0/pic/-2081263898_-2090810650_787959852.png',
        taskId: 888999,
        taskStatus: 4,
    },
];

const creativeVideosCard = {
    type: 113,
    payload: {
        field: 'creativeVideos',
        failInfo: true,
        creativeVideos,
        aixProduct: [1],
    },
};

const ModCreativeVideosCard = {
    type: 221,
    payload: {
        field: 'creativeVideos',
        failInfo: true,
        // creativeVideos,
        videoSegmentTypes: [
            {
                'source': 0,
                'segmentType': 10002,
                'auditContent': {
                    'videos': [
                        {
                            'videoId': 1497565551,
                            'videoUrl': 'http://nadvideo2.baidu.com/a915a9803f398f4fc90cce7edbf5af62_1080_1920.mp4',
                            'fragMentUrl': 'http://fc-transvideo.baidu.com/a781badfb9c8cf946a4041c09afdcce9.mp4',
                            'videoDesc': '',
                            'images': {
                                '203': [
                                    {
                                        'picUrl': 'https://fc-ccimage.baidu.com/0/pic/1627951490_-912649480_-519762096.jpg',
                                        'imageRatio': '3:4',
                                        'imageId': 7773015528,
                                    },
                                ],
                            },
                        },
                    ],
                },
            },
        ],
        aixProduct: [1],
    },
};

const videoScript = '拔完牙后必须要几天才能种牙吗？不需要种植牙要一两万，不需要本院全新种植牙价格表。您想了解的种牙价格都在这里，缺牙人群均可免费查询。'
    + '\n现在采用数字化精准种植单颗十分钟就能完成，跟真牙一样，想吃啥吃啥快速获取人工精准报价，先了解价格，再种牙不花冤枉钱，点击下方链接即可在线查询。';
const recommendVideoCard = {
    type: 114,
    payload: {
        field: 'creativeVideos',
        creativeVideos: [
            {
                videoUrl: 'http://nadvideo2.baidu.com/6c956151d2822be48733529741d76caf_1920_1080.mp4',
                thumbnail: 'http://nadvideo2.baidu.com/25f35b6d13e96e97f2e6f768bab1c80e.jpg',
                videoScript,
                videoJson: JSON.stringify({
                    templateId: 100110,
                    scriptTexts: [videoScript],
                    digitalHumanId: 100290,
                    backgroundImgId: 1000,
                    autoSave: true,
                    productLine: 'OPTIMIZATION',
                }),
                md5: 'recommend_md5md5',
            },
        ],
    },
};

const finishedCard = {
    type: 115,
    payload: {
        cardParameters: {
            equipmentType: 0,
            isShowPauseSug: true,
            landPageUrl: 'https://qingge.baidu.com',
            fieldMap: {
                text: {field: 'creativeTexts', use: true},
                image: {field: 'pics', use: true},
                video: {field: 'videos', use: true},
            },
        },
    },
};

const streamTextCard = {
    type: 116,
    payload: {
        // path: 'report-pilot/GET/ReportChatService/answer',
        field: 'askReportParams', estimatedWordCount: 100,
        askReportParams: {
            reportParams: {token: 'test-token', reportId: null},
            otherParams: {sessionId: 'xxx', query: 'yyy'},
        },
        content: '**搜索账户被拒**：已为您诊断搜索消费波动情况，昨日消费<Mark>356.78元</Mark>，环比下降<Mark status="warning">14%（49.05元）</Mark>，存在消费突降问题。您可以<Sug prompt="检查账户状态"></Sug>后再次查询',
    },
};

const brandCard = {
    type: 119,
    payload: {
        field: 'brand',
        options: [
            {brandId: 123, brandName: '品牌1', brandLogo: 'https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png', rejectReason: '审核拒绝理由1', isDefault: false},
            {brandId: 1234, brandName: '品牌12', brandLogo: 'https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png', rejectReason: '审核拒绝理由1', isDefault: false},
            {brandId: 1235, brandName: '品牌13', brandLogo: 'https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png', rejectReason: '审核拒绝理由1', isDefault: false},
            {brandId: 1236, brandName: '品牌14', brandLogo: 'https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png', rejectReason: '审核拒绝理由1', isDefault: true},
            {brandId: 12378, brandName: '品牌151', brandLogo: 'https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png', rejectReason: '审核拒绝理由1', isDefault: false},
            {brandId: 12371, brandName: '品牌啊端口帕斯卡152', brandLogo: 'https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png', rejectReason: '审核拒绝理由1', isDefault: false},
            {brandId: 12372, brandName: '品牌153', brandLogo: 'https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png', rejectReason: '审核拒绝理由1', isDefault: false},
            {brandId: 12373, brandName: '品啊是哒是哒萨达萨达撒旦牌154', brandLogo: 'https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png', rejectReason: '审核拒绝理由1', isDefault: false},
            {brandId: 12374, brandName: '品牌155', brandLogo: 'https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png', rejectReason: '审核拒绝理由1', isDefault: false},
            {brandId: 12375, brandName: '品牌156', brandLogo: 'https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png', rejectReason: '审核拒绝理由1', isDefault: false},
        ],
    },
};

const lineChart = {
    type: 120,
    payload: {
        path: 'marsPro/GET/ReportDataService/getReportData', // 报告接口
        params: {token: 'xxx'},
        xAxis: ['date'], // X轴类型
        yAxis: [{ // unit类型最多支持两种
            name: 'cost',
            // type: 'value',
        }, {
            name: 'impression',
            // type: 'ratio',
        }, {
            name: 'ctr',
            // type: 'ratio',
        }], // Y轴类型
        startDate: '2023-08-09',
        endDate: '2023-08-10',
    },
};

const oneLineChart = {
    type: 170,
    payload: {
        'reportParams': {
            'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
            'reportType': 1900000,
            'userIds': null,
            'startDate': '2024-03-19',
            'endDate': '2024-03-25',
            'timeUnit': 'DAY',
            'columns': [
                'date',
                'cost',
            ],
            'sorts': [
                {
                    'column': 'date',
                    'sortRule': 'ASC',
                    'compareField': null,
                },
            ],
            'filters': null,
            'startRow': 0,
            'rowCount': 50,
            'needSum': true,
            'splitColumn': null,
            'compareStartDate': null,
            'needCache': true,
            'addZeroRows': true,
            'withColumnMeta': false,
            'topCount': 0,
        },
        'yAxis': [
            {
                'name': 'cost',
                'type': 'value',
            },
        ],
        'xAxis': [
            'date',
        ],
        'level': 'account',
        'distribution': 'space-between',
        'highlightColumns': [],
        'failInfo': false,
    },
};

const optimizeAdvice = {
    type: 152,
    payload: {
        'level': 'campaign',
        'distribution': 'space-between',
        'diagnosisNew': [
            // {
            //     'caseLevel': 3,
            //     'id': *********,
            //     'name': '营销方案-*************',
            //     'type': 1,
            //     'contentId': 6,
            //     'content': '创意文本推荐',
            // },
            // {
            //     'caseLevel': 3,
            //     'id': *********,
            //     'name': '大搜通投1030',
            //     'type': 1,
            //     'contentId': 3,
            //     'content': '创意文本推荐',
            // },
            {
                'caseLevel': 3,
                'id': *********,
                'name': '营销方案-*************',
                'type': 1,
                'contentId': 4,
                'content': '创意图片推荐',
            },
            {
                'caseLevel': 3,
                'id': *********,
                'name': '大搜通投1030',
                'type': 1,
                'contentId': 4,
                'content': '创意图片推荐',
            },
            {
                'caseLevel': 3,
                'id': *********,
                'name': '大搜通投1030',
                'type': 1,
                'contentId': 7,
                'content': '创意图片推荐',
            },
            {
                'caseLevel': 2,
                'id': 630152,
                'name': 'searchlab',
                'type': 1,
                'contentId': 15,
                'content': '线索有效性低于行业平均水平',
            },
            {
                'caseLevel': 2,
                'id': 630152,
                'name': 'searchlab',
                'type': 1,
                'contentId': 16,
                'content': '线索有效性存在优化空间',
            },
            {
                'caseLevel': 2,
                'id': 630152,
                'name': 'searchlab',
                'type': 1,
                'contentId': 13,
                'content': JSON.stringify({
                    'inferenceMode': 1,
                    'expectKeywordCnt': 9,
                    'flowLevelHighCnt': 2,
                    'flowLevelMediumCnt': 3,
                    'flowLevelLowCnt': 4,
                    'sugs': [
                        '参照广告落地页补充产品/服务描述',
                        '参照搜索推广项目补充产品/服务描述',
                        '参考相关用户需求热点补充描述',
                        '切换为综合演绎',
                    ],
                }),
            },
            {
                'caseLevel': 3,
                'id': 630152,
                'name': 'searchlab',
                'type': 0,
                'contentId': 13,
                'content': JSON.stringify({
                    'inferenceMode': 1,
                    'expectKeywordCnt': 9,
                    'flowLevelHighCnt': 2,
                    'flowLevelMediumCnt': 3,
                    'flowLevelLowCnt': 4,
                    'sugs': [
                        '参照广告落地页补充产品/服务描述',
                        '参照搜索推广项目补充产品/服务描述',
                        '参考相关用户需求热点补充描述',
                        '切换为综合演绎',
                    ],
                }),
            },
        ],
        'cardParameters': {
            'tradeNameLevel1': 'xxx', // 一级行业名称
            'effect': { // 各项指标数据
                'click': '24%',
                'show': '',
                'cost': '46%',
                'ct': '57%',
            },
        },
        'failInfo': false,
    },
};

const barChart = {
    type: 121,
    payload: {
        path: 'marsPro/GET/ReportDataService/getReportData', // 报告接口
        params: {token: 'xxx'},
        xAxis: ['date'], // X轴类型
        yAxis: [{ // unit类型最多支持两种
            name: 'cost',
            // type: 'value',
        }, {
            name: 'ctr',
            // type: 'ratio',
        }, {
            name: 'impression',
        }], // Y轴类型
    },
};
const horizontalBar = {
    type: 125, // 横向柱状图
    payload: {
        path: 'marsPro/GET/ReportDataService/getReportData', // 报告接口
        params: {token: 'xxx'},
        xAxis: ['ageId'], // X轴类型
        yAxis: [{ // unit类型最多支持两种
            name: 'cost',
            type: 'value', // 数值用折线
        }], // Y轴类型
    },
};

const industryInsights = {
    type: 128,
    payload: {
        path: 'marsPro/GET/ReportDataService/getReportData', // 报告接口
        tradeInsightParams: {token: 'xxx'},
        tradeExplainParams: {token: 'xxx'},
    },
};

const pieChart = {
    type: 122,
    payload: {
        path: 'marsPro/GET/ReportDataService/getReportData', // 报告接口
        params: {token: 'xxx'},
        field: 'ageId',
        indicator: 'click',
    },
};

const indicator = {
    type: 123,
    payload: {
        path: 'marsPro/GET/ReportDataService/getReportData', // 报告接口
        params: {token: 'xxx'},
        series: [{
            name: 'click',
        }, {
            name: 'impression',
        }, {
            name: 'ctr',
        }, {
            name: 'cpc',
        }, {
            name: 'cost',
        }],
    },
};

const searchReport = {
    type: 124,
    payload: {
        path: 'marsPro/GET/ReportDataService/getReportData', // 报告接口
        reportParams: {token: 'xxx'},
    },
};

const videoReport = {
    type: 129,
    payload: {
        path: 'marsPro/GET/ReportDataService/getReportData', // 报告接口
        reportParams: {token: 'xxx'},
    },
};

const baiduIndexReport = {
    type: 131,
    payload: {
        baiduIndexResponse: {},
    },
};

const icgGoods = {
    type: 130,
    payload: {
        field: 'icgGoods',
        'options': [{
            'url': 'http://groupoffline.wejianzhan.com/site/wjzkkvp/d6ebc039-ed66-4878-88e0-146b4e5a29cb?dynType=4', // 商品投放页URL
            'id': 105992414, // 商品id
            'name': '1搜索广告投放平台-780646', // 商品名称
            'score': 1,
            'thumbnailUrl': 'xxx.com/thumbnailUrl', // 商品投放页封面
            'previewUrl': '', // 商品投放页预览URL
            'prompt': '我想推广的是【翡翠首饰 手镯 吊坠 珠链 一手货源 高质缅甸原产原石 纯净无杂质】（ID：2831213643）',
        },
        {
            'url': 'http://groupoffline.wejianzhan.com/site/wjzkkvp/d6ebc039-ed66-4878-88e0-146b4e5a29cb?dynType=4', // 商品投放页URL
            'id': 1059924141, // 商品id
            'score': 2,
            'name': '2搜索广告投放平台-780646', // 商品名称
            'thumbnailUrl': 'xxx.com/thumbnailUrl', // 商品投放页封面
            'previewUrl': '', // 商品投放页预览URL
            'prompt': '我想推广的是【翡翠首饰 手镯 吊坠 珠链 一手货源 高质缅甸原产原石 纯净无杂质】（ID：2831213643）',
        },
        {
            'url': 'http://groupoffline.wejianzhan.com/site/wjzkkvp/d6ebc039-ed66-4878-88e0-146b4e5a29cb?dynType=4', // 商品投放页URL
            'id': 1059924142, // 商品id
            'score': 3,
            'name': '3搜索广告投放平台-780646', // 商品名称
            'thumbnailUrl': 'xxx.com/thumbnailUrl', // 商品投放页封面
            'previewUrl': '', // 商品投放页预览URL
            'prompt': '我想推广的是【翡翠首饰 手镯 吊坠 珠链 一手货源 高质缅甸原产原石 纯净无杂质】（ID：2831213643）',
        },
        {
            'url': 'http://groupoffline.wejianzhan.com/site/wjzkkvp/d6ebc039-ed66-4878-88e0-146b4e5a29cb?dynType=4', // 商品投放页URL
            'id': 1059924143, // 商品id
            'score': 4,
            'name': '4搜索广告投放平台-780646', // 商品名称
            'thumbnailUrl': 'xxx.com/thumbnailUrl', // 商品投放页封面
            'previewUrl': '', // 商品投放页预览URL
            'prompt': '我想推广的是【翡翠首饰 手镯 吊坠 珠链 一手货源 高质缅甸原产原石 纯净无杂质】（ID：2831213643）',
        },
        {
            'url': 'http://groupoffline.wejianzhan.com/site/wjzkkvp/d6ebc039-ed66-4878-88e0-146b4e5a29cb?dynType=4', // 商品投放页URL
            'id': 1059924144, // 商品id
            'name': '5搜索广告投放平台-780646', // 商品名称
            'thumbnailUrl': 'xxx.com/thumbnailUrl', // 商品投放页封面
            'previewUrl': '', // 商品投放页预览URL
            'prompt': '我想推广的是【翡翠首饰 手镯 吊坠 珠链 一手货源 高质缅甸原产原石 纯净无杂质】（ID：2831213643）',
        },
        {
            'url': 'http://groupoffline.wejianzhan.com/site/wjzkkvp/d6ebc039-ed66-4878-88e0-146b4e5a29cb?dynType=4', // 商品投放页URL
            'id': 1059924145, // 商品id
            'name': '6搜索广告投放平台-780646', // 商品名称
            'thumbnailUrl': 'xxx.com/thumbnailUrl', // 商品投放页封面
            'previewUrl': '', // 商品投放页预览URL
            'prompt': '我想推广的是【翡翠首饰 手镯 吊坠 珠链 一手货源 高质缅甸原产原石 纯净无杂质】（ID：2831213643）',
        },
        {
            'url': 'http://groupoffline.wejianzhan.com/site/wjzkkvp/d6ebc039-ed66-4878-88e0-146b4e5a29cb?dynType=4', // 商品投放页URL
            'id': 1059924146, // 商品id
            'name': '7搜索广告投放平台-780646', // 商品名称
            // eslint-disable-next-line max-lines
            'thumbnailUrl': 'xxx.com/thumbnailUrl', // 商品投放页封面
            'previewUrl': '', // 商品投放页预览URL
            'prompt': '我想推广的是【翡翠首饰 手镯 吊坠 珠链 一手货源 高质缅甸原产原石 纯净无杂质】（ID：2831213643）',
        },
        {
            'url': 'http://groupoffline.wejianzhan.com/site/wjzkkvp/d6ebc039-ed66-4878-88e0-146b4e5a29cb?dynType=4', // 商品投放页URL
            'id': 1059924147, // 商品id
            'name': '8搜索广告投放平台-780646', // 商品名称
            'thumbnailUrl': 'xxx.com/thumbnailUrl', // 商品投放页封面
            'previewUrl': '', // 商品投放页预览URL
            'prompt': '我想推广的是【翡翠首饰 手镯 吊坠 珠链 一手货源 高质缅甸原产原石 纯净无杂质】（ID：2831213643）',
        },
        {
            'url': 'http://groupoffline.wejianzhan.com/site/wjzkkvp/d6ebc039-ed66-4878-88e0-146b4e5a29cb?dynType=4', // 商品投放页URL
            'id': 1059924148, // 商品id
            'name': '9搜索广告投放平台-780646', // 商品名称
            'thumbnailUrl': 'xxx.com/thumbnailUrl', // 商品投放页封面
            'previewUrl': '', // 商品投放页预览URL
            'prompt': '我想推广的是【翡翠首饰 手镯 吊坠 珠链 一手货源 高质缅甸原产原石 纯净无杂质】（ID：2831213643）',
        },
        {
            'url': 'http://groupoffline.wejianzhan.com/site/wjzkkvp/d6ebc039-ed66-4878-88e0-146b4e5a29cb?dynType=4', // 商品投放页URL
            'id': 1059924149, // 商品id
            'name': '10搜索广告投放平台-780646', // 商品名称
            'thumbnailUrl': 'xxx.com/thumbnailUrl', // 商品投放页封面
            'previewUrl': '', // 商品投放页预览URL
            'prompt': '我想推广的是【翡翠首饰 手镯 吊坠 珠链 一手货源 高质缅甸原产原石 纯净无杂质】（ID：2831213643）',
        },
        {
            'url': 'http://groupoffline.wejianzhan.com/site/wjzkkvp/d6ebc039-ed66-4878-88e0-146b4e5a29cb?dynType=4', // 商品投放页URL
            'id': 10599241410, // 商品id
            'name': '11搜索广告投放平台-780646', // 商品名称
            'thumbnailUrl': 'xxx.com/thumbnailUrl', // 商品投放页封面
            'previewUrl': '', // 商品投放页预览URL
            'prompt': '我想推广的是【翡翠首饰 手镯 吊坠 珠链 一手货源 高质缅甸原产原石 纯净无杂质】（ID：2831213643）',
        }],
    },
};


const link = {
    type: 132,
    payload: {
        descAndLinks: [
            '更多产品/服务描述撰写技巧，',
            {text: '轻舸产品手册', toUrl: 'https://yingxiao.baidu.com/course/outside/detail?from=yingxiaoSearch&detailId=6513'},
        ],
    },
};

const layout = {
    type: 133,
    payload: {
        cards: [
            decisionCard,
            link,
        ],
        distribution: 'space-between',
    },
};

const FeedAppSelector = {
    type: 201,
    payload: {
        appInfoList: [{
            appName: '封神',
            platform: 1,
            icon: 'https://app-center.cdn.bcebos.com/appcenter/file/upload/e18648a3020fc820b010db84e92b283a4fed389a-f1d9-4bfe-b04c-5125dc31c167.png',
            version: '1.0',
            summary: 'xxxxxxxxx',
            packageName: 'com.fsbaidu.yyds',
            channelId: 10000590487,
            prompt: '确认使用【com.fsbaidu.yyds】包名。',
        }, {
            appName: '封神ios',
            platform: 3,
            icon: 'https://app-center.cdn.bcebos.com/appcenter/file/upload/e18648a3020fc820b010db84e92b283a4fed389a-f1d9-4bfe-b04c-5125dc31c167.png',
            version: '1.0',
            summary: 'xxxxxxxxx',
            packageName: 'com.fsbaidu.yyds',
            channelId: 10000590488,
            prompt: '确认使用【com.fsbaidu.yyds】包名。',
        }],
        field: 'appInfo',
    },
};

const appSelectorCard = {
    type: 134,
    payload: {
        appInfoList: [{
            appName: '封神',
            platform: 1,
            icon: 'https://app-center.cdn.bcebos.com/appcenter/file/upload/e18648a3020fc820b010db84e92b283a4fed389a-f1d9-4bfe-b04c-5125dc31c167.png',
            version: '1.0',
            summary: 'xxxxxxxxx',
            packageName: 'com.fsbaidu.yyds',
            channelId: 10000590487,
            prompt: '确认使用【com.fsbaidu.yyds】包名。',
        }, {
            appName: '封神ios',
            platform: 3,
            icon: 'https://app-center.cdn.bcebos.com/appcenter/file/upload/e18648a3020fc820b010db84e92b283a4fed389a-f1d9-4bfe-b04c-5125dc31c167.png',
            version: '1.0',
            summary: 'xxxxxxxxx',
            packageName: 'com.fsbaidu.yyds',
            channelId: 10000590488,
            prompt: '确认使用【com.fsbaidu.yyds】包名。',
        }],
        field: 'appInfo',
    },
};

const assetCard = {
    type: 142,
    payload: {
        field: 'appTransAssets',
        cardParameters: {
            transAssets: [
                {
                    transType: 3,
                    transId: 123,
                    transName: '下载+注册付费',
                    isBind: true,
                },
                {
                    transType: 3,
                    transId: 234,
                    transName: '金融ces',
                },
                {
                    transType: 3,
                    transId: 345,
                    transName: '测试双出价2',
                },
                {
                    transType: 3,
                    transId: 456,
                    transName: '测试双出价3',
                },
            ],
        },
    },
};

const feedAssetCard = {
    type: 197,
    payload: {
        field: 'appTransAssets',
        cardParameters: {
            feedProjectType: {
                'projectFeedId': 553717697,
                'projectFeedName': '项目-参照-测试无图片计划a89',
                'subject': 8,
                'appInfo': {
                    'downloadType': 0,
                    'appName': 'haha',
                    'apkName': 'com.baidu',
                    'docId': 12345,
                    'channelId': 0,
                },
                'pause': false,
                'status': 20,
                'bidMode': 1,
                'addTime': '2024-06-18 15:21:01',
                'ocpc': {
                    'ocpcBid': 88.0,
                    'deepOcpcBid': 99,
                    'ocpcLevel': 2,
                    'transType': 3,
                    'transFrom': 2,
                    'deepTransType': 29,
                    'appTransId': 65533938,
                    'isSkipStageOne': false,
                    'urlType': 1,
                    'isOptimizeDeepTrans': true,
                    'deepOcpcStatus': 0,
                    'useRoi': false,
                    'roiRatio': 0.0,
                    'isManualBidForMaxMode': 0,
                    'isManualDeepBidForMaxMode': 0,
                    'transTypeName': '表单提交成功',
                    'transTargetType': 2,
                    'isNewVersion': 1,
                },
                'projectOcpxStatus': -1,
                'catalogSource': 2,
                'productType': 4,
                'campaignFeedIds': [
                    543540928,
                ],
                'catalogId': 2095828,
                'productStatus': 2,
                'mandatoryOperation': 0,
                'adSource': 0,
                'aiTag': 1,
                'liftSwitch': 0,
            },
        },
        aixProduct: [2],
    },
};

const SingleProjectCard = {
    type: 198,
    payload: {
        field: 'singleProject',
        fcProjectType: {
            'projectId': '123213',
            'index': 0,
            'projectName': '轻舸投放项目_07_19_14:55:40',
            'marketingTargetId': 7,
            'projectModeType': 0,
            'sharedBudget': 1638,
            'useSharedBudget': 1,
            'transAsset': 0,
            'transTypes': [
                18,
            ],
            'assistTransTypes': [
                75,
            ],
            'ocpcBidType': 1,
            'ocpcBid': 123,
            'deepTransTypeMode': 1,
            'ocpcDeepBid': 124,
            'bindIds': [
                97221573,
                97128356,
                97222843,
                97222833,
                97230512,
            ],
            'unbindSharedBudgetInfo': [],
            'useLiftBudget': 1,
            'liftBudget': 230,
            'transManagerMode': 0,
            'sharedBudgetType': 7,
            'liftBudgetMode': 1,
            'liftBudgetSchedule': [
                {
                    'hour': 1721372400,
                    'weekDay': 0,
                },
            ],
            'aixProjectStatus': true,
            'projectSharedBudgetType': 7,
            'useIndustryAIMaxPromotion': true,
            'aiMaxPromotion': {
                'type': 0,
                'level': 1,
                'targetingOptimize': true,
                'creativeOptimize': true,
                'crowdOptimize': false,
            },
            'crowdQualityOcpcBidRatio': [
                {
                    'quality': 1,
                    'ratio': 1,
                    'businessId': 2001,
                },
            ],
            'ocpcBidRatioType': 4,
        },
        projectProduct: 1,
    },
};


const FcProjectIncludeCampaign = {
    type: 199,
    payload: {
        field: 'fcProjectIncludeCampaign',
        fcProjectType: {
            bindIds: [123],
            marketingTargetId: 7,
        },
    },
};

const FeedCampaignSelector = {
    type: 200,
    payload: {
        cardParameters: {
        },
        field: 'bindCampaign',
    },
};

const negativeWordPacket = {
    type: 136,
    payload: {
        field: 'negativePackets',
        cardParameters: {
            negativePacketInfos: [{
                negativePacketId: 417322,
            }],
        },
        logger: {
            field: 'manage_entry',
            source: 'negative_packet',
            count: 5890,
        },
    },
};

const errorInfoCard = {
    type: 139,
    payload: {
        errorInfos: [
            {errorMsg: 'xxx', renderer: {type: 'campaignName', info: {campaignName: '岚图FREEL2+级智能驾驶辅助系统性能级智能'}}},
            {errorMsg: 'xxx', renderer: {type: 'campaignName', info: {campaignName: '岚图FREEL3+级智能驾驶辅助系统性能级智能'}}},
        ],
    },
};

const crowdCard = {
    type: 143,
    payload: {
        cardParameters: {
            excludeXingheGroupIds: [],
        },
    },
};

const projectCard = {
    type: 145,
    payload: {
        cardParameters: {
            getProjectParams: {
                'sortField': 'cost',
                'isDesc': true,
                'startTime': '2023-10-23',
                'endTime': '2023-11-23',
                'limit': [0, 20],
                'fieldFilters': [{
                    'field': 'cost',
                    'op': 'gt',
                    'values': ['0'],
                }],
            },
        },
        field: 'expectBusinessSummary',
    },
};

const prompt = `我要推广汇能教育护士执业资格证考试培训业务，我们是一家专业的护士执业资格证考试培训机构，通过我们的培训，可以帮助考生高效备考，提高考试通过率。

我们的培训课程针对护士执业资格证考试的重难点和高频考点进行深度解析，帮助考生突破考试中的难点，提高考试成绩。我们的课程采用面授形式，老师全程面对面教学辅导，让考生更直观地了解考试内容和考试技巧。此外，我们还采用全日制集训营的方式，为考生提供全方位的学习支持和考前冲刺，帮助考生在短期内取得突破。

我们的培训课程不仅针对考试的各个科目进行精讲，而且还提供模考和考前冲刺的机会，让考生更好地了解自己的考试水平。我们还提供专属老师全程面对面教学辅导，针对解析考点，帮助考生更好地掌握考试技巧。此外，我们还采用双师教学的模式，为考生提供更全面、更优质的学习支持。

我们的目标是通过专业的培训和服务，帮助考生顺利通过护士执业资格证考试。我们的品牌和教学已经得到了广泛认可和信任，我们相信通过我们的培训和服务，可以帮助更多的考生实现自己的梦想。

与我的推广业务相似的有XXX等公司，但我们的培训和服务更专业、更优质，相信我们的品牌和服务会得到更多人的认可和信任。`;
const projectPromptCard = {
    type: 146,
    payload: {
        cardParameters: {
            'text': prompt,
            'projectId': 417322,
            'addFrom': 0, // 0-出价策略， 3-项目
            'confirmBtnPrompt': '点击了确认的prompt',
            source: 4,
            path: 'report-pilot/GET/ReportChatService/answer',
            params: {
                reportParams: {token: 'test-token', reportId: null},
                otherParams: {sessionId: 'xxx', query: 'yyy'},
            },
        },
        field: 'expectBusinessSummary',
    },
};

const carCornerCard = {
    type: 153,
    payload: {
        cardParameters: {
            cornerLabels: [
                '新车上市', '尊享好礼', '新车促销', // 智能体以配置文件形式约定
            ],
            currentLabel: '尊享好礼',
        },
        field: 'creativeSegments',
    },
};
// 万能卡片
const universalReportCard = {
    type: 156,
    payload: {
        'distribution': 'space-between',
        'subItems': [
            {
                'subType': 116,
                'payload': {
                    'distribution': 'space-between',
                    'content': '<strong>数据解读：</strong>为您查询到账户近7日各项核心指标和数据趋势如下',
                },
                'config': {
                    'alertType': null,
                    'classNameList': [
                        'list-indent',
                    ],
                },
            },
            {
                'subType': 160,
                'payload': {
                    'distribution': 'space-between',
                },
                'config': null,
            },
            {
                'subType': 158,
                'payload': {
                    'campaignTransTypes': [
                        {
                            'value': 130,
                        },
                    ],
                    'reportParams': {
                        'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
                        'reportType': 1900001,
                        'userIds': [
                            630152,
                        ],
                        'startDate': '2024-03-13',
                        'endDate': '2024-03-19',
                        'timeUnit': 'DAY',
                        'columns': [
                            'cost',
                            'click',
                            'impression',
                            'ocpcTargetTrans',
                            'ctr',
                            'ocpcTargetTransRatio',
                            'ocpcTargetTransCPC',
                        ],
                        'sorts': null,
                        'filters': null,
                        'startRow': 0,
                        'rowCount': 1000,
                        'needSum': true,
                        'splitColumn': null,
                        'compareStartDate': null,
                        'needCache': true,
                        'addZeroRows': true,
                        'withColumnMeta': false,
                        'topCount': 0,
                    },
                    'compareReportParams': {
                        'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
                        'reportType': 1900001,
                        'userIds': [
                            630152,
                        ],
                        'startDate': '2024-03-13',
                        'endDate': '2024-03-19',
                        'timeUnit': 'DAY',
                        'columns': [
                            'cost',
                            'click',
                            'impression',
                            'ocpcTargetTrans',
                            'ctr',
                            'ocpcTargetTransRatio',
                            'ocpcTargetTransCPC',
                        ],
                        'sorts': null,
                        'filters': null,
                        'startRow': 0,
                        'rowCount': 1000,
                        'needSum': true,
                        'splitColumn': null,
                        'compareStartDate': '2024-03-06',
                        'needCache': true,
                        'addZeroRows': true,
                        'withColumnMeta': false,
                        'topCount': 0,
                    },
                    'level': 'account',
                    'distribution': 'space-between',
                    'aixProduct': [],
                },
                'config': {
                    'alertType': 'white',
                    'classNameList': null,
                },
            },
            {
                'subType': 157,
                'payload': {
                    'options': [
                        {
                            'label': '账户数据',
                            'value': 'account',
                        },
                    ],
                    'campaignTransTypes': [
                        {
                            'value': 130,
                        },
                    ],
                    'level': 'account',
                    'distribution': 'space-between',
                    'defaultOption': 'account',
                    'apiMap': {
                        'account': 'marsPro/GET/ReportDataService/getReportData',
                    },
                    'reportParamsMap': {
                        'account': {
                            'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
                            'reportType': 1900001,
                            'userIds': [
                                630152,
                            ],
                            'startDate': '2024-03-13',
                            'endDate': '2024-03-19',
                            'timeUnit': 'DAY',
                            'columns': [
                                'cost',
                                'click',
                                'impression',
                                'ocpcTargetTrans',
                                'ctr',
                                'ocpcTargetTransRatio',
                                'ocpcTargetTransCPC',
                            ],
                            'sorts': null,
                            'filters': null,
                            'startRow': 0,
                            'rowCount': 1000,
                            'needSum': true,
                            'splitColumn': null,
                            'compareStartDate': null,
                            'needCache': true,
                            'addZeroRows': true,
                            'withColumnMeta': false,
                            'topCount': 0,
                        },
                    },
                    'yAxisMap': {
                        'account': [
                            {
                                'name': 'cost',
                                'type': 'value',
                            },
                            {
                                'name': 'click',
                                'type': 'value',
                            },
                            {
                                'name': 'impression',
                                'type': 'value',
                            },
                            {
                                'name': 'ocpcTargetTrans',
                                'type': 'value',
                            },
                            {
                                'name': 'ctr',
                                'type': 'ratio',
                            },
                            {
                                'name': 'ocpcTargetTransRatio',
                                'type': 'ratio',
                            },
                            {
                                'name': 'ocpcTargetTransCPC',
                                'type': 'value',
                            },
                        ],
                    },
                    'highlightColumnsMap': {
                        'account': [
                            'cost',
                        ],
                    },
                    'showDownload': true,
                    'optionType': 'tab',
                },
                'config': {
                    'alertType': 'white',
                    'classNameList': null,
                },
            },
            {
                'subType': 160,
                'payload': {
                    'distribution': 'space-between',
                },
                'config': null,
            },
            {
                'subType': 116,
                'payload': {
                    'distribution': 'space-between',
                    'content': '<strong>投放表现：</strong>您的账户近7日日均消费500.0元，相比于前7日日均消费基本持平，建议继续保持或加大投放。',
                },
                'config': {
                    'alertType': 'normal',
                    'classNameList': null,
                },
            },
            {
                'subType': 160,
                'payload': {
                    'distribution': 'space-between',
                },
                'config': null,
            },
            {
                'subType': 116,
                'payload': {
                    'distribution': 'space-between',
                    'content': '<alert><strong>一键试投投放表现：</strong><span>使用一键试投功能后的账户整体（通过一键试投功能创建的轻舸营销方案和被参照的搜索平台的项目/计划）转化量为20.0，相比于试投前一周原搜索平台被参照的项目/计划的转化量提升100.0%（10.0->20.0）；通过一键试投功能创建的营销方案一周内相较于同期搜索平台被参照的项目/计划，点击率提升20.0%（0.17%->0.2%），转化量提升50.0% （10.0->15.0），转化率提升25.0%（0.2%->0.25%），转化成本下降50.0%（10.0元->6.67元）<sug prompt=\'查看一键试投方案效果\'  status=\'reusable\'></sug></span></alert>',
                },
                'config': {
                    'alertType': 'normal',
                    'classNameList': null,
                },
            },
        ],
        'failInfo': false,
        'guiToken': '查看账户报告',
    },
};

const StackedLineRowsCard = {
    'type': 167,
    'payload': {
        'config': {
            'reportScene': 'crowd',
        },
        'reportParamsMap': { // Map<>
            account: {
                'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
                'reportType': 1900001,
                'userIds': [
                    ********,
                ],
                'startDate': '2024-01-18',
                'endDate': '2024-01-24',
                'timeUnit': 'DAY',
                'columns': [
                    'cost',
                    'click',
                    'impression',
                    'ocpcTargetTrans',
                    'ctr',
                    'ocpcTargetTransRatio',
                    'ocpcTargetTransCPC',
                ],
                'sorts': null,
                'filters': [],
                'startRow': 0,
                'rowCount': 1000,
                'needSum': true,
                'splitColumn': null,
                'compareStartDate': null,
                'needCache': true,
                'addZeroRows': true,
                'withColumnMeta': false,
            },
            crowd: {

                'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
                'reportType': 1900001,
                'userIds': [
                    ********,
                ],
                'startDate': '2024-01-18',
                'endDate': '2024-01-24',
                'timeUnit': 'DAY',
                'columns': [
                    'cost',
                    'click',
                    'impression',
                    'ocpcTargetTrans',
                    'ctr',
                    'ocpcTargetTransRatio',
                    'ocpcTargetTransCPC',
                ],
                'sorts': null,
                'filters': [{}],
                'startRow': 0,
                'rowCount': 1000,
                'needSum': true,
                'splitColumn': null,
                'compareStartDate': null,
                'needCache': true,
                'addZeroRows': true,
                'withColumnMeta': false,
            },
        },
        'options': [
            {
                'value': 'cost',
                'label': '指标：消费',
            },
            {
                'value': 'click',
                'label': '指标：点击量',
            },
            {
                'value': 'ocpcTargetTrans',
                'label': '指标：转化量',
            },
            {
                'value': 'ctr',
                'label': '指标：点击率',
            },
            {
                'value': 'ocpcTargetTransRatio',
                'label': '指标：目标转化率',
            },
            {
                'value': 'ocpcTargetTransCPC',
                'label': '指标：目标转化成本',
            },
        ],
        'level': 'account',
        'distribution': 'space-between',
        'defaultOption': 'cost',
        'optionType': 'select',
        // 'fieldSet': 'campaignNameStatus',
        'title': '消费下降',
    },
    'config': {
        'alertType': 'white',
        'classNameList': null,
    },
};


const textCard = {
    type: 150,
    payload: {
        cardParameters: {
            text: '<alert>已为您诊断账户消费波动情况，账户昨日消费379.74元。环比<Mark status="success">下降77.66%</Mark>（<Mark status="success">下降1320.43元</Mark>)。账户设置<Mark>未发现阻断风险</Mark>。已为您罗列存在消费突降且影响最大的前2个项目。「炎症240315」环比<Mark status="success">下降 91.04%</Mark> (<Mark status="success">下降1238.07元</Mark>)，「人流240315」环比<Mark status="success">下降 24.21%</Mark> (<Mark status="success">下降82.36元</Mark>)。<br>您想要诊断哪个项目？</alert>',
        },
    },
};

const removedMarketPoint = {
    type: 174,
    payload: {
        'cardParameters': {
            defaultOption: 'week',
        },
        field: 'marketPoint',
    },
};

const Protocol = {
    type: 189,
    payload: {
    },
};

const marketPoint = {
    type: 155,
    payload: {
        'cardParameters': {
            'keywordInfos': [
                {
                    'keywordId': 96346790,
                    'keyword': '营销要点1a/bbb',
                    'click': 124,
                    'impression': 124,
                    'cost': 124,
                    'ocpcTargetTrans': 234,
                    'deepOcpcTargetTrans': 239,
                    'flowLevel': 1,
                    'auditStatus': 0,
                    permanent: 1,
                },
                {
                    'keywordId': 96346791,
                    'keyword': '营销要点2?',
                    'click': 124,
                    'impression': 124,
                    'cost': 124,
                    'ocpcTargetTrans': 234,
                    'deepOcpcTargetTrans': 238,
                    'flowLevel': 2,
                    'auditStatus': 0,
                    permanent: 1,
                },
                {
                    'keywordId': 96346792,
                    'keyword': '营销要点3',
                    'click': 124,
                    'impression': 124,
                    'cost': 124,
                    'ocpcTargetTrans': 234,
                    'deepOcpcTargetTrans': 238,
                    'flowLevel': 3,
                    'auditStatus': 0,
                    permanent: 1,
                },
                {
                    'keywordId': 96346793,
                    'keyword': '营销要点4',
                    'click': 124,
                    'impression': 124,
                    'cost': 124,
                    'ocpcTargetTrans': 234,
                    'deepOcpcTargetTrans': 238,
                    'flowLevel': 1,
                    'auditStatus': 0,
                    permanent: 1,
                },
                {
                    'keywordId': 96346794,
                    'keyword': '营销要点5',
                    'click': 124,
                    'impression': 124,
                    'cost': 124,
                    'ocpcTargetTrans': 234,
                    'deepOcpcTargetTrans': 238,
                    'flowLevel': 1,
                    permanent: 0,
                },
                {
                    'keywordId': 96346795,
                    'keyword': '营销要点6',
                    'click': 124,
                    'impression': 124,
                    'cost': 124,
                    'ocpcTargetTrans': 234,
                    'deepOcpcTargetTrans': 238,
                    'flowLevel': 1,
                    permanent: 0,
                },
                {
                    'keywordId': 96346796,
                    'keyword': '营销要点7',
                    'click': 124,
                    'impression': 124,
                    'cost': 124,
                    'ocpcTargetTrans': 234,
                    'deepOcpcTargetTrans': 238,
                    'flowLevel': 1,
                    permanent: 0,
                },
                {
                    'keywordId': 96346797,
                    'keyword': '营销要点8',
                    'click': 124,
                    'impression': 124,
                    'cost': 124,
                    'ocpcTargetTrans': 234,
                    'deepOcpcTargetTrans': 238,
                    'flowLevel': 1,
                    'auditStatus': 0,
                    permanent: 0,
                },
                {
                    'keywordId': 96346798,
                    'keyword': '营销要点9',
                    'click': 124,
                    'impression': 124,
                    'cost': 124,
                    'ocpcTargetTrans': 234,
                    'deepOcpcTargetTrans': 238,
                    'flowLevel': 1,
                    'auditStatus': 0,
                    permanent: 0,
                },
            ],
            expectBusinessSummary: '营销要点811111营销要点1a/bbbb222222营销要点2?333333',
        },
        field: 'marketPoint',
    },
};

const CrowdTagRecommend = {
    type: 149, // 推荐人群标签（crowdTag） && 人群差异化出价的推荐和修改（crowdPriceRatio）（3个）
    payload: {
        currentField: 'crowdPriceRatio', // crowdTag
        cardParameters: {
            crowdTags: [{
                'idmpId': 123456,
                'crowdName': '医美-高转化价值人群',
                'crowdDesc': '经常到访轻医美、整形美容机构的人群,和您所描述整体接近',
                'crowdPriceRatio': 1.8,
                'use': true,
            }, {
                'idmpId': 1234569,
                'crowdName': '年轻人群',
                'crowdDesc': '年纪18-25岁的人群',
                'crowdPriceRatio': 2,
                'use': true,
            }, {
                'idmpId': 19,
                'crowdName': '青少年人群',
                'crowdDesc': '经常到访学习机构的年轻人',
            }, {
                'idmpId': 18,
                'crowdName': '对汽车感兴趣人群',
                'crowdDesc': '对汽车感兴趣，时尚的人',
            }],
        },
        maxCount: 3,
    },
};

const CrowdTagRecommendEdit = {
    type: 151,
    payload: {
        currentField: 'crowdTag',
        cardParameters: {
            crowdTags: [{
                'idmpId': 123456,
                'crowdName': '医美-高转化价值人群',
            }, {
                'idmpId': 1234569,
                'crowdName': '年轻人群',
            }],
        },
    },
};
const commonLineChart = {
    type: 147,
    payload: {
        // 'reportParams': {
        //     'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
        //     'reportType': 1900001,
        //     'userIds': null,
        //     'startDate': '2024-01-18',
        //     'endDate': '2024-01-24',
        //     'timeUnit': 'DAY',
        //     'columns': [
        //         'date',
        //         'impression',
        //         'click',
        //         'cost',
        //         'ctr',
        //         'cpc',
        //         'cpm',
        //         'topPageViews',
        //         'topFirstPageViews',
        //         'topPClicks',
        //         'topPay',
        //         'topPvWinA',
        //         'topFirstPvWinA',
        //         'ocpcTargetTrans',
        //         'ocpcTargetTransCPC',
        //         'ocpcTargetTransRatio',
        //         'deepConversions',
        //         'deepConversionsCost',
        //         'deepConversionsCVR',
        //     ],
        //     'sorts': null,
        //     'filters': null,
        //     'startRow': 0,
        //     'rowCount': 50,
        //     'needSum': true,
        //     'splitColumn': null,
        //     'compareStartDate': null,
        //     'needCache': true,
        //     'addZeroRows': true,
        //     'withColumnMeta': false,
        // },
        // 'yAxis': [
        //     {
        //         'name': 'impression',
        //         'type': 'value',
        //     },
        //     {
        //         'name': 'click',
        //         'type': 'value',
        //     },
        //     {
        //         'name': 'cost',
        //         'type': 'value',
        //     },
        //     {
        //         'name': 'ctr',
        //         'type': 'ratio',
        //     },
        //     {
        //         'name': 'cpc',
        //         'type': 'value',
        //     },
        //     {
        //         'name': 'cpm',
        //         'type': 'value',
        //     },
        //     {
        //         'name': 'ocpcTargetTrans',
        //         'type': 'value',
        //     },
        //     {
        //         'name': 'ocpcTargetTransCPC',
        //         'type': 'value',
        //     },
        //     {
        //         'name': 'ocpcTargetTransRatio',
        //         'type': 'ratio',
        //     },
        //     {
        //         'name': 'deepConversions',
        //         'type': 'value',
        //     },
        //     {
        //         'name': 'deepConversionsCost',
        //         'type': 'value',
        //     },
        //     {
        //         'name': 'deepConversionsCVR',
        //         'type': 'ratio',
        //     },
        // ],
        // 'xAxis': [
        //     'date',
        // ],
        // 'level': 'account',
        // 'distribution': 'space-between',
        // 'campaignReportParams': {
        //     'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
        //     'reportType': 1900001,
        //     'userIds': [
        //         ********,
        //     ],
        //     'startDate': '2024-01-18',
        //     'endDate': '2024-01-24',
        //     'timeUnit': 'SUMMARY',
        //     'columns': [
        //         'date',
        //         'campaignNameStatus',
        //         'impression',
        //         'click',
        //         'cost',
        //         'ctr',
        //         'cpc',
        //         'cpm',
        //         'topPageViews',
        //         'topFirstPageViews',
        //         'topPClicks',
        //         'topPay',
        //         'topPvWinA',
        //         'topFirstPvWinA',
        //         'ocpcTransType',
        //         'ocpcTargetTrans',
        //         'ocpcTargetTransCPC',
        //         'ocpcTargetTransRatio',
        //         'deepConversions',
        //         'deepConversionsCost',
        //         'deepConversionsCVR',
        //     ],
        //     'sorts': null,
        //     'filters': [{'column':'productLine','operator':'IN','values':[2]}],
        //     'startRow': 0,
        //     'rowCount': 1000,
        //     'needSum': true,
        //     'splitColumn': null,
        //     'compareStartDate': null,
        //     'needCache': true,
        //     'addZeroRows': false,
        //     'withColumnMeta': false,
        // },
        // 'highlightColumns': [],
        // 'failInfo': false,
        'reportParamsMap': {
            'fc': {
                'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
                'reportType': 1900007,
                'userIds': null,
                'startDate': '2024-01-17',
                'endDate': '2024-01-23',
                'timeUnit': 'SUMMARY',
                'columns': [
                    'date',
                    'queryWord',
                    'impression',
                    'click',
                    'cost',
                    'ctr',
                    'cpc',
                    'cpm',
                ],
                'sorts': [
                    {
                        'column': 'cost',
                        'sortRule': 'DESC',
                        'compareField': null,
                    },
                ],
                'filters': [
                    {
                        'column': 'impression',
                        'operator': 'GT',
                        'values': [
                            '300',
                        ],
                        'compareField': null,
                    },
                    {
                        'column': 'ctr',
                        'operator': 'LT',
                        'values': [
                            '0.12',
                        ],
                        'compareField': null,
                    },
                ],
                'startRow': 0,
                'rowCount': 5,
                topCount: 21,
                'needSum': true,
                'splitColumn': null,
                'compareStartDate': null,
                'needCache': true,
                'addZeroRows': false,
                'withColumnMeta': false,
            },
            'feed': {
                'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
                'reportType': 1900009,
                'userIds': null,
                'startDate': '2024-01-17',
                'endDate': '2024-01-24',
                'timeUnit': 'SUMMARY',
                'columns': [
                    'date',
                    'queryWord',
                    'impression',
                    'click',
                    'cost',
                    'ctr',
                    'cpc',
                    'cpm',
                ],
                'sorts': [
                    {
                        'column': 'cost1',
                        'sortRule': 'DESC',
                        'compareField': null,
                    },
                ],
                'filters': [
                    {
                        'column': 'impression',
                        'operator': 'GT',
                        'values': [
                            '300',
                        ],
                        'compareField': null,
                    },
                    {
                        'column': 'ctr',
                        'operator': 'LT',
                        'values': [
                            '0.12',
                        ],
                        'compareField': null,
                    },
                ],
                'startRow': 0,
                'rowCount': 5,
                topCount: 21,
                'needSum': true,
                'splitColumn': null,
                'compareStartDate': null,
                'needCache': true,
                'addZeroRows': false,
                'withColumnMeta': false,
            },
        },
        aixProduct: [2],

        guiToken: '营销要点报告： top1',
        title: '营销要点报告： top1',
    },
};

const guiTable = {
    type: 162,
    payload: {
        'reportParamsMap': {
            'IP_CLICKS_REALTIME': {
                'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
                'reportType': 1900028,
                'userIds': null,
                'startDate': '2024-05-23',
                'endDate': '2024-05-29',
                'timeUnit': 'DAY',
                'columns': [
                    'date',
                    'dateTimeSec',
                    'formatIP',
                    'sumClicks',
                    'sumCost',
                    'freeClicks',
                    'freeCost',
                    'validClicks',
                    'validCost',
                    'wInfoNameStatus',
                ],
                'sorts': [
                    {
                        'column': 'date',
                        'sortRule': 'DESC',
                        'compareField': null,
                    },
                ],
                'filters': null,
                'startRow': 0,
                'rowCount': 50,
                'needSum': true,
                'splitColumn': null,
                'compareStartDate': null,
                'needCache': true,
                'addZeroRows': false,
                'withColumnMeta': false,
                'topCount': 0,
            },
            'IP_CLICKS': {
                'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
                'reportType': 1900027,
                'userIds': null,
                'startDate': '2024-05-23',
                'endDate': '2024-05-29',
                'timeUnit': 'DAY',
                'columns': [
                    'date',
                    'formatIP',
                    'sumClicks',
                    'sumCost',
                    'freeClicks',
                    'freeCost',
                    'validClicks',
                    'validCost',
                    'wInfoNameStatus',
                    'aixOcpcConversionsDetail1',
                ],
                'sorts': [
                    {
                        'column': 'date',
                        'sortRule': 'DESC',
                        'compareField': null,
                    },
                ],
                'filters': null,
                'startRow': 0,
                'rowCount': 50,
                'needSum': true,
                'splitColumn': null,
                'compareStartDate': null,
                'needCache': true,
                'addZeroRows': false,
                'withColumnMeta': false,
                'topCount': 0,
            },
        },
        // {
        //     'wInfoNameStatus': {
        //         'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
        //         'reportType': 1900027,
        //         'userIds': null,
        //         'dateRange': {
        //             'startDate': '2024-01-17',
        //             'endDate': '2024-01-23',
        //         },
        //         'timeUnit': 'SUMMARY',
        //         'columns': [
        //             'date',
        //             'queryWord',
        //             'impression',
        //             'click',
        //             'cost',
        //             'ctr',
        //             'cpc',
        //             'cpm',
        //         ],
        //         'sorts': [
        //             {
        //                 'column': 'cost',
        //                 'sortRule': 'DESC',
        //                 'compareField': null,
        //             },
        //         ],
        //         'filters': [
        //             {
        //                 'column': 'impression',
        //                 'operator': 'GT',
        //                 'values': [
        //                     '300',
        //                 ],
        //                 'compareField': null,
        //             },
        //             {
        //                 'column': 'ctr',
        //                 'operator': 'LT',
        //                 'values': [
        //                     '0.12',
        //                 ],
        //                 'compareField': null,
        //             },
        //         ],
        //         'startRow': 0,
        //         'rowCount': 5,
        //         topCount: 21,
        //         'needSum': true,
        //         'splitColumn': null,
        //         'compareStartDate': null,
        //         'needCache': true,
        //         'addZeroRows': false,
        //         'withColumnMeta': false,
        //     },
        //     'click': {
        //         'token': '409f4eaa-ad19-464f-ac33-ab8b480asdds',
        //         'reportType': 1900007,
        //         'userIds': null,
        //         'startDate': '2024-01-17',
        //         'endDate': '2024-01-23',
        //         'timeUnit': 'SUMMARY',
        //         'columns': [
        //             'date',
        //             'queryWord',
        //             'impression',
        //             'click',
        //             'cost',
        //             'ctr',
        //             'cpc',
        //             'cpm',
        //         ],
        //         'sorts': [
        //             {
        //                 'column': 'cost',
        //                 'sortRule': 'DESC',
        //                 'compareField': null,
        //             },
        //         ],
        //         'filters': [
        //             {
        //                 'column': 'impression',
        //                 'operator': 'GT',
        //                 'values': [
        //                     '300',
        //                 ],
        //                 'compareField': null,
        //             },
        //             {
        //                 'column': 'ctr',
        //                 'operator': 'LT',
        //                 'values': [
        //                     '0.12',
        //                 ],
        //                 'compareField': null,
        //             },
        //         ],
        //         'startRow': 0,
        //         'rowCount': 5,
        //         topCount: 21,
        //         'needSum': true,
        //         'splitColumn': null,
        //         'compareStartDate': null,
        //         'needCache': true,
        //         'addZeroRows': false,
        //         'withColumnMeta': false,
        //     },
        // },
        'defaultOption': 'IP_CLICKS',
        'options': [
            {
                'label': '计费IP查询',
                'value': 'IP_CLICKS',
            },
            {
                'label': '计费实时IP查询',
                'value': 'IP_CLICKS_REALTIME',
            },
        ],
        // [
        //     {
        //         'label': '营销要点实时消费',
        //         'value': 'wInfoNameStatus',
        //     },
        //     {
        //         'label': '营销方案实时消费',
        //         'value': 'click',
        //     },
        // ],
        'filterProps': {},
    },
};

const guiFilterArea = {
    type: 165,
    payload: {
        'distribution': 'space-between',
        guiReportParams: {
            'startDate': '2024-01-15',
            'endDate': '2024-01-21',
            'timeUnit': 'SUMMARY',
            'campaignId': null,
            reportType: 1900009,
            aixProduct: [2],
        },
    },
};

const assembleReport = {
    type: 190,
    payload: {
        reportParamsMap: {
            account: {
                reportType: 123,
            },
            feed: {
                reportType: 124,
            },
            fc: {
                reportType: 125,
            },
            aix: {
                reportType: 126,
            },
            service: {
                reportType: 127,
            },
            clue: {
                reportType: 128,
            },
        },
    },
};

const springActivity = {
    type: 169,
    payload: {},
};

const anchor = {
    type: 175,
    payload: {},
};

const anchorItem = {
    type: 176,
    payload: {
        options: [
            {
                anchorId: 12345,
                anchorName: '主播111',
                anchorLogo: 'https://fc-ccimage.baidu.com/0/pic/-722769115_-1943136793_606452364.jpg',
                broadCastMode: 3,
                anchorStatus: 0,
                rejectReason: '主播信息审核未通过',
            },
        ],
    },
};

const cdpBot = {
    type: 166,
    payload: {
        currentField: 'aia',
    },
};
const cbpAgent = {
    type: 171,
    payload: {
        field: 'shopAgent',
        bcpAgentInfo: [{// 暂时只有一个
            bcpAgentHeadImage: 'https://picsum.photos/400/400', // 头像
            bcpAgentName: '智能体1', // 标题
            url: 'www.baidu.com', // 落地页
            isRefuse: true, // 是否拒绝
            wholeReason: '不符合规范', // 拒绝原因
        }],
    },
};
const marketGuide = {
    type: 184,
    payload: {
        aixProduct: [2],
    },
};

const campaignSelectorCard = {
    type: 168,
    payload: {
        cardParameters: {
            campaignParams: {},
            isMultiple: true,
            buttonList: [
                {
                    promptTemplate: '11已选择${campaignNames}XXXXXX',
                    buttonText: '开启14日',
                    completeText: '已确认开启14日',
                },
                {
                    promptTemplate: '22已选择${campaignNames}XXXXXX',
                    buttonText: '开启7日',
                    completeText: '已确认开启7日',
                },
            ],
        },
        field: 'relievedMarketing',
    },
};


const MigrationSelectorCard = {
    type: 172,
    payload: {
        cardParameters: {
            options: [{
                'migrateDataId': stone.id, // 迁移对象id
                'migrateDataName': '计划1', // 迁移对象名称
                'migrateDataType': stone.enum([0, 1, 3]), // 迁移对象类型, 0-出价策略, 1-计划，3-项目
                'transTypes': [3, 30, 1, 2, 3, 4, 5, 6, 7], // 目标转化类型
                'deepTransTypes': [72], // 深度目标转化类型
                'bindCampaignIds': [123, 456], // 绑定的计划id，仅出价策略和项目才有
                'bindCampaignNames': [stone.randomStr, stone.randomStr], // 绑定的计划名称，仅出价策略和项目才有
                'marketingTargetId': 0, // 轻舸营销目标, 0-销售线索
                'cost': 123.022222, // 消费
            }, {
                'migrateDataId': stone.id, // 迁移对象id
                'migrateDataName': '计划1', // 迁移对象名称
                'migrateDataType': stone.enum([0, 1, 3]), // 迁移对象类型, 0-出价策略, 1-计划，3-项目
                'transTypes': [3, 30], // 目标转化类型
                'deepTransTypes': [72], // 深度目标转化类型
                'bindCampaignIds': [123, 456], // 绑定的计划id，仅出价策略和项目才有
                'bindCampaignNames': ['计划2计划2计划2计划2计划2', '计划2计划2计划2计划2计划2计划2', '计划2计划2计划2计划2计划2计划2'], // 绑定的计划名称，仅出价策略和项目才有
                'marketingTargetId': 0, // 轻舸营销目标, 0-销售线索
                'cost': 123.022222, // 消费
            }, {
                'migrateDataId': stone.id, // 迁移对象id
                'migrateDataName': stone.randomStr, // 迁移对象名称
                'migrateDataType': stone.enum([0, 1, 3]), // 迁移对象类型, 0-出价策略, 1-计划，3-项目
                'transTypes': [3, 30], // 目标转化类型
                'deepTransTypes': [72], // 深度目标转化类型
                'bindCampaignIds': [123, 456], // 绑定的计划id，仅出价策略和项目才有
                'bindCampaignNames': ['计划2计划2计划2计划2计划2', '计划2计划2计划2计划2计划2计划2', '计划2计划2计划2计划2计划2计划2'], // 绑定的计划名称，仅出价策略和项目才有
                'marketingTargetId': 0, // 轻舸营销目标, 0-销售线索
                'cost': 123.022222, // 消费
            }, {
                'migrateDataId': stone.id, // 迁移对象id
                'migrateDataName': '计划1', // 迁移对象名称
                'migrateDataType': stone.enum([0, 1, 3]), // 迁移对象类型, 0-出价策略, 1-计划，3-项目
                'transTypes': [3, 30], // 目标转化类型
                'deepTransTypes': [72], // 深度目标转化类型
                'bindCampaignIds': [123, 456], // 绑定的计划id，仅出价策略和项目才有
                'bindCampaignNames': ['计划1', '计划2'], // 绑定的计划名称，仅出价策略和项目才有
                'marketingTargetId': 0, // 轻舸营销目标, 0-销售线索
                'cost': 123.022222, // 消费
            }],
            promptTemplate: '已选择${migrateDataName}${migrationTypeName}XXXXXX',
            initialTab: 'project',
        },
        field: 'feed-migration',
    },
};

const liftBudgetCard = {
    type: 180,
    payload: {
        liftBudgetMode: [
            {
                liftBudgetModeName: 'immediate',
            },
            {liftBudgetModeName: 'appoint'},
            {liftBudgetModeName: 'appointWeekly'},
        ],
        distribution: 'space-between',
        failInfo: false,
        field: 'liftBudget',
    },
};

const customRenderCard = {
    type: 181,
    payload: {
        config: {
            key: 'InvalidClickDashboard',
            reportType: 1900028,
        },
    },
};

const clueValidityOptimizeCard = {
    type: 187,
    payload: {
        field: 'effectGuarantee',
        fieldOperate: 'MOD',
        distribution: 'space-between',
        cardParameters: {
            type: 'refinement',
            showProject: true,
            // options: [
            //     {
            //         'type': 'project',
            //         'projectId': 1,
            //         'projectName': '项目项目',
            //         'marketingTargetId': 1,
            //         'transTypes': [1, 2, 3],
            //         'ocpcBid': 1.0,
            //         'bindCampaigns': 1,
            //         'crowdQualityOcpcBidRatio': [
            //             {
            //                 quality: 1,
            //                 ratio: 0.9,
            //             },
            //         ],
            //     },
            //     {
            //         'type': 'project',
            //         'projectId': 2,
            //         'projectName': '项目项目项目项目',
            //         'marketingTargetId': 1,
            //         'transTypes': [1, 2, 3],
            //         'ocpcBid': 0.2,
            //         'bindCampaigns': 1,
            //         'crowdQualityOcpcBidRatio': [],
            //     },
            //     {
            //         'type': 'project',
            //         'projectId': 3,
            //         'projectName': '项目678',
            //         'marketingTargetId': 1,
            //         'transTypes': [1, 2, 3],
            //         'ocpcBid': 0.2,
            //         'bindCampaigns': 1,
            //         'crowdQualityOcpcBidRatio': [],
            //     },
            //     {
            //         'type': 'project',
            //         'projectId': 4,
            //         'projectName': '项目项目',
            //         'marketingTargetId': 1,
            //         'transTypes': [1, 2, 3],
            //         'ocpcBid': 1.0,
            //         'bindCampaigns': 1,
            //         'crowdQualityOcpcBidRatio': [
            //             {
            //                 quality: 1,
            //                 ratio: 0.9,
            //             },
            //         ],
            //     },
            //     {
            //         'type': 'project',
            //         'projectId': 5,
            //         'projectName': '项目项目项目项目',
            //         'marketingTargetId': 1,
            //         'transTypes': [1, 2, 3],
            //         'ocpcBid': 0.2,
            //         'bindCampaigns': 1,
            //         'crowdQualityOcpcBidRatio': [],
            //     },
            //     {
            //         'type': 'project',
            //         'projectId': 6,
            //         'projectName': '项目678',
            //         'marketingTargetId': 1,
            //         'transTypes': [1, 2, 3],
            //         'ocpcBid': 0.2,
            //         'bindCampaigns': 1,
            //         'crowdQualityOcpcBidRatio': [],
            //     },
            //     {
            //         'type': 'project',
            //         'projectId': 7,
            //         'projectName': '项目678',
            //         'marketingTargetId': 1,
            //         'transTypes': [1, 2, 3],
            //         'ocpcBid': 0.2,
            //         'bindCampaigns': 1,
            //         'crowdQualityOcpcBidRatio': [],
            //     },
            // ],
            // options: [
            //     {
            //         "type": "project",
            //         "projectId": 569502521,
            //         "projectName": "项目项目-1720670020918",
            //         "transTypes": [
            //             3,
            //             18,
            //             30,
            //         ],
            //         "ocpcBid": 99,
            //         "transFrom": 1000,
            //         "deepTransTypes": [76],
            //         "ocpcDeepBid": 9,
            //         "supportChangeDeepCtList": [
            //             10,
            //             56,
            //             73,
            //             74,
            //             75,
            //             76,
            //             77,
            //         ],
            //     },
            //     {
            //         "type": "project",
            //         "projectId": 5602521,
            //         "projectName": "项目项目-1720670020918",
            //         "transTypes": [
            //             3,
            //             18,
            //             30,
            //         ],
            //         "ocpcBid": 99,
            //         "transFrom": 1000,
            //         "deepTransTypes": [77],
            //         "ocpcDeepBid": 9,
            //         "supportChangeDeepCtList": [
            //             10,
            //             56,
            //             73,
            //             74,
            //             75,
            //             76,
            //             77,
            //         ],
            //     },
            //     {
            //         "type": "project",
            //         "projectId": 5695021,
            //         "projectName": "项目项目kkkk-1720670020918",
            //         "transTypes": [
            //             3,
            //             18,
            //             30,
            //         ],
            //         "ocpcnBid": 66,
            //         "transFrom": 1000,
            //         "deepTransTypes": [
            //             76,
            //         ],
            //         "ocpcnDeepBid": 0,
            //         "supportChangeDeepCtList": [
            //             10,
            //             56,
            //             73,
            //             74,
            //             75,
            //             76,
            //             77,
            //         ],
            //     },
            //     {
            //         "type": "campaign",
            //         'supportChangeDeepCtList': [1, 2, 3],
            //         'campaignId': 518540598,
            //         'campaignName': '营销方案-1713939949584',
            //         'business': {
            //             'urls': [
            //                 'https://ada.baidu.com/imlp/xyl?imid=6ffa4d6b6781bc2cf9bf16e921030634#back1692616484907',
            //             ],
            //             'businessOpenExpression': {
            //                 'expectBusinessSummary': '我要推广的是一款线上教学的书法培训课程。主要的核心业务包含了少儿书法、成人书法、硬笔书法等多种定制化课程，涵盖了基本用笔方法、各类书体讲解、字体笔画规律等教学内容。暂不提供。中国人民解放军总医院，提供全面的医疗服务和健康保障。和我业务相关的用户需求热点是：线上教学书法培训课程12334465652342423242',
            //             },
            //             'brandId': 0,
            //             'brandInfo': {
            //                 'brandId': 0,
            //                 'brandName': '百度时代网络技术（北京）有限公司',
            //                 'isDefault': false,
            //                 'isCompany': false,
            //                 'id': 0,
            //             },
            //             'deeplink': '',
            //             'ulinkUrl': '',
            //             'ulinkScheme': '',
            //             'inferenceMode': 2,
            //             'anchorId': 0,
            //             'broadCastMode': 0,
            //         },
            //         'targeting': {
            //             'targetingOpenExpression': {
            //                 'expectCrowdSummary': '不限',
            //                 'negativeCrowdSummary': '收入4000以下男性人群、在校学生人群、持有小米手机的人、低消费人士、使用小米手机人群、1500元以下手机、关注1000元以下手机人群、关注1000-2000元手机人群、1500-2499元手机、2500-3499元手机,高中及以下学历人群',
            //             },
            //             'equipmentType': 2,
            //             'regionIds': [
            //                 1000,
            //                 2000,
            //                 3000,
            //                 4000,
            //                 5000,
            //                 200000,
            //                 8000,
            //                 9000,
            //                 10000,
            //                 11000,
            //                 12000,
            //                 13000,
            //                 14000,
            //                 15000,
            //                 16000,
            //                 17000,
            //                 18000,
            //                 19000,
            //                 20000,
            //                 21000,
            //                 22000,
            //                 23000,
            //                 24000,
            //                 25000,
            //                 26000,
            //                 27000,
            //                 28000,
            //                 29000,
            //                 30000,
            //                 31000,
            //                 32000,
            //                 33000,
            //                 34000,
            //                 35000,
            //                 36000,
            //                 300000,
            //             ],
            //             'expectSex': 0,
            //             'negativePackets': [
            //                 {
            //                     'bindId': 15126724282,
            //                     'negativePacketId': 710440,
            //                     'negativePacketName': '否定关键词包-1',
            //                     'negativeKeywords': [
            //                         {
            //                             'negativeKeywordId': 14546467029,
            //                             'negativeKeyword': 'fwgr',
            //                             'negativeMatchType': 0,
            //                             'wordId': 7103579487,
            //                             'negativeKeywordSign': 8243688922895352000,
            //                             'negativePacketId': 710440,
            //                         },
            //                         {
            //                             'negativeKeywordId': 14546467030,
            //                             'negativeKeyword': 'asfbsfh',
            //                             'negativeMatchType': 0,
            //                             'wordId': 21760638943,
            //                             'negativeKeywordSign': 7090481523797289000,
            //                             'negativePacketId': 710440,
            //                         },
            //                     ],
            //                     'campaignId': 518540598,
            //                     'id': 710440,
            //                 },
            //                 {
            //                     'bindId': 15126724283,
            //                     'negativePacketId': 710442,
            //                     'negativePacketName': '否定关键词包-2',
            //                     'negativeKeywords': [
            //                         {
            //                             'negativeKeywordId': 14546476083,
            //                             'negativeKeyword': 'sdfabhdfh',
            //                             'negativeMatchType': 0,
            //                             'wordId': 21760667136,
            //                             'negativeKeywordSign': 370748529502839700,
            //                             'negativePacketId': 710442,
            //                         },
            //                         {
            //                             'negativeKeywordId': 14546476084,
            //                             'negativeKeyword': 'adfnsdgnj',
            //                             'negativeMatchType': 0,
            //                             'wordId': 21760667135,
            //                             'negativeKeywordSign': 405422805584336500,
            //                             'negativePacketId': 710442,
            //                         },
            //                     ],
            //                     'campaignId': 518540598,
            //                     'id': 710442,
            //                 },
            //             ],
            //             'fcGeoLocation': 0,
            //             'feedGeoLocation': 1,
            //             'regionTargetType': 0,
            //         },
            //         'constraint': {
            //             'campaignBidType': 1,
            //             'campaignTransTypes': [
            //                 17,
            //             ],
            //             'campaignDeepTransTypes': [73],
            //             'campaignBid': 100,
            //             'budget': 899,
            //             'budgetPeriodType': 0,
            //             'monitorUrl': '',
            //             'transFrom': 1000,
            //             'campaignCvSource': 1000,
            //             'transId': 0,
            //             'campaignDeepBid': 0,
            //             'budgetContinueDay': 0,
            //             'budgetCycleStatus': false,
            //             'roiRatio': 0,
            //             'retentionRate': 0,
            //             'liftBudgetStatus': 0,
            //         },
            //         'creativeInfo': {
            //             'creativeTexts': [
            //                 {
            //                     'title': '零基础学书法,从入门到精通',
            //                     'description1': '线上教学,多种书体讲解,快速掌握书法技巧.',
            //                     'description2': '',
            //                     'auditStatus': 0,
            //                     'wholeReason': '',
            //                     'addFrom': 18,
            //                     'creativeTextId': 93019880307,
            //                     'md5': 'adbfc6dd4f499c72c6b8178243bf2684',
            //                 },
            //                 {
            //                     'title': '书法课程一站通,轻松提升艺术修养',
            //                     'description1': '线上教学模式,自由选择学习时间,轻松提升书法水平.',
            //                     'description2': '',
            //                     'auditStatus': 0,
            //                     'wholeReason': '',
            //                     'addFrom': 18,
            //                     'creativeTextId': 93019880306,
            //                     'md5': '9c6b129d68f56635e31e89610ddc5c1e',
            //                 },
            //                 {
            //                     'title': '线上书法课,名师亲授技巧',
            //                     'description1': '针对不同水平提供定制化课程,让书法学习更高效.',
            //                     'description2': '',
            //                     'auditStatus': 0,
            //                     'wholeReason': '',
            //                     'addFrom': 18,
            //                     'creativeTextId': 93019880305,
            //                     'md5': 'd5e748c8a7d37970ac004cd3148366c1',
            //                 },
            //             ],
            //             'creativeImages': [
            //                 {
            //                     'imageId': 6786455643,
            //                     'picId': 3391437327197467000,
            //                     'picBosUrl': 'https://fc-ccimage.baidu.com/0/pic/-1285595340_789630535_-845483288.jpg',
            //                     'picMolaUrl': 'https://fc2tn.baidu.com/it/u=789630535,3449484008&fm=203',
            //                     'rawPicUrl': 'https://fc-ccimage.baidu.com/0/pic/-1285595340_789630535_-845483288.jpg',
            //                     'width': 3000,
            //                     'height': 1000,
            //                     'imageRatio': '3:1',
            //                     'auditStatus': 0,
            //                     'wholeReason': '',
            //                     'addFrom': 18,
            //                     'creativeImageId': 93019880309,
            //                     'md5': '8fd9b2dab7cc1feffbd1e1706c9496eb',
            //                 },
            //                 {
            //                     'imageId': 6266603865,
            //                     'picId': -3166552728669224400,
            //                     'picBosUrl': 'https://fc-ccimage.baidu.com/0/pic/904430306_-737270510_86016405.jpg',
            //                     'picMolaUrl': 'https://fc5tn.baidu.com/it/u=3557696786,86016405&fm=203',
            //                     'rawPicUrl': 'https://fc-ccimage.baidu.com/0/pic/904430306_-737270510_86016405.jpg',
            //                     'width': 6144,
            //                     'height': 2048,
            //                     'imageRatio': '3:1',
            //                     'auditStatus': 0,
            //                     'wholeReason': '',
            //                     'addFrom': 18,
            //                     'creativeImageId': 93019880308,
            //                     'md5': '22a314224aa2fa0c6cf840c21bd07e0b',
            //                 },
            //             ],
            //             'creativeVideos': [
            //                 {
            //                     'videoId': 1086657749,
            //                     'videoUrl': 'http://nadvideo2.baidu.com/439fe8e75bff2f47fb35afb3261372cd_1920_1080.mp4',
            //                     'picId': 0,
            //                     'thumbnail': 'https://feed-image.baidu.com/0/pic/-1920691581_-2013856765_1842003215.jpg',
            //                     'width': 1920,
            //                     'height': 1080,
            //                     'duration': 3,
            //                     'auditStatus': 1,
            //                     'addFrom': 20,
            //                     'creativeVideoId': 93033920664,
            //                     'taskId': 5611026,
            //                 },
            //             ],
            //             'creativeGenerateStatus': true,
            //             'creativeTextNumber': 3,
            //             'creativeImageNumber': 2,
            //             'creativeVideoNumber': 1,
            //         },
            //         'flowChannel': 2,
            //         'pause': false,
            //         'status': 21,
            //         'adType': 25,
            //         'addTime': '2024-04-24 14:25:59',
            //         'modTime': '2024-04-25 15:01:23',
            //         'idMappingData': {
            //             'campaignOcpcStrategyId': 3385548204,
            //             'feedUnitId': 9768746861,
            //             'copyFcPlanId': 0,
            //             'copyFeedProjectId': 0,
            //             'copyFeedPlanIds': 0,
            //         },
            //         'addFrom': 0,
            //         'marketingTargetId': 0,
            //         'expectKeywords': {
            //             'status': 1,
            //             'successTime': '2024-04-25 15:01:12',
            //             'keywords': [
            //                 {
            //                     'keywordId': 786905628655,
            //                     'keyword': '硬笔书法培训',
            //                     'keywordTags': [],
            //                     'auditStatus': 0,
            //                     'flowLevel': 1,
            //                 },
            //                 {
            //                     'keywordId': 786905628658,
            //                     'keyword': '成人书法培训',
            //                     'keywordTags': [],
            //                     'auditStatus': 0,
            //                     'flowLevel': 3,
            //                 },
            //                 {
            //                     'keywordId': 786905628659,
            //                     'keyword': '少儿书法培训',
            //                     'keywordTags': [],
            //                     'auditStatus': 0,
            //                     'flowLevel': 3,
            //                 },
            //                 {
            //                     'keywordId': 787706718339,
            //                     'keyword': '线上教学书法培训课程',
            //                     'keywordTags': [
            //                         4,
            //                     ],
            //                     'auditStatus': 0,
            //                     'flowLevel': 2,
            //                 },
            //                 {
            //                     'keywordId': 787827615370,
            //                     'keyword': '中国人民解放军总医院',
            //                     'keywordTags': [
            //                         2,
            //                     ],
            //                     'auditStatus': 2,
            //                     'flowLevel': 3,
            //                 },
            //             ],
            //         },
            //         'flowChannelReverse': [
            //             1,
            //         ],
            //         'budgetStatus': 0,
            //         'carlabel': false,
            //         'id': 518540598,
            //     },
            //     {
            //         "type": "campaign",
            //         'supportChangeDeepCtList': [3, 18, 30],
            //         'campaignId': 518540566,
            //         'campaignName': '营销方案-测试2',
            //         'business': {
            //             'urls': [
            //                 'https://ada.baidu.com/imlp/xyl?imid=6ffa4d6b6781bc2cf9bf16e921030634#back1692616484907',
            //             ],
            //             'businessOpenExpression': {
            //                 'expectBusinessSummary': '我要推广的是一款线上教学的书法培训课程。主要的核心业务包含了少儿书法、成人书法、硬笔书法等多种定制化课程，涵盖了基本用笔方法、各类书体讲解、字体笔画规律等教学内容。暂不提供。中国人民解放军总医院，提供全面的医疗服务和健康保障。和我业务相关的用户需求热点是：线上教学书法培训课程12334465652342423242',
            //             },
            //             'brandId': 0,
            //             'brandInfo': {
            //                 'brandId': 0,
            //                 'brandName': '百度时代网络技术（北京）有限公司',
            //                 'isDefault': false,
            //                 'isCompany': false,
            //                 'id': 0,
            //             },
            //             'deeplink': '',
            //             'ulinkUrl': '',
            //             'ulinkScheme': '',
            //             'inferenceMode': 2,
            //             'anchorId': 0,
            //             'broadCastMode': 0,
            //         },
            //         'targeting': {
            //             'targetingOpenExpression': {
            //                 'expectCrowdSummary': '不限',
            //                 'negativeCrowdSummary': '收入4000以下男性人群、在校学生人群、持有小米手机的人、低消费人士、使用小米手机人群、1500元以下手机、关注1000元以下手机人群、关注1000-2000元手机人群、1500-2499元手机、2500-3499元手机,高中及以下学历人群',
            //             },
            //             'equipmentType': 2,
            //             'regionIds': [
            //                 1000,
            //                 2000,
            //                 3000,
            //                 4000,
            //                 5000,
            //                 200000,
            //                 8000,
            //                 9000,
            //                 10000,
            //                 11000,
            //                 12000,
            //                 13000,
            //                 14000,
            //                 15000,
            //                 16000,
            //                 17000,
            //                 18000,
            //                 19000,
            //                 20000,
            //                 21000,
            //                 22000,
            //                 23000,
            //                 24000,
            //                 25000,
            //                 26000,
            //                 27000,
            //                 28000,
            //                 29000,
            //                 30000,
            //                 31000,
            //                 32000,
            //                 33000,
            //                 34000,
            //                 35000,
            //                 36000,
            //                 300000,
            //             ],
            //             'expectSex': 0,
            //             'negativePackets': [
            //                 {
            //                     'bindId': 15126724282,
            //                     'negativePacketId': 710440,
            //                     'negativePacketName': '否定关键词包-1',
            //                     'negativeKeywords': [
            //                         {
            //                             'negativeKeywordId': 14546467029,
            //                             'negativeKeyword': 'fwgr',
            //                             'negativeMatchType': 0,
            //                             'wordId': 7103579487,
            //                             'negativeKeywordSign': 8243688922895352000,
            //                             'negativePacketId': 710440,
            //                         },
            //                         {
            //                             'negativeKeywordId': 14546467030,
            //                             'negativeKeyword': 'asfbsfh',
            //                             'negativeMatchType': 0,
            //                             'wordId': 21760638943,
            //                             'negativeKeywordSign': 7090481523797289000,
            //                             'negativePacketId': 710440,
            //                         },
            //                     ],
            //                     'campaignId': 518540598,
            //                     'id': 710440,
            //                 },
            //                 {
            //                     'bindId': 15126724283,
            //                     'negativePacketId': 710442,
            //                     'negativePacketName': '否定关键词包-2',
            //                     'negativeKeywords': [
            //                         {
            //                             'negativeKeywordId': 14546476083,
            //                             'negativeKeyword': 'sdfabhdfh',
            //                             'negativeMatchType': 0,
            //                             'wordId': 21760667136,
            //                             'negativeKeywordSign': 370748529502839700,
            //                             'negativePacketId': 710442,
            //                         },
            //                         {
            //                             'negativeKeywordId': 14546476084,
            //                             'negativeKeyword': 'adfnsdgnj',
            //                             'negativeMatchType': 0,
            //                             'wordId': 21760667135,
            //                             'negativeKeywordSign': 405422805584336500,
            //                             'negativePacketId': 710442,
            //                         },
            //                     ],
            //                     'campaignId': 518540598,
            //                     'id': 710442,
            //                 },
            //             ],
            //             'fcGeoLocation': 0,
            //             'feedGeoLocation': 1,
            //             'regionTargetType': 0,
            //         },
            //         'constraint': {
            //             'campaignBidType': 1,
            //             'campaignTransTypes': [
            //                 32,
            //             ],
            //             'campaignDeepTransTypes': [73],
            //             'campaignBid': 300,
            //             'budget': 899,
            //             'budgetPeriodType': 0,
            //             'monitorUrl': '',
            //             'transFrom': 1000,
            //             'campaignCvSource': 1000,
            //             'transId': 0,
            //             'campaignDeepBid': 0,
            //             'budgetContinueDay': 0,
            //             'budgetCycleStatus': false,
            //             'roiRatio': 0,
            //             'retentionRate': 0,
            //             'liftBudgetStatus': 0,
            //         },
            //         'creativeInfo': {
            //             'creativeTexts': [
            //                 {
            //                     'title': '零基础学书法,从入门到精通',
            //                     'description1': '线上教学,多种书体讲解,快速掌握书法技巧.',
            //                     'description2': '',
            //                     'auditStatus': 0,
            //                     'wholeReason': '',
            //                     'addFrom': 18,
            //                     'creativeTextId': 93019880307,
            //                     'md5': 'adbfc6dd4f499c72c6b8178243bf2684',
            //                 },
            //                 {
            //                     'title': '书法课程一站通,轻松提升艺术修养',
            //                     'description1': '线上教学模式,自由选择学习时间,轻松提升书法水平.',
            //                     'description2': '',
            //                     'auditStatus': 0,
            //                     'wholeReason': '',
            //                     'addFrom': 18,
            //                     'creativeTextId': 93019880306,
            //                     'md5': '9c6b129d68f56635e31e89610ddc5c1e',
            //                 },
            //                 {
            //                     'title': '线上书法课,名师亲授技巧',
            //                     'description1': '针对不同水平提供定制化课程,让书法学习更高效.',
            //                     'description2': '',
            //                     'auditStatus': 0,
            //                     'wholeReason': '',
            //                     'addFrom': 18,
            //                     'creativeTextId': 93019880305,
            //                     'md5': 'd5e748c8a7d37970ac004cd3148366c1',
            //                 },
            //             ],
            //             'creativeImages': [
            //                 {
            //                     'imageId': 6786455643,
            //                     'picId': 3391437327197467000,
            //                     'picBosUrl': 'https://fc-ccimage.baidu.com/0/pic/-1285595340_789630535_-845483288.jpg',
            //                     'picMolaUrl': 'https://fc2tn.baidu.com/it/u=789630535,3449484008&fm=203',
            //                     'rawPicUrl': 'https://fc-ccimage.baidu.com/0/pic/-1285595340_789630535_-845483288.jpg',
            //                     'width': 3000,
            //                     'height': 1000,
            //                     'imageRatio': '3:1',
            //                     'auditStatus': 0,
            //                     'wholeReason': '',
            //                     'addFrom': 18,
            //                     'creativeImageId': 93019880309,
            //                     'md5': '8fd9b2dab7cc1feffbd1e1706c9496eb',
            //                 },
            //                 {
            //                     'imageId': 6266603865,
            //                     'picId': -3166552728669224400,
            //                     'picBosUrl': 'https://fc-ccimage.baidu.com/0/pic/904430306_-737270510_86016405.jpg',
            //                     'picMolaUrl': 'https://fc5tn.baidu.com/it/u=3557696786,86016405&fm=203',
            //                     'rawPicUrl': 'https://fc-ccimage.baidu.com/0/pic/904430306_-737270510_86016405.jpg',
            //                     'width': 6144,
            //                     'height': 2048,
            //                     'imageRatio': '3:1',
            //                     'auditStatus': 0,
            //                     'wholeReason': '',
            //                     'addFrom': 18,
            //                     'creativeImageId': 93019880308,
            //                     'md5': '22a314224aa2fa0c6cf840c21bd07e0b',
            //                 },
            //             ],
            //             'creativeVideos': [
            //                 {
            //                     'videoId': 1086657749,
            //                     'videoUrl': 'http://nadvideo2.baidu.com/439fe8e75bff2f47fb35afb3261372cd_1920_1080.mp4',
            //                     'picId': 0,
            //                     'thumbnail': 'https://feed-image.baidu.com/0/pic/-1920691581_-2013856765_1842003215.jpg',
            //                     'width': 1920,
            //                     'height': 1080,
            //                     'duration': 3,
            //                     'auditStatus': 1,
            //                     'addFrom': 20,
            //                     'creativeVideoId': 93033920664,
            //                     'taskId': 5611026,
            //                 },
            //             ],
            //             'creativeGenerateStatus': true,
            //             'creativeTextNumber': 3,
            //             'creativeImageNumber': 2,
            //             'creativeVideoNumber': 1,
            //         },
            //         'flowChannel': 2,
            //         'pause': false,
            //         'status': 21,
            //         'adType': 25,
            //         'addTime': '2024-04-24 14:25:59',
            //         'modTime': '2024-04-25 15:01:23',
            //         'idMappingData': {
            //             'campaignOcpcStrategyId': 3385548204,
            //             'feedUnitId': 9768746861,
            //             'copyFcPlanId': 0,
            //             'copyFeedProjectId': 0,
            //             'copyFeedPlanIds': 0,
            //         },
            //         'addFrom': 0,
            //         'marketingTargetId': 0,
            //         'expectKeywords': {
            //             'status': 1,
            //             'successTime': '2024-04-25 15:01:12',
            //             'keywords': [
            //                 {
            //                     'keywordId': 786905628655,
            //                     'keyword': '硬笔书法培训',
            //                     'keywordTags': [],
            //                     'auditStatus': 0,
            //                     'flowLevel': 1,
            //                 },
            //                 {
            //                     'keywordId': 786905628658,
            //                     'keyword': '成人书法培训',
            //                     'keywordTags': [],
            //                     'auditStatus': 0,
            //                     'flowLevel': 3,
            //                 },
            //                 {
            //                     'keywordId': 786905628659,
            //                     'keyword': '少儿书法培训',
            //                     'keywordTags': [],
            //                     'auditStatus': 0,
            //                     'flowLevel': 3,
            //                 },
            //                 {
            //                     'keywordId': 787706718339,
            //                     'keyword': '线上教学书法培训课程',
            //                     'keywordTags': [
            //                         4,
            //                     ],
            //                     'auditStatus': 0,
            //                     'flowLevel': 2,
            //                 },
            //                 {
            //                     'keywordId': 787827615370,
            //                     'keyword': '中国人民解放军总医院',
            //                     'keywordTags': [
            //                         2,
            //                     ],
            //                     'auditStatus': 2,
            //                     'flowLevel': 3,
            //                 },
            //             ],
            //         },
            //         'flowChannelReverse': [
            //             1,
            //         ],
            //         'budgetStatus': 0,
            //         'carlabel': false,
            //         'id': 518540598,
            //     },
            // ],
        },
    },
};

const clueBusinessPoint = {
    type: 191,
    payload: {
        cardParameters: {
            clueFeatures: [
                {
                    'productFeatureId': ***********,
                    'productClueName': '器乐培训',
                    'productClueType': 3,
                },
                {
                    'productFeatureId': ***********,
                    'productClueName': '播音主持艺考培训',
                    'productClueType': 3,
                },
            ],
        },
    },
};

const marketingPointDiagnose = {
    type: 192,
    'payload': {
        'options': [
            {
                'inferenceMode': 1, // 推理模式, 1-强调要点，2-综合演绎
                'expectKeywordCnt': 9, // 营销要点数量
                'flowLevelHighCnt': 2, // 流量覆盖为高的营销要点数量
                'flowLevelMediumCnt': 3, // 流量覆盖为中的营销要点数量
                'flowLevelLowCnt': 4, // 流量覆盖为弱的营销要点数量
                'campaignId': 123545,
                'campaignName': 'name',
                'creativeImages': [], // 复用现有的创意图片内容
            },
            {
                'inferenceMode': 1, // 推理模式, 1-强调要点，2-综合演绎
                'expectKeywordCnt': 9, // 营销要点数量
                'flowLevelHighCnt': 2, // 流量覆盖为高的营销要点数量
                'flowLevelMediumCnt': 3, // 流量覆盖为中的营销要点数量
                'flowLevelLowCnt': 4, // 流量覆盖为弱的营销要点数量
                'campaignId': 1235445,
                'campaignName': 'name2',
                'creativeImages': [], // 复用现有的创意图片内容
            },
            {
                'inferenceMode': 1, // 推理模式, 1-强调要点，2-综合演绎
                'expectKeywordCnt': 9, // 营销要点数量
                'flowLevelHighCnt': 2, // 流量覆盖为高的营销要点数量
                'flowLevelMediumCnt': 3, // 流量覆盖为中的营销要点数量
                'flowLevelLowCnt': 4, // 流量覆盖为弱的营销要点数量
                'campaignId': 12145,
                'campaignName': 'name3',
                'creativeImages': [], // 复用现有的创意图片内容
            },
            {
                'inferenceMode': 1, // 推理模式, 1-强调要点，2-综合演绎
                'expectKeywordCnt': 9, // 营销要点数量
                'flowLevelHighCnt': 2, // 流量覆盖为高的营销要点数量
                'flowLevelMediumCnt': 3, // 流量覆盖为中的营销要点数量
                'flowLevelLowCnt': 4, // 流量覆盖为弱的营销要点数量
                'campaignId': 12355,
                'campaignName': 'name4name4name4name4name4name4name4name4name4name4',
                'creativeImages': [], // 复用现有的创意图片内容
            },
            {
                'inferenceMode': 1, // 推理模式, 1-强调要点，2-综合演绎
                'expectKeywordCnt': 9, // 营销要点数量
                'flowLevelHighCnt': 2, // 流量覆盖为高的营销要点数量
                'flowLevelMediumCnt': 3, // 流量覆盖为中的营销要点数量
                'flowLevelLowCnt': 4, // 流量覆盖为弱的营销要点数量
                'campaignId': 12351235,
                'campaignName': 'name5',
                'creativeImages': [], // 复用现有的创意图片内容
            },
        ],
    },
};

const conflictNegative = {
    type: 193,
    payload: {
        field: 'conflictNegative',
        campaignId: 111,
    },
};
const FcAiMax = {
    type: 194,
    payload: {
        data: {
        },
    },
};

const FeedAiMax = {
    type: 202,
    payload: {
        data: {
        },
    },
};

const AiIncubator = {
    type: 1001,
    payload: {
        cardParameters: {
            'budget': 150.31, // 预算，为0代表不限，选填，默认为0
            'budgetType': 7, // 预算周期类型，7-长周期预算    3-日预算

            'campaignBidRatio': 1.51, // 爱采购加油推计划出价系数，[1-10]闭区间，最多支持2位小数，选填，默认值为null
            'holidayPremium': 0, // 节假日是否溢价 0-工作日和节假日都溢价 1-工作日溢价
            'premiumTime': [1], // 溢价生效时间 1-日间 2-夜间 3-凌晨，全选传入 [1, 2, 3]
        },
    },
};

const AiIncubatorEdit = {
    type: 1002,
    payload: {
        data: {
        },
    },
};

const invalidCluesTableSelector = {
    type: 195,
    payload: {
        cardParameters: {
            options: [{
                clueId: 131312, // 线索id
                solutionType: 6, // 线索类型
                clueDate: '2023-06-28 10:48:46', // 线索时间
                reason: '不符合申诉规定ocpc/ocpm范围',
            }],
            canReport: true,
            timeRange: ['2024-07-01', '2024-07-03'],
        },
    },
};

const dateRangeClickInput = {
    type: 196,
    payload: {},
};

const diagnoseAgent = {
    type: 1018,
    payload: {
        title: '账户诊断',
        /* type: 'check',
        itemList: [
            {
                title: '信息状态',
                status: '异常异常，测试一个比较长的balalbala',
                icon: 'error',
            },
            {
                title: '资质状态',
                status: '无异常',
                icon: 'info',
            },
            {
                title: '信息状态2',
                status: '异常异常，测试一个比较长的balalbala',
                icon: 'error',
            },
            {
                title: '资质状态2',
                status: '无异常',
                icon: 'info',
            },
            {
                title: '信息状态3',
                status: '异常异常，测试一个比较长的balalbala',
                icon: 'error',
            },
            {
                title: '资质状态3',
                status: '无异常',
                icon: 'info',
            },
        ], */
        type: 'think',
        itemList: [
            {
                title: '信息状态',
                loading: '正在分析定向覆盖竞争力……',
                finish: '定向覆盖分析完成，<Mark status="warning" size="small">波动显著</Mark>',
            },
            {
                title: '资质状态',
                loading: '正在根据业务点「公司注册」「国际业务」和消费体量圈选可信竞对……',
            },
            {
                title: '信息状态2',
                loading: '正在分析定向覆盖竞争力……',
                finish: '定向覆盖分析完成，<Mark status="info" size="small">波动正常</Mark>',
            },
            {
                title: '资质状态2',
                loading: '正在根据业务点「公司注册」「国际业务」和消费体量圈选可信竞对……',
                finish: '可信竞对分析完成：<Sug prompt="去查看" theme="plain" size="small"></Sug>',
            },
            {
                title: '生成最终结论',
                loading: '生成中……',
            },
        ],
        field: 'checkItem',
        tipContent: '提示内容',
    },
};

const consumerInfo = {
    type: 1019,
    payload: {
        status: 'collapse',
        projectShowList: [
            {
                processId: 'test',
                projectId: 10001,
                aixProduct: [1],
                projectName: '轻舸投放项目_0611_23:54:57',
                baseCharge: 7548.00,
                contrastCharge: 6789.234,
                chargeDiff: 698.234,
                chargeRatio: 0.1052,
                prompt: '诊断【项目A】的消费波动',
            },
            {
                processId: 'test',
                projectId: 10002,
                aixProduct: [2],
                projectName: '轻舸投放项目B_0689_23:39:48',
                baseCharge: 2077.00,
                contrastCharge: 1653.987,
                chargeDiff: -540.91,
                chargeRatio: -0.874,
            },
        ],
        field: 'consumerInfo',
    },
};

const SelectedCampaignAdgroup = {
    type: 209,
    payload: {
        currentField: 'creativeTexts',
        campaignId: 123,
        curLevel: 'none',
        expandLevel: ['campaign', 'unit'],
        chooseLevel: ['campaign', 'unit'],
        aixProduct: [],
    },
};

const CreativeTextEtaRecommend = {
    type: 214,
    payload: {
        'creativeTypes': [
            {
                'title': '{医疗保健服务}-点击进入查询～',
                'description1': '医疗保健,一站式服务,专业全面,让您无忧.医疗保健,为您的健康保驾护航.',
                'addFrom': 18,
                'md5': '255f49471be1e7468c079cd95f905cad',
            },
            {
                'title': '{医疗保健服务}-点击进入',
                'description1': '医疗保健,专业,全面.点击了解更多详情!',
                'addFrom': 18,
                'md5': 'cefec9ee61ba1768104f620014c1121a',
            },
            {
                'title': '2024年{医疗保健服务}',
                'description1': '医疗保健,专业团队,为您的健康保驾护航.{医疗保健服务},专业全面,一键下载',
                'addFrom': 18,
                'md5': '46e8d518fa5959dd4d48b85722e2fbf3',
            },
            {
                'title': '2024年{医疗保健服务}-点击进入',
                'description1': '医疗保健,专业指导,全面呵护,让您远离疾病困扰.医疗保健,为您的健康保驾护航.',
                'addFrom': 18,
                'md5': '2b5bf7d262dfa4cbd4ca11f5663d7d12',
            },
            {
                'title': '{医疗保健服务}-2024年新政策公布',
                'description1': '医疗保健,专业团队,全面保障,让您安心无忧.医疗保健,让您的生活更加美好.',
                'addFrom': 18,
                'md5': '0342dd38f562a7445be7e23cc65447c8',
            },
        ],
        'prompt': '确认以下所选文案素材使用到方案中。',
        'landPageUrl': 'https://ada.baidu.com/imlp/xyl?imid=6ffa4d6b6781bc2cf9bf16e921030634#back1692616484907',
        'isRecommend': true,
        'distribution': 'space-between',
        'failInfo': false,
        'aixProduct': [
            1,
        ],
        campaignInfoParam: {
            'campaignId': 123,
        }, // todo 这个需要智能体新增
    },
};

const CreativeTextRsaRecommend = {
    type: 215,
    payload: {
        aixProduct: [1],
        creativeTypes: [{
            'programTitles': [ // 推荐程序化创意的方案 才有数据
                '标题1-{书法培训}-零基础入门到精通-免费试学',
                '标题2-{书法培训}-零基础入门到精通-免费试学',
                '标题3-{书法培训}-零基础入门到精通-免费试学',
            ],
            'programDescriptions': [ // 推荐程序化创意的方案 才有数据
                '描述1-{书法培训}-零基础入门到精通-免费试学11',
                '描述2-{书法培训}-零基础入门到精通-免费试学11',
                '描述3-{书法培训}-零基础入门到精通-免费试学11',
            ],
        }],

        'prompt': '确认以下所选文案素材使用到方案中。',
        'landPageUrl': 'https://ada.baidu.com/imlp/xyl?imid=6ffa4d6b6781bc2cf9bf16e921030634#back1692616484907', // 会下掉
        'isRecommend': true, // 是否为推荐场景
        'failInfo': false, // 是否为失败数据
        'creativeTextType': 1, // 创意类型，0-普通创意，1-程序化创意, 客户打开记得带入GUI， 文生文接口需要这个字段
        'campaignId': 123,
        'enableCreativeShow': true,
        'isDefaultSelected': true,
        'maxCount': 50,
        'taskId': 123, // 新增 字段
        'sceneInitTimestamp': 12213245,
        'campaignInfoParam': { // 新增结构
            'creativeTextType': 1, // 新增 1：程序化创意； 其他：非程序化创意 （默认）
            'expectBusinessSummary': '方案业务描述', //  新增字段
            'campaignId': 123, //  新增  方案Id，非新建场景才有值
            'expectKeywords': ['营销要点1', '营销要点2'],
            'url': 'https://ada.baidu.com/imlp/xyl?imid=6ffa4d6b6781bc2cf9bf16e9210306',
        },
    },
};

const CreativeTextRsaEdit = {
    type: 218,
    payload: {
        creativeTypes: [{
            creativeId: 123232,
            programTitleAuditInfos: [{
                'content': '标题1-{书法培训}-零基础入门到精通-免费试学',
                'auditStatus': 0,
                'reason': '保存失败的错误信息',
                // 'addFrom': 18,
                // 'md5': '07cfcebb9a57aafc33de60040889855c',
                // 'wholeReason': '拒审理由',
                // 'errorMsg': '保存失败的错误信息',

            },
            {
                'content': '标题2-{书法培训}-零基础入门到精通-免费试学',
                'auditStatus': 0,
                'reason': '保存失败的错误信息',
                // 'addFrom': 18,
                // 'md5': '07cfcebb9a57aafc33de60040889855c',
                // 'wholeReason': '拒审理由',
                // 'errorMsg': '保存失败的错误信息',

            },
            {
                'content': '标题3-{书法培训}-零基础入门到精通-免费试学',
                'auditStatus': 0,
                'reason': '保存失败的错误信息',
                // 'addFrom': 18,
                // 'md5': '07cfcebb9a57aafc33de60040889855c',
                // 'wholeReason': '拒审理由',
                // 'errorMsg': '保存失败的错误信息',

            }],
            'programDescriptionAuditInfos': [ // 推荐程序化创意的方案 才有数据
                {
                    'content': '描述1-{书法培训}-零基础入门到精通-免费试学11',
                    'auditStatus': 0,
                    'reason': '保存失败的错误信息',
                    // 'addFrom': 18,
                    // 'md5': '07cfcebb9a57aafc33de60040889855c',
                    // 'wholeReason': '拒审理由',
                    // 'errorMsg': '保存失败的错误信息',
                },
                {
                    'content': '描述2-{书法培训}-零基础入门到精通-免费试学11',
                    'auditStatus': 0,
                    'reason': '保存失败的错误信息',
                    // 'addFrom': 18,
                    // 'md5': '07cfcebb9a57aafc33de60040889855c',
                    // 'wholeReason': '拒审理由',
                    // 'errorMsg': '保存失败的错误信息',
                },
                {
                    'content': '描述3-{书法培训}-零基础入门到精通-免费试学11',
                    'auditStatus': 0,
                    'reason': '保存失败的错误信息',
                    // 'addFrom': 18,
                    // 'md5': '07cfcebb9a57aafc33de60040889855c',
                    // 'wholeReason': '拒审理由',
                    // 'errorMsg': '保存失败的错误信息',
                },
            ],
        }],
        recommendCreativeTypes: [{
            'programTitles': [ // 推荐程序化创意的方案 才有数据
                '推荐------标题1-{书法培训}-零基础入门到精通-免费试学',
                '推荐------标题2-{书法培训}-零基础入门到精通-免费试学',
                '推荐------标题3-{书法培训}-零基础入门到精通-免费试学',
                '推荐------标题4-{书法培训}-零基础入门到精通-免费试学',
                '推荐------标题5-{书法培训}-零基础入门到精通-免费试学',
                '推荐------标题6-{书法培训}-零基础入门到精通-免费试学',
                '推荐------标题7-{书法培训}-零基础入门到精通-免费试学',
                '推荐------标题8-{书法培训}-零基础入门到精通-免费试学',
                '推荐------标题9-{书法培训}-零基础入门到精通-免费试学',
                '推荐------标题10-{书法培训}-零基础入门到精通-免费试学',
            ],
            'programDescriptions': [ // 推荐程序化创意的方案 才有数据
                '推荐------描述1-{书法培训}-零基础入门到精通-免费试学11',
                '推荐------描述2-{书法培训}-零基础入门到精通-免费试学11',
                '推荐------描述3-{书法培训}-零基础入门到精通-免费试学11',
            ],
        }],
        'aixProduct': [
            1,
        ],
        campaignInfoParam: {
            'campaignId': 123,
        }, // todo 这个需要智能体新增
    },
};

const CreativeTextEtaEdit = {
    type: 220,
    payload: {
        'creativeTypes': [
            {
                creativeId: 123123,
                'title': '{医疗保健服务}-点击进入查询～',
                'description1': '医疗保健,一站式服务,专业全面,让您无忧.医疗保健,为您的健康保驾护航.',
                'addFrom': 18,
                'md5': '255f49471be1e7468c079cd95f905cad',
                offlineReasons: [
                    {
                        'mainReason': '12',
                        'detailReason': '[]',
                    },
                    {
                        'mainReason': '28',
                        'detailReason': '{"audit_detail":[{"desc":"创意文案","text":"内容可能涉及等级词汇（十大）"}]}',
                    },
                    {
                        'mainReason': '3',
                        'detailReason': '[["内容可能涉及等级词汇（内容可能涉及等级词汇（十大））",""]]',
                    },
                ],
            },
            {
                'title': '{医疗保健服务}-点击进入',
                'description1': '医疗保健,专业,全面.点击了解更多详情!',
                'addFrom': 18,
                'md5': 'cefec9ee61ba1768104f620014c1121a',
            },
            {
                'title': '2024年{医疗保健服务}',
                'description1': '医疗保健,专业团队,为您的健康保驾护航.{医疗保健服务},专业全面,一键下载',
                'addFrom': 18,
                'md5': '46e8d518fa5959dd4d48b85722e2fbf3',
            },
            {
                'title': '2024年{医疗保健服务}-点击进入',
                'description1': '医疗保健,专业指导,全面呵护,让您远离疾病困扰.医疗保健,为您的健康保驾护航.',
                'addFrom': 18,
                'md5': '2b5bf7d262dfa4cbd4ca11f5663d7d12',
            },
            {
                'title': '{医疗保健服务}-2024年新政策公布',
                'description1': '医疗保健,专业团队,全面保障,让您安心无忧.医疗保健,让您的生活更加美好.',
                'addFrom': 18,
                'md5': '0342dd38f562a7445be7e23cc65447c8',
            },
        ],
        recommendCreativeTypes: [
            {
                'title': '推荐------文案1',
                'description1': '推荐-----医疗保健,专业,全面.点击了解更多详情!',
                'addFrom': 18,
                'md5': 'cefec9ee61ba1768104f620014c1121a',
            },
            {
                'title': '推荐------2024年{医疗保健服务}',
                'description1': '推荐------医疗保健,专业团队,为您的健康保驾护航.{医疗保健服务},专业全面,一键下载',
                'addFrom': 18,
                'md5': '46e8d518fa5959dd4d48b85722e2fbf3',
            },
            {
                'title': '推荐------2024年{医疗保健服务}-点击进入',
                'description1': '推荐------医疗保健,专业指导,全面呵护,让您远离疾病困扰.医疗保健,为您的健康保驾护航.',
                'addFrom': 18,
                'md5': '2b5bf7d262dfa4cbd4ca11f5663d7d12',
            },
            {
                'title': '推荐------{医疗保健服务}-2024年新政策公布',
                'description1': '推荐------医疗保健,专业团队,全面保障,让您安心无忧.医疗保健,让您的生活更加美好.',
                'addFrom': 18,
                'md5': '0342dd38f562a7445be7e23cc65447c8',
            },
        ],
        'aixProduct': [
            1,
        ],
        campaignInfoParam: {
            'campaignId': 123,
        }, // todo 这个需要智能体新增
    },
};

const BindProject = {
    type: 217,
    payload: {},
};

const TargetCrowd = {
    type: 216,
    payload: {
        aixProduct: [1],
        fcKeywordCampaignType: {
            'campaignType': {
                'campaignId': 662506892,
                'campaignName': '轻舸营销方案_10_29_16:20:14',
                'marketingTargetId': 7,
                'campaignBidType': 1,
                'campaignOcpcBidType': 1,
                'campaignOcpcBid': 23,
                'campaignTransTypes': [
                    120,
                ],
                'campaignAutoOptimizationStatus': false,
                'structuredCategoryType': 1,
                'campaignPayTimesStatus': false,
                'campaignAutoTargetingStatus': 1,
                'campaignSegmentRecommendStatus': 1,
                'campaignCreativeTextOptimizationStatus': 1,
                'transManagerMode': 0,
                'transAsset': 0,
                'budget': 123213,
                'campaignDeviceBidStatus': false,
                'equipmentType': 1,
                'schedule': [],
                'schedulePriceFactors': [
                    {
                        'timeId': 700,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 701,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 702,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 703,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 704,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 705,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 706,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 707,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 708,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 709,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 710,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 711,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 712,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 713,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 714,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 715,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 716,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 717,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 718,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 719,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 720,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 721,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 722,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 723,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 100,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 101,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 102,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 103,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 104,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 105,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 106,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 107,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 108,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 109,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 110,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 111,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 112,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 113,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 114,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 115,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 116,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 117,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 118,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 119,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 120,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 121,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 122,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 123,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 200,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 201,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 202,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 203,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 204,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 205,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 206,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 211,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 212,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 213,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 214,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 215,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 216,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 217,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 218,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 219,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 220,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 221,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 222,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 223,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 207,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 208,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 209,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 210,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 300,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 301,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 302,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 303,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 304,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 305,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 306,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 311,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 312,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 313,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 314,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 315,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 316,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 317,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 318,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 319,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 320,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 321,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 322,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 323,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 307,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 308,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 309,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 310,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 400,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 401,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 402,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 403,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 404,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 405,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 406,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 411,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 412,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 413,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 414,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 415,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 416,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 417,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 418,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 419,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 420,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 421,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 422,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 423,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 407,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 408,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 409,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 410,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 500,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 501,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 502,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 503,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 504,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 505,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 506,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 511,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 512,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 513,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 514,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 515,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 516,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 517,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 518,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 519,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 520,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 521,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 522,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 523,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 507,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 508,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 509,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 510,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 600,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 601,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 602,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 603,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 604,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 605,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 606,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 611,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 612,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 613,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 614,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 615,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 616,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 617,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 618,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 619,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 620,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 621,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 622,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 623,
                        'priceFactor': 1,
                    },
                    {
                        'timeId': 607,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 608,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 609,
                        'priceFactor': 1.5,
                    },
                    {
                        'timeId': 610,
                        'priceFactor': 1.5,
                    },
                ],
                'adType': 0,
                'regionTarget': [
                    19064,
                    11258,
                    3000,
                    25196,
                    5070,
                    13327,
                    20072,
                    15335,
                    32272,
                    5081,
                    32273,
                    32276,
                    4085,
                    32277,
                    26214,
                    32278,
                    32279,
                    4088,
                    21155,
                    25219,
                    31284,
                    33000,
                    34000,
                    35000,
                    36000,
                ],
                'exactNegativeWords': [],
                'priceRatio': 1,
                'pcPriceRatio': 1,
                'status': 21,
                'reOnlineReasons': [],
                'regionPriceFactor': [],
                'geoLocationStatus': 1,
                'crowdAixTargetBaseTypes': [],
            },
            'productDetails': [],
            'adgroupType': {
                'adgroupName': '轻舸单元_11_06_15:38:36',
                'maxPrice': 1,
                'adgroupAutoTargetingStatus': true,
                'segmentRecommendStatus': true,
                'creativeTextOptimizationStatus': true,
            },
            'cbind': {},
            'keywordTypes': [],
            'creativeTypes': [],
            'pictureSegmentTypes': [],
            'videoSegmentTypes': [],
        },
    },
};

const CampaignSubmitCard = {
    type: 224,
    payload: {
        aixProduct: [1],
        fcKeywordCampaignType: {
            campaignType: {
                'campaignId': 584656221,
                'campaignName': '234',
                'businessPointId': 200501001,
                'businessPointName': '制冷设备',
                'marketingTargetId': 0,
                'bidPrefer': 2,
                'pause': false,
                'regionTarget': [
                    19064,
                    11258,
                    3000,
                    25196,
                    5070,
                    13327,
                    20072,
                    15335,
                    32272,
                    5081,
                    32273,
                    32276,
                    4085,
                    32277,
                    26214,
                    32278,
                    32279,
                    4088,
                    21155,
                    25219,
                    31284,
                    33000,
                    12099,
                    18040,
                    8302,
                    4112,
                    4113,
                    5052,
                    4117,
                    10118,
                    19054,
                    19056,
                    19058,
                    19062,
                ],
                'exactNegativeWords': [],
                'priceRatio': 1,
                'adType': 0,
                'pcPriceRatio': 1,
                'status': 21,
                'schedulePriceFactors': [],
                'reOnlineReasons': [],
                'regionPriceFactor': [],
                'indepdentNegativeWords': [],
                'indepdentExactNegativeWords': [],
                'regionRestrictStatus': false,
                'regionType': 0,
                'regionArea': [],
                'regionStore': [],
                'storePageInfos': [],
                'priceStrategy': {
                    // project: {
                    //     strategyId: 3436084448,
                    //     sharedBudget: 1234,
                    // },
                },
                'shopType': 0,
                'equipmentType': 3,
                'campaignDeviceBidStatus': false,
                'campaignBidType': 1,
                'campaignBid': 4,
                'campaignOcpcBidType': 3,
                'campaignTransTypes': [
                    1,
                ],
                'campaignCvSources': [
                    1000,
                ],
                'campaignAutoOptimizationStatus': true,
                'campaignPayTimesStatus': false,
                'normalNegativeKeywordCount': 0,
                'exactNegativeKeywordCount': 0,
                'transAsset': 2,
                'transAssetId': -1,
                'geoLocationStatus': 0,
                'promotionScene': 0,
                'productCategoryTypeStatus': false,
                'storeDistance': 0,
                'transManagerMode': 0,
                'budgetType': 0,
                'negativeWords': [],
                structuredProductIds: [1234],
                structuredCategoryType: 3,
                schedule: [
                    {
                        'weekDay': 1,
                        'startHour': 2,
                        'endHour': 6,
                    },
                    {
                        'weekDay': 2,
                        'startHour': 2,
                        'endHour': 6,
                    },
                    {
                        'weekDay': 2,
                        'startHour': 17,
                        'endHour': 18,
                    },
                    {
                        'weekDay': 4,
                        'startHour': 3,
                        'endHour': 4,
                    },
                    {
                        'weekDay': 4,
                        'startHour': 11,
                        'endHour': 14,
                    },
                    {
                        'weekDay': 4,
                        'startHour': 20,
                        'endHour': 21,
                    },
                    {
                        'weekDay': 5,
                        'startHour': 1,
                        'endHour': 2,
                    },
                    {
                        'weekDay': 5,
                        'startHour': 11,
                        'endHour': 14,
                    },
                    {
                        'weekDay': 6,
                        'startHour': 11,
                        'endHour': 14,
                    },
                    {
                        'weekDay': 6,
                        'startHour': 21,
                        'endHour': 22,
                    },
                    {
                        'weekDay': 7,
                        'startHour': 11,
                        'endHour': 14,
                    },
                    {
                        'weekDay': 7,
                        'startHour': 17,
                        'endHour': 19,
                    },
                ],
            },
            creativeTypes: [
                // 自定义创意
                {
                    'creativeId': 1111,
                    'title': 'iuhiuuihiuhuh',
                    'description1': 'cdvvrvrvvv',
                    'description2': '',
                },
            ],
            pictureSegmentTypes: [
                {
                    'source': 0,
                    'segmentType': 101,
                    // eslint-disable-next-line quotes
                    'auditContent': "{\"score\":6,\"desc\":\"1\",\"picUrl\":\"https://fc3tn.baidu.com/it/u=2171113738,3978856336&fm=203\",\"segmentType\":101,\"rawPicUrl\":\"https://vcg-image.cdn.bcebos.com/VCG41N965015286_4041439866%2C2835535721.jpeg\",\"imageId\":\"7681208622\"}",
                    'addfrom': 524959,
                },
            ],
        },
    },
};

const ShopAgent = {
    type: 226,
    payload: {
        bcpAgentInfoList: [
            {
                'bcpAgentId': 203631,
                'bcpAgentName': 'a教师助理1321233213智能体',
                'bcpAgentHeadImage': 'https://fc-feed.bj.bcebos.com/bot_preview.png',
                'bcpAgentUrl': 'https://ada.baidu.com/site/wjzkkvp/agent?imid=a34f4b278eb85bd68edc6090d6997571',
                'transTypes': [],
                'sameStatus': 0, // 0 表示同账户，1表示同主体
            },
            {
                'bcpAgentId': 203632,
                'bcpAgentName': 'a教师助理1321233213智能体',
                'bcpAgentHeadImage': 'https://fc-feed.bj.bcebos.com/bot_preview.png',
                'bcpAgentUrl': 'https://ada.baidu.com/site/wjzkkvp/agent?imid=a34f4b278eb85bd68edc6090d6997571',
                'transTypes': [],
                'sameStatus': 0, // 0 表示同账户，1表示同主体
            },
            {
                'bcpAgentId': 203633,
                'bcpAgentName': 'a教师助理1321233213智能体',
                'bcpAgentHeadImage': 'https://fc-feed.bj.bcebos.com/bot_preview.png',
                'bcpAgentUrl': 'https://ada.baidu.com/site/wjzkkvp/agent?imid=a34f4b278eb85bd68edc6090d6997571',
                'transTypes': [],
                'sameStatus': 1, // 0 表示同账户，1表示同主体
            },
            {
                'bcpAgentId': 203634,
                'bcpAgentName': 'a教师助理1321233213智能体',
                'bcpAgentHeadImage': 'https://fc-feed.bj.bcebos.com/bot_preview.png',
                'bcpAgentUrl': 'https://ada.baidu.com/site/wjzkkvp/agent?imid=a34f4b278eb85bd68edc6090d6997571',
                'transTypes': [],
                'sameStatus': 1, // 0 表示同账户，1表示同主体
            },
            {
                'bcpAgentId': 203635,
                'bcpAgentName': 'a教师助理1321233213智能体',
                'bcpAgentHeadImage': 'https://fc-feed.bj.bcebos.com/bot_preview.png',
                'bcpAgentUrl': 'https://ada.baidu.com/site/wjzkkvp/agent?imid=a34f4b278eb85bd68edc6090d6997571',
                'transTypes': [],
                'sameStatus': 1, // 0 表示同账户，1表示同主体
            },
            {
                'bcpAgentId': 203636,
                'bcpAgentName': 'a教师助理1321233213智能体',
                'bcpAgentHeadImage': 'https://fc-feed.bj.bcebos.com/bot_preview.png',
                'bcpAgentUrl': 'https://ada.baidu.com/site/wjzkkvp/agent?imid=a34f4b278eb85bd68edc6090d6997571',
                'transTypes': [],
                'sameStatus': 1, // 0 表示同账户，1表示同主体
            },
            {
                'bcpAgentId': 203637,
                'bcpAgentName': 'a教师助理1321233213智能体',
                'bcpAgentHeadImage': 'https://fc-feed.bj.bcebos.com/bot_preview.png',
                'bcpAgentUrl': 'https://ada.baidu.com/site/wjzkkvp/agent?imid=a34f4b278eb85bd68edc6090d6997571',
                'transTypes': [],
                'sameStatus': 1, // 0 表示同账户，1表示同主体
            },
            {
                'bcpAgentId': 203638,
                'bcpAgentName': 'a教师助理1321233213智能体',
                'bcpAgentHeadImage': 'https://fc-feed.bj.bcebos.com/bot_preview.png',
                'bcpAgentUrl': 'https://ada.baidu.com/site/wjzkkvp/agent?imid=a34f4b278eb85bd68edc6090d6997571',
                'transTypes': [],
                'sameStatus': 1, // 0 表示同账户，1表示同主体
            },
        ],
        currentField: 'bcpAgentInfo',
        currentFieldOperate: 'MOD',
    },
};

const FCLandPageSelectorCard = {
    type: 1000,
    payload: {
        cardParameters: {},
        options: [
            {sourceType: 0, pageType: 1, pageUrl: 'www.baidu.com', picture: 'https://placehold.co/140x306', prompt: '选择【岚图FREE - 提表单】落地页'},
        ],
    },
};

const FCAppSelectorCard = {
    type: 1004,
    payload: {
        'field': 'appinfo',
        'fieldOperate': 'MOD',
        cardParameters: {
            'fcAppInfos': [
                ...(Array(20).fill('').map((i, idx) => ({
                    'sid': 23702414 + idx,
                    'appName': '程猫民宿' + idx,
                    'appId': 11375540 + idx,
                    'platform': 3,
                    'downloadUrl': 'https://apps.apple.com/cn/app/%E7%A8%8B%E7%8C%AB%E6%B0%91%E5%AE%BF/id1060935066',
                    'icon': 'https://is2-ssl.mzstatic.com/image/thumb/Purple123/v4/09/0c/f2/090cf2f8-b7dc-7e76-b7d7-638e875995f8/source/512x512bb.jpg',
                    'versionCode': `2.2.${idx}`,
                    'versionId': 23629550 + idx,
                    'status': 0,
                    'packageName': 'com.elong.eroom',
                    'packageId': 1060935066 + idx,
                    'channelPackage': '',
                    'source': 2,
                    'channelId': 0,
                    'adgroupCount': 0,
                    'campaignCount': 0,
                }))),
                ...(
                    Array(20).fill('').map((i, idx) => ({
                        sid: 36640423 + idx,
                        appName: '百度' + idx,
                        appId: 11642160 + idx,
                        platform: 1,
                        downloadUrl: 'https://gdown.baidu.com/appcenter/pkg/upload/a345881afff7a7e2f08e20bb661be8aa',
                        icon: 'https://app-center.cdn.bcebos.com/appcenter/sts/pcfile/5088868476/041b8582640e6432421a547074e90db0.jpg',
                        versionCode: `13.45.${idx}`,
                        versionId: 232805632 + idx,
                        status: 0,
                        packageName: 'com.baidu.searchbox',
                        packageId: 393768490 + idx,
                        channelPackage: '8-分享-安卓01-0327' + idx,
                        source: 3,
                        channelId: 10001527014 + idx,
                        adgroupCount: 0,
                        campaignCount: 0,
                    }))
                ),
            ],
        },
    },
};

const aiBuildUploader = {
    type: 1020,
    payload: {},
};

const aiBuildSelectSource = {
    type: 1040,
    payload: {
        field: 'ai-build-select-source',
        cardParameters: {
            campaignTypes: [
                {
                    campaignId: 1,
                    campaignName: '方案1',
                    marketingTargetId: 1,
                    projectId: 1,
                    projectName: '项目1',
                    isSuggestion: true,
                },
                {
                    campaignId: 2,
                    campaignName: '方案2',
                    marketingTargetId: 1,
                    projectId: 1,
                    projectName: '项目1',
                    isSuggestion: false,
                },
                {
                    campaignId: 3,
                    campaignName: '方案3',
                    marketingTargetId: 3,
                    projectId: 3,
                    projectName: '项目3',
                    isSuggestion: true,
                },
            ],
            allowPreviousStep: false,
            previousStepField: '',
            // campaignIds: [1, 2],
            // projectIds: [3],
            useOriginNegativeKeywords: false,
        },
    },
};

const aiBuildStructure = {
    type: 1022,
    payload: {
        'distribution': 'space-between',
        'field': 'ai-build-structure',
        'cardParameters': {
            'keyInformation': {
                'keyInformationId': 0,
                'business': [
                    '不锈钢桥架',
                    '镀锌桥架',
                ],
                'businessAdvantages': [
                    '高品质不锈钢与镀锌桥架，防腐防锈;性价比高，满足各类工程项目需求',
                ],
                'mobileUrls': [
                    'https://sjh.baidu.com',
                ],
                'pcUrls': [
                    'https://sjh.baidu.com',
                ],
                'keywords': [
                    '电缆桥架',
                    '梯式桥架',
                    '槽式桥架',
                    '不锈钢桥架',
                    '镀锌桥架',
                ],
                'avoidBusiness': [
                    '道路桥架',
                ],
                'budget': 345.67,
                'images': [],
                'bidType': 0,
                'marketingTargetId': 7,
            },
            'structureTemplate': [
                {
                    'campaignPartitionType': 1, // 计划划分方式，本次新增，1- 以投放设备+经营业务划分，2 - 以经营业务划分，3 - 只生成品牌计划
                    'templateId': 5,
                    'templateName': '以投放设备+经营业务划分',
                    'suggestReason': '  * 精准定位：可以根据不同设备的用户行为习惯进行针对性投放，优化广告效果。例如，移动端用户更倾向于点击电话联系类广告，而PC端用户可能更关注价格和详细信息。\n  * 预算分配灵活：不同设备可能有不同的表现，可以根据表现优化预算分配，提高投资回报率（ROI）。\n',
                    'campaigns': [
                        {
                            'campaignName': '移动-不锈钢桥架',
                            'campaignBidType': 0,
                            'equipmentType': 2,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                        {
                            'campaignName': 'PC-不锈钢桥架',
                            'campaignBidType': 0,
                            'equipmentType': 1,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                        {
                            'campaignName': '移动-镀锌桥架',
                            'campaignBidType': 0,
                            'equipmentType': 2,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                        {
                            'campaignName': 'PC-镀锌桥架',
                            'campaignBidType': 0,
                            'equipmentType': 1,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                    ],
                },
                {
                    'campaignPartitionType': 1, // 计划划分方式，本次新增，1- 以投放设备+经营业务划分，2 - 以经营业务划分，3 - 只生成品牌计划
                    'templateId': 2,
                    'templateName': '以投放设备+经营业务划分',
                    'suggestReason': '  * 精准定位：可以根据不同设备的用户行为习惯进行针对性投放，优化广告效果。例如，移动端用户更倾向于点击电话联系类广告，而PC端用户可能更关注价格和详细信息。\n  * 预算分配灵活：不同设备可能有不同的表现，可以根据表现优化预算分配，提高投资回报率（ROI）。\n',
                    'campaigns': [
                        {
                            'campaignName': '移动-不锈钢桥架',
                            'campaignBidType': 0,
                            'equipmentType': 2,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                        {
                            'campaignName': 'PC-不锈钢桥架',
                            'campaignBidType': 0,
                            'equipmentType': 1,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                        {
                            'campaignName': '移动-镀锌桥架',
                            'campaignBidType': 0,
                            'equipmentType': 2,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                        {
                            'campaignName': 'PC-镀锌桥架',
                            'campaignBidType': 0,
                            'equipmentType': 1,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                    ],
                },
            ],
            allowPreviousStep: true,
            previousStepField: 'ai-build-form',
        },
    },
};

const aiRefreshStructure = {
    type: 1042,
    payload: {
        'distribution': 'space-between',
        'field': 'ai-refresh-structure',
        'cardParameters': {
            'keyInformations': [{
                'keyInformationId': 0,
                'business': [
                    '不锈钢桥架',
                    '镀锌桥架',
                ],
                'businessAdvantages': [
                    '高品质不锈钢与镀锌桥架，防腐防锈;性价比高，满足各类工程项目需求',
                ],
                'mobileUrls': [
                    'https://sjh.baidu.com',
                ],
                'pcUrls': [
                    'https://sjh.baidu.com',
                ],
                'keywords': [
                    '电缆桥架',
                    '梯式桥架',
                    '槽式桥架',
                    '不锈钢桥架',
                    '镀锌桥架',
                ],
                'avoidBusiness': [
                    '道路桥架',
                ],
                'budget': 345.67,
                'images': [],
                'bidType': 0,
                'marketingTargetId': 7,
            }],
            'structureTemplate': [
                {
                    'campaignPartitionType': 1, // 计划划分方式，本次新增，1- 以投放设备+经营业务划分，2 - 以经营业务划分，3 - 只生成品牌计划
                    'templateId': 5,
                    'templateName': '以投放设备+经营业务划分',
                    'suggestReason': '  * 精准定位：可以根据不同设备的用户行为习惯进行针对性投放，优化广告效果。例如，移动端用户更倾向于点击电话联系类广告，而PC端用户可能更关注价格和详细信息。\n  * 预算分配灵活：不同设备可能有不同的表现，可以根据表现优化预算分配，提高投资回报率（ROI）。\n',
                    'campaigns': [
                        {
                            'campaignName': '移动-不锈钢桥架',
                            'campaignBidType': 0,
                            'equipmentType': 2,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                        {
                            'campaignName': 'PC-不锈钢桥架',
                            'campaignBidType': 0,
                            'equipmentType': 1,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                        {
                            'campaignName': '移动-镀锌桥架',
                            'campaignBidType': 0,
                            'equipmentType': 2,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                        {
                            'campaignName': 'PC-镀锌桥架',
                            'campaignBidType': 0,
                            'equipmentType': 1,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                    ],
                },
                {
                    'campaignPartitionType': 1, // 计划划分方式，本次新增，1- 以投放设备+经营业务划分，2 - 以经营业务划分，3 - 只生成品牌计划
                    'templateId': 2,
                    'templateName': '以投放设备+经营业务划分',
                    'suggestReason': '  * 精准定位：可以根据不同设备的用户行为习惯进行针对性投放，优化广告效果。例如，移动端用户更倾向于点击电话联系类广告，而PC端用户可能更关注价格和详细信息。\n  * 预算分配灵活：不同设备可能有不同的表现，可以根据表现优化预算分配，提高投资回报率（ROI）。\n',
                    'campaigns': [
                        {
                            'campaignName': '移动-不锈钢桥架',
                            'campaignBidType': 0,
                            'equipmentType': 2,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                        {
                            'campaignName': 'PC-不锈钢桥架',
                            'campaignBidType': 0,
                            'equipmentType': 1,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                        {
                            'campaignName': '移动-镀锌桥架',
                            'campaignBidType': 0,
                            'equipmentType': 2,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                        {
                            'campaignName': 'PC-镀锌桥架',
                            'campaignBidType': 0,
                            'equipmentType': 1,
                            'adgroups': [
                                {
                                    'adgroupName': '核心词',
                                },
                                {
                                    'adgroupName': '通用词',
                                },
                                {
                                    'adgroupName': '电话词',
                                },
                                {
                                    'adgroupName': '价格词',
                                },
                            ],
                            'budget': 345.67,
                        },
                    ],
                },
            ],
            allowPreviousStep: true,
            previousStepField: 'ai-refresh-form',
        },
    },
};

const aiBuildPreview = {
    type: 1024,
    payload: {
        'distribution': 'space-between',
        'field': 'ai-build-preview',
        'cardParameters': {
            'keyInformation': {
                'keyInformationId': 0,
                'business': [
                    '不锈钢桥架',
                    '镀锌桥架',
                ],
                'businessAdvantages': [
                    '高品质不锈钢与镀锌桥架，防腐防锈;性价比高，满足各类工程项目需求',
                ],
                'mobileUrls': [
                    'https://sjh.baidu.com',
                ],
                'pcUrls': [
                    'https://sjh.baidu.com',
                ],
                'keywords': [
                    '电缆桥架',
                    '梯式桥架',
                    '槽式桥架',
                    '不锈钢桥架',
                    '镀锌桥架',
                ],
                'avoidBusiness': [
                    '道路桥架',
                ],
                'budget': 345.67,
                'images': [],
                'bidType': 0,
                'marketingTargetId': 7,
            },
            'accountStructure': {
                'templateId': 5,
                'templateName': '以投放设备+经营业务划分',
                'suggestReason': '  * 精准定位：可以根据不同设备的用户行为习惯进行针对性投放，优化广告效果。例如，移动端用户更倾向于点击电话联系类广告，而PC端用户可能更关注价格和详细信息。\n  * 预算分配灵活：不同设备可能有不同的表现，可以根据表现优化预算分配，提高投资回报率（ROI）。\n',
                'campaigns': [
                    {
                        'campaignName': '移动-不锈钢桥架',
                        'campaignBidType': 0,
                        'equipmentType': 2,
                        'adgroups': [
                            {
                                'adgroupName': '核心词',
                            },
                            {
                                'adgroupName': '通用词',
                            },
                            {
                                'adgroupName': '电话词',
                            },
                            {
                                'adgroupName': '价格词',
                            },
                        ],
                        'budget': 345.67,
                    },
                    {
                        'campaignName': 'PC-不锈钢桥架',
                        'campaignBidType': 0,
                        'equipmentType': 1,
                        'adgroups': [
                            {
                                'adgroupName': '核心词',
                            },
                            {
                                'adgroupName': '通用词',
                            },
                            {
                                'adgroupName': '电话词',
                            },
                            {
                                'adgroupName': '价格词',
                            },
                        ],
                        'budget': 345.67,
                    },
                    {
                        'campaignName': '移动-镀锌桥架',
                        'campaignBidType': 0,
                        'equipmentType': 2,
                        'adgroups': [
                            {
                                'adgroupName': '核心词',
                            },
                            {
                                'adgroupName': '通用词',
                            },
                            {
                                'adgroupName': '电话词',
                            },
                            {
                                'adgroupName': '价格词',
                            },
                        ],
                        'budget': 345.67,
                    },
                    {
                        'campaignName': 'PC-镀锌桥架',
                        'campaignBidType': 0,
                        'equipmentType': 1,
                        'adgroups': [
                            {
                                'adgroupName': '核心词',
                            },
                            {
                                'adgroupName': '通用词',
                            },
                            {
                                'adgroupName': '电话词',
                            },
                            {
                                'adgroupName': '价格词',
                            },
                        ],
                        'budget': 345.67,
                    },
                ],
            },
            'taskInfo': {
                'taskId': 12308,
                'taskName': '5_12308',
                'taskType': 5,
                'taskStatus': 0,
                'addTime': '2024-09-11 14:58:07',
            },
            allowPreviousStep: true,
            previousStepField: 'ai-build-structure',
        },
    },
};

const aiRefreshPreview = {
    type: 1043,
    payload: {
        'distribution': 'space-between',
        'field': 'ai-refresh-preview',
        'cardParameters': {
            'keyInformations': [{
                'keyInformationId': 0,
                'business': [
                    '不锈钢桥架',
                    '镀锌桥架',
                ],
                'businessAdvantages': [
                    '高品质不锈钢与镀锌桥架，防腐防锈;性价比高，满足各类工程项目需求',
                ],
                'mobileUrls': [
                    'https://sjh.baidu.com',
                ],
                'pcUrls': [
                    'https://sjh.baidu.com',
                ],
                'keywords': [
                    '电缆桥架',
                    '梯式桥架',
                    '槽式桥架',
                    '不锈钢桥架',
                    '镀锌桥架',
                ],
                'avoidBusiness': [
                    '道路桥架',
                ],
                'budget': 345.67,
                'images': [],
                'bidType': 0,
                'marketingTargetId': 7,
            }],
            'accountStructure': {
                'templateId': 5,
                'templateName': '以投放设备+经营业务划分',
                'suggestReason': '  * 精准定位：可以根据不同设备的用户行为习惯进行针对性投放，优化广告效果。例如，移动端用户更倾向于点击电话联系类广告，而PC端用户可能更关注价格和详细信息。\n  * 预算分配灵活：不同设备可能有不同的表现，可以根据表现优化预算分配，提高投资回报率（ROI）。\n',
                'campaigns': [
                    {
                        'campaignName': '移动-不锈钢桥架',
                        'campaignBidType': 0,
                        'equipmentType': 2,
                        'adgroups': [
                            {
                                'adgroupName': '核心词',
                            },
                            {
                                'adgroupName': '通用词',
                            },
                            {
                                'adgroupName': '电话词',
                            },
                            {
                                'adgroupName': '价格词',
                            },
                        ],
                        'budget': 345.67,
                    },
                    {
                        'campaignName': 'PC-不锈钢桥架',
                        'campaignBidType': 0,
                        'equipmentType': 1,
                        'adgroups': [
                            {
                                'adgroupName': '核心词',
                            },
                            {
                                'adgroupName': '通用词',
                            },
                            {
                                'adgroupName': '电话词',
                            },
                            {
                                'adgroupName': '价格词',
                            },
                        ],
                        'budget': 345.67,
                    },
                    {
                        'campaignName': '移动-镀锌桥架',
                        'campaignBidType': 0,
                        'equipmentType': 2,
                        'adgroups': [
                            {
                                'adgroupName': '核心词',
                            },
                            {
                                'adgroupName': '通用词',
                            },
                            {
                                'adgroupName': '电话词',
                            },
                            {
                                'adgroupName': '价格词',
                            },
                        ],
                        'budget': 345.67,
                    },
                    {
                        'campaignName': 'PC-镀锌桥架',
                        'campaignBidType': 0,
                        'equipmentType': 1,
                        'adgroups': [
                            {
                                'adgroupName': '核心词',
                            },
                            {
                                'adgroupName': '通用词',
                            },
                            {
                                'adgroupName': '电话词',
                            },
                            {
                                'adgroupName': '价格词',
                            },
                        ],
                        'budget': 345.67,
                    },
                ],
            },
            'taskInfo': {
                'taskId': 12308,
                'taskName': '5_12308',
                'taskType': 5,
                'taskStatus': 0,
                'addTime': '2024-09-11 14:58:07',
            },
            allowPreviousStep: true,
            previousStepField: 'ai-refresh-structure',
        },
    },
};

const aiBuildForm = {
    type: 1021,
    payload: {
        field: 'ai-build-form',
        cardParameters: {
            keyInformation: {
                userId: 31414, // 账户id，主键
                sessionId: '123', // 会话ID
                keyInformationId: 12345, // 关键信息ID（一期暂无该字段）
                business: ['不锈钢桥架', '镀锌桥架'], // 推广业务
                businessAdvantages: ['高品质不锈钢与镀锌桥架，防腐防锈', '性价比高，满足各类工程项目需求', 'xxxx'], // 业务优势
                mobileUrls: ['https://www.baidu.com'], // 移动落地页
                pcUrls: ['https://www.baidu.com'], // 计算机落地页
                keywords: ['电缆桥架', '梯式桥架', '槽式桥架', '不锈钢桥架', '镀锌桥架', '热浸锌槽式桥架', '桥架制造', '中山桥架'], // 推广关键词
                avoidBusiness: ['不投放xxx', 'xxxxx'], // 排除业务
                budget: 100.12, // 日预算
                bidType: 1, // 0-点击，1-转化
                ocpcBidType: 1, // 0-cpc,1-ocpc
                transTypes: [1, 2], // 优化目标
                ocpcBid: 31.3, // 转化出价
                images: [{ // 上传的图片经过处理后的信息
                    imageId: 1234,
                    picUrl: 'https://placehold.co/140x306', // 设置图库返回的molaUrl
                    rawPicUrl: 'https://placehold.co/140x306', // 设置图库返回的url
                    desc: '-', // 固定值
                    width: 123,
                    height: 345,
                }],
            },
            taskInfo: {
                taskId: 12308,
                taskName: '5_12308',
                taskType: 8,
                taskStatus: 0,
                addTime: '2024-09-11 14:58:07',
            },
            allowPreviousStep: true,
            previousStepField: 'ai-build-uploader',
        },
    },
};

const aiRefreshForm = {
    type: 1041,
    payload: {
        field: 'ai-refresh-form',
        cardParameters: {
            keyInformations: [
                {
                    userId: 31414, // 账户id，主键
                    sessionId: '123', // 会话ID
                    keyInformationId: 12345, // 关键信息ID（一期暂无该字段）
                    business: ['不锈钢桥架1', '镀锌桥架'], // 推广业务
                    businessAdvantages: ['高品质不锈钢与镀锌桥架，防腐防锈', '性价比高，满足各类工程项目需求', 'xxxx'], // 业务优势
                    mobileUrls: ['https://www.baidu.com'], // 移动落地页
                    pcUrls: ['https://www.baidu.com'], // 计算机落地页
                    keywords: ['电缆桥架', '梯式桥架', '槽式桥架', '不锈钢桥架', '镀锌桥架', '热浸锌槽式桥架', '桥架制造', '中山桥架'], // 推广关键词
                    avoidBusiness: ['不投放xxx', 'xxxxx'], // 排除业务
                    budget: 100.12, // 日预算
                    bidType: 1, // 0-点击，1-转化
                    ocpcBidType: 1, // 0-cpc,1-ocpc
                    transTypes: [1, 2], // 优化目标
                    ocpcBid: 31.3, // 转化出价
                    images: [{ // 上传的图片经过处理后的信息
                        imageId: 1234,
                        picUrl: 'https://placehold.co/140x306', // 设置图库返回的molaUrl
                        rawPicUrl: 'https://placehold.co/140x306', // 设置图库返回的url
                        desc: '-', // 固定值
                        width: 123,
                        height: 345,
                    }],
                },
                {
                    userId: 31414, // 账户id，主键
                    sessionId: '123', // 会话ID
                    keyInformationId: 12345, // 关键信息ID（一期暂无该字段）
                    business: ['甜品店2'], // 推广业务
                    businessAdvantages: ['蛋糕'], // 业务优势
                    mobileUrls: ['https://www.alili.com'], // 移动落地页
                    pcUrls: ['https://www.alili.com'], // 计算机落地页
                    keywords: ['甜品'], // 推广关键词
                    avoidBusiness: ['不投放xxx', 'xxxxx'], // 排除业务
                    budget: 100.12, // 日预算
                    bidType: 1, // 0-点击，1-转化
                    ocpcBidType: 1, // 0-cpc,1-ocpc
                    transTypes: [1, 2], // 优化目标
                    ocpcBid: 31.3, // 转化出价
                    images: [{ // 上传的图片经过处理后的信息
                        imageId: 1234,
                        picUrl: 'https://placehold.co/140x306', // 设置图库返回的molaUrl
                        rawPicUrl: 'https://placehold.co/140x306', // 设置图库返回的url
                        desc: '-', // 固定值
                        width: 123,
                        height: 345,
                    }],
                },
                {
                    userId: 31414, // 账户id，主键
                    sessionId: '123', // 会话ID
                    keyInformationId: 12345, // 关键信息ID（一期暂无该字段）
                    business: ['甜品店3'], // 推广业务
                    businessAdvantages: ['蛋糕'], // 业务优势
                    mobileUrls: ['https://www.alili.com'], // 移动落地页
                    pcUrls: ['https://www.alili.com'], // 计算机落地页
                    keywords: ['甜品'], // 推广关键词
                    avoidBusiness: ['不投放xxx', 'xxxxx'], // 排除业务
                    budget: 100.12, // 日预算
                    bidType: 1, // 0-点击，1-转化
                    ocpcBidType: 1, // 0-cpc,1-ocpc
                    transTypes: [1, 2], // 优化目标
                    ocpcBid: 31.3, // 转化出价
                    images: [{ // 上传的图片经过处理后的信息
                        imageId: 1234,
                        picUrl: 'https://placehold.co/140x306', // 设置图库返回的molaUrl
                        rawPicUrl: 'https://placehold.co/140x306', // 设置图库返回的url
                        desc: '-', // 固定值
                        width: 123,
                        height: 345,
                    }],
                },
                {
                    userId: 31414, // 账户id，主键
                    sessionId: '123', // 会话ID
                    keyInformationId: 12345, // 关键信息ID（一期暂无该字段）
                    business: ['甜品店4'], // 推广业务
                    businessAdvantages: ['蛋糕'], // 业务优势
                    mobileUrls: ['https://www.alili.com'], // 移动落地页
                    pcUrls: ['https://www.alili.com'], // 计算机落地页
                    keywords: ['甜品'], // 推广关键词
                    avoidBusiness: ['不投放xxx', 'xxxxx'], // 排除业务
                    budget: 100.12, // 日预算
                    bidType: 1, // 0-点击，1-转化
                    ocpcBidType: 1, // 0-cpc,1-ocpc
                    transTypes: [1, 2], // 优化目标
                    ocpcBid: 31.3, // 转化出价
                    images: [{ // 上传的图片经过处理后的信息
                        imageId: 1234,
                        picUrl: 'https://placehold.co/140x306', // 设置图库返回的molaUrl
                        rawPicUrl: 'https://placehold.co/140x306', // 设置图库返回的url
                        desc: '-', // 固定值
                        width: 123,
                        height: 345,
                    }],
                },
            ],
            taskInfo: {
                taskId: 12308,
                taskName: '5_12308',
                taskType: 9,
                taskStatus: 0,
                addTime: '2024-09-11 14:58:07',
            },
            allowPreviousStep: true,
            previousStepField: 'ai-build-identify-business',
        },
    },
};

const aiBuildSuccess = {
    type: 1026,
    payload: {
        field: 'ai-build-success',
        cardParameters: {
            taskInfo: {
                taskId: 12308,
                taskName: '5_12308',
                taskType: 5,
                taskStatus: 0,
                addTime: '2024-09-11 14:58:07',
            },
        },
    },
};

const aiBuildBusinessForm = {
    type: 1044,
    payload: {
        field: 'ai-build-business-form',
        cardParameters: {
            taskInfo: {
                taskId: 12308,
                taskName: '5_12308',
                taskType: 7,
                taskStatus: 0,
                addTime: '2024-09-11 14:58:07',
            },
            allowPreviousStep: true,
            previousStepField: 'ai-build-uploader',
        },
    },
};

const CommonTable = {
    type: 1031,
    payload: {
        field: 'common-table',
        'cardParameters': {
            'colConfig': [
                {
                    'key': 'region',
                    'colName': '地域',
                },
                {
                    'key': 'worth',
                    'colName': '价值（元）',
                },
            ],
            'rows': [
                {
                    'region': '黄浦区、海淀区',
                    'worth': '2',
                },
            ],
        },
    },
};

const ROITable = {
    type: 1032,
    payload: {
        field: 'roi-table',
        cardParameters: {},
    },
};

const feedIdeaPlugin = {
    type: 1028,
    payload: {
        field: 'idea-plugin',
        cardParameters: {},
    },
};

const diagnosisDate = {
    type: 1038,
    payload: {
        field: 'diagnosisConsumptionDropAdopt',
        cardParameters: {
            diagnosisStep: 'process',
            diagnosisInfo: {
                diagnosisType: 'diagnosisCustomDay',
                readyTimeRange: [
                    dayjs().subtract(1, 'month').format('YYYYMMDD'),
                    dayjs().format('YYYYMMDD'),
                ],
            },
        },
    },
};

const brandSDKLauncherBtn = {
    type: 1030,
    payload: {},
};

const feedAutoCreative = {
    type: 1035,
    payload: {},
};

const feedShortPlay = {
    type: 1036,
    payload: {},
};

const bjhPayPanel = {
    type: 1037,
    payload: {},
};


const cards = {
    CommonTable,
    ROITable,
    FCAppSelectorCard,
    FCLandPageSelectorCard,
    MigrationSelectorCard,
    campaignSelectorCard,
    lpCard,
    radioCard,
    transTypeCard,
    decisionCard,
    exampleCard,
    scheduleCard,
    modOptAndTipCard,
    materialCard,
    creativeTextsCheckGroupCard,
    creativeTextsGroupCard,
    creativeMaterialsCard,
    regionCard,
    productCard,
    crowdAgeCard,
    creativeVideosCard,
    recommendVideoCard,
    finishedCard,
    streamTextCard,
    singleLpCard,
    brandCard,
    lineChart,
    barChart,
    horizontalBar,
    pieChart,
    indicator,
    dataInsights,
    industryInsights,
    optimizeAdvice,
    searchReport,
    videoReport,
    baiduIndexReport,
    icgGoods,
    layout,
    appSelectorCard,
    FeedAppSelector,
    business,
    yimeiLandingPage,
    assetCard,
    negativeWordPacket,
    errorInfoCard,
    crowdCard,
    projectCard,
    projectPromptCard,
    carCornerCard,
    universalReportCard,
    textCard,
    marketPoint,
    CrowdTagRecommend,
    CrowdTagRecommendEdit,
    StackedLineRowsCard,
    guiTable,
    commonLineChart,
    guiFilterArea,
    assembleReport,
    oneLineChart,
    cdpBot,
    cbpAgent,
    icgGoodsMultiple,
    icgGoodsWatch,
    springActivity,
    liftBudgetCard,
    anchor,
    anchorItem,
    demandHotPointDescription,
    programCreativetextsSelector,
    customRenderCard,
    removedMarketPoint,
    marketGuide,
    clueValidityOptimizeCard,
    Protocol,
    marketingPointDiagnose,
    clueBusinessPoint,
    conflictNegative,
    FcAiMax,
    feedAssetCard,
    SingleProjectCard,
    FcProjectIncludeCampaign,
    FeedCampaignSelector,
    FeedAiMax,
    AiIncubator,
    timeSection,
    AiIncubatorEdit,
    invalidCluesTableSelector,
    dateRangeClickInput,
    diagnoseAgent,
    consumerInfo,
    aiBuildUploader,
    aiBuildStructure,
    aiRefreshStructure,
    ...feedCards,
    SelectedCampaignAdgroup,
    CreativeTextEtaRecommend,
    CreativeTextRsaRecommend,
    CreativeTextRsaEdit,
    CreativeTextEtaEdit,
    modCreativeImageCard,
    TargetCrowd,
    BindProject,
    ModCreativeVideosCard,
    CampaignSubmitCard,
    aiBuildPreview,
    aiRefreshPreview,
    aiRefreshForm,
    aiBuildForm,
    aiBuildSuccess,
    aiBuildBusinessForm,
    aiBuildSelectSource,
    ShopAgent,
    feedIdeaPlugin,
    brandSDKLauncherBtn,
    feedAutoCreative,
    diagnosisDate,
    feedShortPlay,
    bjhPayPanel,
};

module.exports = cards;
