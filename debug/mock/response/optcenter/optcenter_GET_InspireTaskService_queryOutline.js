

/* eslint-disable */
module.exports = function mock(params) {
    return {
        'data': [
            {
            "taskId": 13,
            "status": -1,
            "startTime": "2025-07-01 00:00:00",
            "endTime": "2025-09-30 23:59:59",
            "taskDuration": 30,
            "rewardLimit": 0,
            "rewardType": 1,
            "indicatorCurrent": 0,
            "taskIndicator": 0,
            "threshold": 0,
            "estimateConversions": 0,
            "competition": 0,
            "title": "呼朋唤友“荐”面有礼",
            "estimateIndicator": 0,
            "estimateData": {
                "estimateCoupon": 5000
            },
            "reportData": {
                "inviteUsers": [{   // 本次新增
                        "inviteCompany": "百度",    // 被邀请客户公司名（新）
                        "inviteOfficial": "张三",    // 被邀请公司负责人
                        "inviteOfficialPhone": 1234556,    // 被邀请公司负责人手机号
                        "city": "北京",
                        "inviteStatus": 0,     // 0 - 未邀请成功；1 - 邀请成功
                        "inviteFailReason": "活动时间未购买产品",   // 邀请失败原因
                        "hasPay": 1, // 0 - 未产生消费，1 - 产生消费
                    },{   // 本次新增
                        "inviteCompany": "百度",    // 被邀请客户公司名（新）
                        "inviteOfficial": "张三",    // 被邀请公司负责人
                        "inviteOfficialPhone": 1234556,    // 被邀请公司负责人手机号
                        "city": "北京",
                        "inviteStatus": 1,     // 0 - 未邀请成功；1 - 邀请成功
                        "inviteFailReason": "活动时间未购买产品",   // 邀请失败原因
                        "hasPay": 0, // 0 - 未产生消费，1 - 产生消费
                    },{   // 本次新增
                        "inviteCompany": "百度",    // 被邀请客户公司名（新）
                        "inviteOfficial": "张三",    // 被邀请公司负责人
                        "inviteOfficialPhone": 1234556,    // 被邀请公司负责人手机号
                        "city": "北京",
                        "inviteStatus": 1,     // 0 - 未邀请成功；1 - 邀请成功
                        "inviteFailReason": "活动时间未购买产品",   // 邀请失败原因
                        "hasPay": 1, // 0 - 未产生消费，1 - 产生消费
                    }]
            },
            "rewardsData": {
                "rewards": 1000
            },
            "extConfig": {
                "STOCK_CONFIG_TYPE_FIXED": 1,
                "maxInviteCustSize": 5,
                "reveiceTimes": 1,
                "STOCK_CONFIG_NON": 0,
                "sameCustLimit": 0,
                "STOCK_CONFIG_TYPE_BY_DATE": 2
            },
            "subTasks": [],
            "taskType": "INVITE"
        },
       {
                "taskId" : 9,
                "status" : -1,  // -1 - 未领取；0 - 已领取，但任务进度未开始；1 - 未达到任务门槛或仍未完成任务；2 - 已达到门槛但未达到任务上限；3 -已完成（未发放金币）；4-已完成（已发放金币）
                "startTime" : "2025-01-01",
                "userStartTime" : "2025-01-01",  // 任务领取时间。
                "userTaskUpdateTime": "2025-01-01",      // 任务奖励更新时间
                "endTime" : "2025-01-01",
                "taskDuration" : 14, // 任务周期。任务倒计时，通过此字段和userStartTime一起得出
                "title" : "我是任务标题",
                "subTitle" : "我是任务副标题",
                "desc" : "我是任务描述",
                "rewardLimit" : 1000, // 当前账户的奖励上限；金币总额，金币分母
                "rewards": 100,  // 当前任务获得的金币
                "rewardType" : 0,  // 0 - 金币，1 - 优惠券
                "indicatorCurrent" : 100, // 当前任务目标完成值：pc消费
                "taskIndicator" : 10000, // 任务目标金额：pc消费
                "estimateIndicator": 2000, // 预估目标完成值
                "threshold" : 100, // 任务门槛
                "subTasks" :[{   // 当前已完成任务。分母=数组数量，分子=status已完成的任务数量
                    "subTaskId" : 22,
                    "status" : 2
                }],
                "competition" : 0.2, // 小数，需要前端转成百分比。有多少同行采纳,
                "reportData" : { // 效果数据明细
                    "conversions": 4,
                    "cost": 80,
                    "click": 120
                },
                "estimateData": { // 预估数据明细
                    "conversions": 149999
                },
                "rewardsData": { // 奖励明细
                    "payCoins": 7,       // 消费奖励金币
                    "targetCoins": 20    // 目标达成奖励金币
                },
                // 任务扩展配置。每个任务需要的字段可能不一样
                "extConfig": { 
                    "stages" : [{
                        "indicator" : 200,   // 节点取值 - 比如，消费
                        "rewards" : 300,      // 节点奖励 - 比如，金币
                        "extraRewards" : 20  // 节点额外奖励。仅最后一个节点有数据
                     }],
                     "rewardCurrentStock" : 100, //奖池剩余金币数量
                     "rewardStock" : 200       //奖池总共金币数量
                }
            },
            {
                "taskId": 8,
                "status": -1,
                "startTime": "2025-05-01 00:00:00",
                "userStartTime": "2025-05-15 15:20:48",
                "userTaskUpdateTime": "2025-05-30 12:05:10",
                "endTime": "2025-06-30 23:59:59",
                "taskDuration": 50,
                "rewardLimit": 95,
                "rewards": 0,
                "rewardType": 1,
                "indicatorCurrent": 0.0,
                "taskIndicator": 1000.0,
                "threshold": 50.0,
                "estimateConversions": 0,
                "competition": 0.0,
                "title": "使用智能翻新奖5折优惠券",
                "estimateIndicator": 0.0,
                "estimateData": {},
                "reportData": {
                    "conversions": 0,
                    "cost": 0.0,
                    "click": 0,
                    "cashAfterAddTask": 0.0
                },
                "rewardsData": {
                    "rewardUserName": "searchlab",
                    "rewardUserId": 630152,
                    "rewards": 0
                },
                "extConfig": {
                    "STOCK_CONFIG_TYPE_FIXED": 1,
                    "rewardLimit": 100,
                    "reveiceTimes": 1,
                    "STOCK_CONFIG_NON": 0,
                    "sameCustLimit": 2,
                    "taskIndicator": 1000.0,
                    "STOCK_CONFIG_TYPE_BY_DATE": 2,
                    "threshold": 50.0
                },
                "subTasks": [],
                "taskType": "SPRINT_PAY"
            },
            {
                "taskId": 7,
                "status": -1,  // -1 - 未领取；0 - 已领取，但任务进度未开始；1 - 未达到任务门槛或仍未完成任务；2 - 已达到门槛但未达到任务上限；3 -已完成（未结算）；4-已完成，且已结算已发放奖励 5 - 未领取，但因库存不足不能领取 6 -已完成，且已结算但没能获取奖励
                "startTime": "2025-04-28",
                "userStartTime": "2025-04-28",  // 任务领取时间。
                "userTaskUpdateTime": "2025-04-28",      // 任务奖励更新时间
                "endTime": "2025-05-30",
                "taskDuration": 30, // 任务周期。任务倒计时，通过此字段和userStartTime一起得出
                "title": "我是任务标题",
                "desc": "",
                "rewardLimit": 5000, // 当前账户的优惠券（或者积分）奖励上限。（每个账户不一样）
                "taskIndicator": 100000, // 任务目标金额：消费 （每个账户不一样）
                indicatorCurrent: 120000,
                "rewardType": 1,  // 0 - 金币，1 - 优惠券，2 - 积分
                "competition": 0,
                "reportData": { 
                    "cost": 120000 // 当前消费
                },
                "estimateData": { // 预估数据明细
                    "estimateCoupon": 5000  // 预估可获得的优惠券金额（领取前，前卡展示的）
                },
                "rewards": 5000,
                "rewardsData": { 
                     "rewards": 5000,  // 当前任务获得的优惠券面值或者积分（根据 rewardType 来）。
                },
                // 任务扩展配置。每个任务需要的字段可能不一样
                "extConfig": { // 无
                    "threshold" : 50000 // 任务门槛
                },
                subTasks: []
            },
            {
                "taskId": 5,
                "status": 4,  // -1 - 未领取；0 - 已领取，但任务进度未开始；1 - 未达到任务门槛或仍未完成任务；2 - 已达到门槛但未达到任务上限；3 -已完成（未结算）；4-已完成，且已结算已发放奖励 5 - 未领取，但因库存不足不能领取 6 -已完成，且已结算但没能获取奖励
                "startTime": "2025-04-28",
                "userStartTime": "2025-04-28",  // 任务领取时间。
                "userTaskUpdateTime": "2025-04-28",      // 任务奖励更新时间
                "endTime": "2025-05-30",
                "taskDuration": 30, // 任务周期。任务倒计时，通过此字段和userStartTime一起得出
                "title": "我是任务标题",
                "desc": "",
                "rewardLimit": 5000, // 当前账户的优惠券（或者积分）奖励上限。（每个账户不一样）
                "taskIndicator": 100000, // 任务目标金额：消费 （每个账户不一样）
                indicatorCurrent: 80000,
                "rewardType": 1,  // 0 - 金币，1 - 优惠券，2 - 积分
                "competition": 0,
                "reportData": { 
                    "cost": 80000 // 当前消费
                },
                "estimateData": { // 预估数据明细
                    "estimateCoupon": 5000  // 预估可获得的优惠券金额（领取前，前卡展示的）
                },
                "rewards": 3000,
                "rewardsData": { 
                     "rewards": 3000,  // 当前任务获得的优惠券面值或者积分（根据 rewardType 来）。
                },
                // 任务扩展配置。每个任务需要的字段可能不一样
                "extConfig": { // 无
                    "threshold" : 50000 // 任务门槛
                },
                subTasks: []
            },
            {
                'taskId': 1,
                'taskType': 'STAGE_PAY_REWARD',
                'status': 1, // -1 - 未领取；0 - 已领取，但任务进度未开始；1 - 未达到任务门槛或仍未完成任务；2 - 已达到门槛但未达到任务上限；3 -已完成（包含已达到任务上限）
                'startTime': '2025-01-01',
                'userStartTime': '2025-01-20', // 任务领取时间。
                'endTime': '2025-01-01',
                'taskDuration': 14, // 任务周期。任务倒计时，通过此字段和userStartTime一起得出
                'rewardLimit': 182, // 当前账户的奖励上限；金币总额，金币分母
                'rewards': 100, // 当前任务获得的金币
                'indicatorCurrent': 700, // 当前任务目标完成值：pc消费
                'taskIndicator': 1000, // 任务目标金额：pc消费
                'threshold': 100, // 任务门槛
                reportData: {conversions: 4, cost: 340, click: 60},
                'estimateConversions': 123, // 预估转化。仅未领取状态下有
                // 当前已完成任务。分母=数组数量，分子=status已完成的任务数量
                'subTasks': [
                    {
                        'subTaskid': 22,
                        'status': 2,
                    }
                ],
                'competition': 0.2, // 小数，需要前端转成百分比。有多少同行采纳
                "extConfig": { 
                    "stages" : [{
                        "indicator" : 200,   // 节点取值
                        "rewards" : 20,     // 节点奖励
                    }, {
                        "indicator" : 600,   // 节点取值
                        "rewards" : 60,     // 节点奖励
                    }, {
                        "indicator" : 800,   // 节点取值
                        "rewards" : 80,     // 节点奖励
                    }, {
                        "indicator" : 1000,   // 节点取值
                        "rewards" : 100,     // 节点奖励
                        "extraRewards" : 50  // 节点额外奖励。仅最后一个节点有数据
                    }]
                }
            },
            {
                'taskId': 1,
                'taskType': 'STAGE_PAY_REWARD',
                'status': -1, // -1 - 未领取；0 - 已领取，但任务进度未开始；1 - 未达到任务门槛或仍未完成任务；2 - 已达到门槛但未达到任务上限；3 -已完成（包含已达到任务上限）
                'startTime': '2025-01-01',
                'userStartTime': '2025-01-20', // 任务领取时间。
                'endTime': '2025-01-01',
                'taskDuration': 14, // 任务周期。任务倒计时，通过此字段和userStartTime一起得出
                'rewardLimit': 9999, // 当前账户的奖励上限；金币总额，金币分母
                'rewards': 100, // 当前任务获得的金币
                'indicatorCurrent': 100, // 当前任务目标完成值：pc消费
                'taskIndicator': 500, // 任务目标金额：pc消费
                'threshold': 100, // 任务门槛
                reportData: {conversions: 4, cost: 340, click: 60},
                'estimateConversions': 123, // 预估转化。仅未领取状态下有
                // 当前已完成任务。分母=数组数量，分子=status已完成的任务数量
                'subTasks': [
                    {
                        'subTaskid': 22,
                        'status': 2,
                    }
                ],
                estimateData: {
                    click: 88,
                    conversions: 12,
                    cost: 666
                },
                'competition': 0.2, // 小数，需要前端转成百分比。有多少同行采纳
                "desc" : "经观察目前您所在行业同体量账户计算机端消费占比已高达:mark[9.3%]{status='warning'}，计算机端转化占比:mark[18%]{status='warning'}， 而您的账户暂未开启计算机端设备投放，计算机端流量存在较大的利用提升空间，完成本任务预估最高可获得:mark[65]{status='warning'}个转化或:mark[134]{status='warning'}个点击，基于当前账户过往消费情况和计算机端流量利用潜力，为您制定以下目标，并根据当前账户设置情况，为您推荐推荐以下建议", // 任务推荐-描述
            },
            {
                "taskId": 2,
                'taskType': 'PAYMENT_REWARD',
                "status": 4,
                "startTime": "2025-03-11 00:00:00",
                "userStartTime": "2025-03-13 00:00:00",
                "userTaskUpdateTime": "2025-03-13 18:57:53",
                "endTime": "2025-04-01 00:00:00",
                "taskDuration": 3,
                "rewardLimit": 1,
                "rewards": 0,
                "rewardType": 1,
                "indicatorCurrent": 1000.0,
                "taskIndicator": 0.0,
                "threshold": 0.0,
                "estimateConversions": 0,
                "competition": 0.0,
                "title": "限时续费，抢5折优惠券",
                "estimateIndicator": 0.0,
                "estimateData": {
                    "estimateCoupon": 650
                },
                "reportData": {},
                "rewardsData": {
                    coupon: 650,
                    isSameCid: true,
                },
                "extConfig": {
                    "couponConfig": [{
                        "charge": 1000,
                        "coupon": 300
                    }, {
                        "charge": 2000,
                        "coupon": 450
                    }, {
                        "charge": 4000,
                        "coupon": 550
                    }, {
                        "charge": 6000,
                        "coupon": 650
                    }],
                    descDetail: '首次续费最高可得价值:mark[**￥600**]{status="warning" size="small"}超值优惠',
                },
                "subTasks": []
            },
            // {
            //     'taskId': 10,
            //     'taskType': 'STAGE_PAY_REWARD',
            //     'status': -1, // -1 - 未领取；0 - 已领取，但任务进度未开始；1 - 未达到任务门槛或仍未完成任务；2 - 已达到门槛但未达到任务上限；3 -已完成（包含已达到任务上限）
            //     'startTime': '2025-01-01',
            //     'userStartTime': '2025-01-20', // 任务领取时间。
            //     'endTime': '2025-01-01',
            //     'taskDuration': 14, // 任务周期。任务倒计时，通过此字段和userStartTime一起得出
            //     'rewardLimit': 9999, // 当前账户的奖励上限；金币总额，金币分母
            //     'rewards': 100, // 当前任务获得的金币
            //     'indicatorCurrent': 100, // 当前任务目标完成值：pc消费
            //     'taskIndicator': 500, // 任务目标金额：pc消费
            //     'threshold': 100, // 任务门槛
            //     reportData: {conversions: 4, cost: 340, click: 60},
            //     'estimateConversions': 123, // 预估转化。仅未领取状态下有
            //     // 当前已完成任务。分母=数组数量，分子=status已完成的任务数量
            //     'subTasks': [
            //         {
            //             'subTaskid': 22,
            //             'status': 2,
            //         }
            //     ],
            //     estimateData: {
            //         click: 88,
            //         conversions: 12,
            //         cost: 666
            //     },
            //     'competition': 0.2, // 小数，需要前端转成百分比。有多少同行采纳
            //     "desc" : "经观察目前您所在行业同体量账户计算机端消费占比已高达:mark[9.3%]{status='warning'}，计算机端转化占比:mark[18%]{status='warning'}， 而您的账户暂未开启计算机端设备投放，计算机端流量存在较大的利用提升空间，完成本任务预估最高可获得:mark[65]{status='warning'}个转化或:mark[134]{status='warning'}个点击，基于当前账户过往消费情况和计算机端流量利用潜力，为您制定以下目标，并根据当前账户设置情况，为您推荐推荐以下建议", // 任务推荐-描述
            // },
            {
                'taskId': 12,
                'taskType': 'STAGE_PAY_REWARD',
                'status': -1, // -1 - 未领取；0 - 已领取，但任务进度未开始；1 - 未达到任务门槛或仍未完成任务；2 - 已达到门槛但未达到任务上限；3 -已完成（包含已达到任务上限）
                'startTime': '2025-01-01',
                'userStartTime': '2025-01-20', // 任务领取时间。
                'endTime': '2025-01-01',
                'taskDuration': 14, // 任务周期。任务倒计时，通过此字段和userStartTime一起得出
                'rewardLimit': 9999, // 当前账户的奖励上限；金币总额，金币分母
                'rewards': 100, // 当前任务获得的金币
                'indicatorCurrent': 100, // 当前任务目标完成值：pc消费
                'taskIndicator': 500, // 任务目标金额：pc消费
                'threshold': 100, // 任务门槛
                reportData: {conversions: 4, cost: 340, click: 60},
                'estimateConversions': 123, // 预估转化。仅未领取状态下有
                // 当前已完成任务。分母=数组数量，分子=status已完成的任务数量
                'subTasks': [
                    {
                        'subTaskid': 22,
                        'status': 2,
                    }
                ],
                estimateData: {
                    click: 88,
                    conversions: 12,
                    cost: 666
                },
                'competition': 0.2, // 小数，需要前端转成百分比。有多少同行采纳
                "desc" : "经观察目前您所在行业同体量账户计算机端消费占比已高达:mark[9.3%]{status='warning'}，计算机端转化占比:mark[18%]{status='warning'}， 而您的账户暂未开启计算机端设备投放，计算机端流量存在较大的利用提升空间，完成本任务预估最高可获得:mark[65]{status='warning'}个转化或:mark[134]{status='warning'}个点击，基于当前账户过往消费情况和计算机端流量利用潜力，为您制定以下目标，并根据当前账户设置情况，为您推荐推荐以下建议", // 任务推荐-描述
            },
            // {
            //     "taskId": 13,
            //     "taskType": "INVITE", // INVITE : 老带新邀请
            //     "status": 2,  // -1 - 未领取；0 - 已领取，但任务进度未开始；1 - 未达到任务门槛或仍未完成任务；2 - 已达到门槛但未达到任务上限；3 -已完成（未发放金币）；4-已完成（已发放金币）
            //     "startTime": "2025-07-01",
            //     "userStartTime": "2025-07-29",  // 任务领取时间。
            //     "userTaskUpdateTime": "2025-07-31",      // 任务奖励更新时间
            //     "endTime": "2025-09-30",
            //     "taskDuration": 14, // 任务周期。任务倒计时，通过此字段和userStartTime一起得出
            //     "title": "呼朋唤友“荐”面有礼",
            //     "desc": "邀请越多奖励越多",
            //     "rewardLimit": 1000, // 当前账户的奖励上限
            //     "rewards": 100,  // 当前任务获得的奖励
            //     "rewardType": 0,  // 0 - 金币，1 - 优惠券
            //     'subTasks': [
            //         {
            //             'subTaskid': 22,
            //             'status': 2,
            //         }
            //     ],
            //     "reportData": { // 效果数据明细
            //         "inviteUsers": [{   // 本次新增
            //             "inviteCompany": "百度",    // 被邀请客户公司名（新）
            //             "inviteOfficial": "张三",    // 被邀请公司负责人
            //             "inviteOfficialPhone": 1234556,    // 被邀请公司负责人手机号
            //             "city": "北京",
            //             "inviteStatus": 0,     // 0 - 未邀请成功；1 - 邀请成功
            //             "inviteFailReason": "活动时间未购买产品",   // 邀请失败原因
            //             "hasPay": 0, // 0 - 未产生消费，1 - 产生消费
            //         },{   // 本次新增
            //             "inviteCompany": "百度",    // 被邀请客户公司名（新）
            //             "inviteOfficial": "张三",    // 被邀请公司负责人
            //             "inviteOfficialPhone": 1234556,    // 被邀请公司负责人手机号
            //             "city": "北京",
            //             "inviteStatus": 1,     // 0 - 未邀请成功；1 - 邀请成功
            //             "inviteFailReason": "活动时间未购买产品",   // 邀请失败原因
            //             "hasPay": 0, // 0 - 未产生消费，1 - 产生消费
            //         },{   // 本次新增
            //             "inviteCompany": "百度",    // 被邀请客户公司名（新）
            //             "inviteOfficial": "张三",    // 被邀请公司负责人
            //             "inviteOfficialPhone": 1234556,    // 被邀请公司负责人手机号
            //             "city": "北京",
            //             "inviteStatus": 1,     // 0 - 未邀请成功；1 - 邀请成功
            //             "inviteFailReason": "活动时间未购买产品",   // 邀请失败原因
            //             "hasPay": 1, // 0 - 未产生消费，1 - 产生消费
            //         }]
            //     },
            //     "estimateData": { // 预估数据明细 
            //         "estimateCoupon": 5000  // 预估可获得的优惠券金额。在任务领取前、完成中时，会使用
            //     },
            //     "rewardsData": { // 奖励明细
            //         "coupon": 5000  // 任务完成后，实际所获得的优惠券面值
            //     },
            //     // 任务扩展配置。每个任务需要的字段可能不一样
            //     "extConfig": {
            //     },
            // },
        ],
        'expand': {},
        'status': 0,
        'errors': [],
    };
};

