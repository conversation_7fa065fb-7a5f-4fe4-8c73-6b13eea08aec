/* eslint-disable new-cap */
import {getFlag} from './getFlag';
import globalData from './globalData';

function getFeedFlag(key: string) {
    const flag = globalData.get('feedBasicInfo')?.bizAuth?.hags[key];
    return flag == null || !!flag;
}
function getFeedCustomerFlag(key: string) {
    const flag = globalData.get('feedBasicInfo')?.bizAuth?.customerHags[key];
    return flag == null || !!flag;
}

// 百青藤媒体定向升级
export const isFeedMediaTargetUpgrade = () => getFeedFlag('feed-media-targeting-upgrade');
// 百青藤媒体分类体系优化
export const getNewMediaCategoryFlag = () => getFeedFlag('feed-new-media-category');
// 信息流结构化投放产品化-ios
export const isIosGameUser = () => getFeedFlag('feed_ios_game_user');

// 应用调起
export const isProjectAppLink = () => getFeedFlag('feed_project_app_link');
// 销售线索结构化产品（教育医美）
export const getFeedSalesLead = () => getFeedFlag('feed-sales-lead');

// 信息流推广支持百度小程序作为落地页投放“付费阅读”
export const isFeedProgramPayToRead = () => getFeedFlag('feed-program-pay-to-read');
// 百度小程序-付费观剧
export const isFeedMiniProgramPayWatch = () => getFeedFlag('feed-mini-program-pay-watch');

// 计划和单元出价改版
export const getFeedBidUpgradeFlag = () => getFeedFlag('feed-planbid-upgrade');
// 百青藤激励明示
export const isFeedBqtPowerOptimizeOrigin = () => getFeedFlag('feed-bqt-power-optimize');
// 百青藤激励明示逻辑优化（为出价上移小流量名单feed-planbid-upgrade的子集）
export const isFeedBqtPowerOptimize = () => getFeedBidUpgradeFlag() && getFeedFlag('feed-bqt-power-optimize');

// 小程序营销目标新增微信小游戏
export const isMiniProgramChat = () => getFeedFlag('feed-wenxin-game-userlist');

// 转化资产
export const getFeedAssetFlag = () => getFeedFlag('conversion-tracking-remode-list');

// 预算下限提高到100元
export const isFeedBudgetLimit = () => getFeedFlag('feed-budget-limit');
// feed 程序化创意支持3:4竖版图片样式
export const isSupportFeedVerticalImage = () => getFeedFlag('feed-vertical-largepic-mt');

// 程序化互动图适配
export const getFeedProgFit = () => getFeedFlag('feed-prog-fit');

// feed 程序化创意支持互动图
export const isSupportFeedHudongImage = () => (getFeedFlag('feed-hudong2') && !getFeedFlag('feed-prog-fit'));
// 信息流创编是否支持磁贴组件
export const isSupportFeedImageTextCombine = () => getFeedFlag('feed-tile-plugin');
// 信息流创编是否支持磁贴组件
export const isSupportFeedProgrameIdeaHaokan = () => getFeedFlag('feed-ftype8');

// 大图，单图，三图，横版视频，竖版视频支持广告互动(点赞、分享、评论)
export const isFeedHudongcard = () => getFeedFlag('feed-hudongcard');

// 糯米二类电商
export const getFeedScecSmallFlowFlag = () => (getFeedFlag('scec_small_flow') || getFeedFlag('direct_sale_eshop'));

// 百青藤创意整合1.0
export const isFeedLeagueIdeaOpt = () => getFeedFlag('feed-league-idea-opt');

// 百青藤创意整合2.0
export const isFeedLeagueIdeaOptTwo = () => getFeedFlag('feed-league-idea-opt-two');

// 百青藤支持程序化开屏大图
export const isSupportFeedLeagueLargePic = () => getFeedFlag('feed-largepic-lianmeng');
// feed idmp 人群
export const isFeedAICrowdUser = () => getFeedFlag('feed-idmp-1');
// feed App行为新增对IOS支持
export const isIosSupportAppaction = () => getFeedFlag('feed-ios-support-appaction');

// 预算出价等操作提示
export const isFeedManageBidExcFlag = () => getFeedFlag('feed-chaotou-rule');
// AIGC辅助创意素材自动优化，控制自动文案优化、自动图片优化、自动视频优化开关可见，是feed-auto-idea-opti-text-image名单子集
export const getAutoIdeaOpt = () => getFeedFlag('feed-auto-idea-opti');
// 控制自动文案优化、自动图片优化开关可见
export const getAutoIdeaTextImageOpt = () => getFeedFlag('feed-auto-idea-opti-text-image');
// 教育行业账户不支持创建网站链接营销目标计划
export const isEducationNonsupportLink = () => getFeedFlag('feed-edu-not-support-link');
// 电商入口下线需求——允许新建电商店铺（基木鱼、健康商城）的白名单
export const isFeedMall = () => getFeedFlag('feed-duxiaodian-health-limit');

// 应用推广挂接结构化产品
export const getFeedStructuredFlag = () => getFeedFlag('feed-plan-structred');
// 信息流项目支持百度小程序
export const isFeedProjectBaiduProgram = () => getFeedFlag('feed_project_baidubox');

// ocpc/ocpm付费模式合并
export const isFeedOcpxMerge = () => getFeedFlag('feed-ocpx-merge');
// 是否为新的应用推广样式: 应用推广新增新游预约类型
export const isNewAppAppoint = () => getFeedFlag('feed-new-game');

// 智投项目
export const isFeedProject = () => getFeedFlag('feed_project');

export const isFeedUpgradeBidUser = () => getFeedFlag('feed-planbid-upgrade');

export const isFeedRegionResidentSetUser = () => getFeedFlag('feed-region-resident-set');

export const isFeedNaUrlUser = () => getFeedFlag('feed-na-url-user');
export const isFeedOCPCApiClueNaUrlUser = () => getFeedFlag('feed-ocpc-api-clue-naurl');
export const isFeedSupportWechatAndUrlUser = () => getFeedFlag('feed-support-wechat-and-url');
export const isFeedNaUrlExcluderUser = () => getFeedFlag('feed-na-url-exclude-user');

export const isDuanjuMigration = () => getFeedFlag('feed-duanju-jmy');

export const isSupportFeedUlink = () => getFeedFlag('feed-ulink');

export const isFeedDownloadRole = () => getFeedFlag('feed-download-type-app');

export const isMiniBidLimitForOCPCUser = () => getFeedFlag('feed-mini-bid-limit-for-ocpc');

export const isMiniBidLimitForOCPCProuser = () => getFeedFlag('feed-mini-bid-limit-for-ocpc-pro');

export const iqiyiHashs = [
    '#/feed/iqiyiReport',
    '#/feed/index~module=iqiyi',
    '#/feed/index~module=iqiyiApps',
    '#/feed/iqiyiReport'
];
export const isIqiyi = () => iqiyiHashs.some(hash => location.hash.includes(hash));
export const IS_FEED_PA_DOUBLE_LINK = () => getFeedFlag('pa_doublelink');
export const IS_FEED_MONITOR_URL_TYPE = () => getFeedFlag('monitorurl_type_customize');
export const IS_FEED_SEC_ECOM_DISTRICT = () => getFeedFlag('feed_scec_small_flow');
export const IS_MBAIDU_PC_ROLE = () => getFeedFlag('feed-mBaidu-pc') && !IS_FEED_SEC_ECOM_DISTRICT();
export const IS_FEED_VERTICAL_IMAGE_STYLE = () => getFeedFlag('feed-vertical-largepic-mt');
export const IS_FEED_WECHAT_MINI_PROGRAM = () => getFeedFlag('feed-wechat-mini-program');
export const IS_FEED_IDEA_REFORM = () => !isIqiyi();
export const IS_FEED_INTERACTIVE_STYLE = () => getFeedFlag('feed-hudongcard');
export const IS_DPLINK = () => getFeedFlag('feed-na-url-user');
export const IS_FEED_ACTION_CALL = () => getFeedFlag('feed-action-call');
export const IS_FEED_LEAGUE_IDEA_OPT_II = () => getFeedFlag('feed-league-idea-opt-two');
export const IS_FEED_TILE_PLUGIN = () => getFeedFlag('feed-tile-plugin');
export const IS_FEED_HAOKAN_INCREASE_STYLE = () => getFeedFlag('feed-ftype8');
export const IS_FEED_SUPPORT_ULINK = () => getFeedFlag('feed-ulink');
export const IS_FEED_PA_BANNER_FLOW = () => getFeedFlag('pa_banner_style');
export const IS_FEED_PA_DYNAMIC_TITLE = () => getFeedFlag('pa_dynamic_title');
export const IS_FEED_WECHAT_MINI_PROGRAM_II = () => IS_FEED_WECHAT_MINI_PROGRAM() && isFeedSupportWechatAndUrlUser();
export const IS_FEED_IQIYI_NAURL = () => getFeedFlag('feed-iqiyi-naurl');
export const IS_FEED_VIDEO_BTN = () => getFeedFlag('feed-video-button');
export const IS_FEED_ANDROID_JSURLGROUP = () => getFeedFlag('app_dyn_lp_white_role');
export const IS_FEED_VIDEO_FLOW = () => getFeedFlag('feed-ftype8');
export const IS_FEED_DOWNLOAD_TYPE = () => getFeedFlag('feed-download-type-app');
export const IS_FEED_EXPOSURE_ATTRIBUTION_USER = () => getFeedFlag('feed-exposure-attribution-user');
export const IS_FEED_OCPX_MERGE = () => getFeedFlag('feed-ocpx-merge');
export const IS_FEED_BQT_POWER_OPTIMIZE = () => getFeedFlag('feed-bqt-power-optimize');
// 信息流自定义流量支持开屏图片
export const IS_FEED_KP = () => getFeedFlag('feed-support-kaiping-pic');
export const IS_FEED_LEAGUE_IDEA_OPT = () => getFeedFlag('feed-league-idea-opt');
export const IS_FEED_ALBUM = () => getFeedFlag('feed-rotation-mt');
export const IS_FEED_BOUTIQUE = () => getFeedFlag('feed-quality-column-mt');
// CPC客户支持曝光监测URL
export const IS_FEED_CPC_EXPOSURE_URL = () => getFeedFlag('feed-cpc-exposure-url');
// 游戏行业强切应用SDK
export const isFeedCanNotUseAppApiAndroid = () => getFeedCustomerFlag('feed-can-not-use-app-api-android');
export const isMedicalFlag = () => getFeedFlag('xbox_medical');

// 一键起量预约生效时间
export const getLiftBudgetEffectiveTime = () => getFeedFlag('feed-lift-budget-appointment');

// 支持一键继承优质计划-仅账户层级
export const getInheritSelectAccountFlag = () => getFeedFlag('feed-inherit-plan');

// 一键继承优质计划-可选择账户层级&计划层级
export const getInheritSelectPlanFlag = () => getFeedFlag('feed-inherit-select-plan-user');

// 一键继承优质计划
export const getFeedInheritPlanFlag = () => getInheritSelectAccountFlag() || getInheritSelectPlanFlag();

// 继承优质计划
export const getInheritFlag = () => getFeedFlag('feed-inherit-flag');

// 继承由客户主动勾选改为系统优选继承
export const getSystemInheritFlag = () => getFeedFlag('feed-system-inherit');

// 百青藤创意整合2.0
export const isFeedLeagueIdeaOptII = () => getFeedFlag('feed-league-idea-opt-two');

// 二类电商支持产品页投放
export const isFeedSecEcomDistrict = () => getFeedFlag('feed_scec_small_flow');

// 手百pc流量明示小流量
export const isMbaiduPcRole = () => getFeedFlag('feed-mBaidu-pc') && !isFeedSecEcomDistrict();

// 信息流监测链接支持自定义
export const isFeedMonitorUrlType = () => getFeedFlag('monitorurl_type_customize');

// 大图，单图，三图，横版视频，竖版视频支持广告互动(点赞、分享、评论)
export const isFeedInteractiveStyle = () => getFeedFlag('feed-hudongcard');

// 闪投调起双链
export const isFeedPaDoubleLink = () => getFeedFlag('pa_doublelink');

// 闪投 - 动态标题
export const isFeedPaDynamicTitle = () => getFeedFlag('pa_dynamic_title');

// CPC客户支持曝光监测URL
export const isFeedCpcExposureUrl = () => getFeedFlag('feed-cpc-exposure-url');

// 闪投 - 横幅样式
export const isFeedPaBannerFlow = () => getFeedFlag('pa_banner_style');

// 爱奇艺支持naurl
export const isFeedIQiyiNaUrl = () => getFeedFlag('feed-iqiyi-naurl');
export const isFeedVideoButton = () => getFeedFlag('feed-video-button');

// android下载支持基木鱼落地页组
export const isFeedAndroidJsUrlGroup = () => getFeedFlag('app_dyn_lp_white_role');

// 曝光归因小流量名单
export const isFeedExposureAttributionUser = () => getFeedFlag('feed-exposure-attribution-user');

// 信息流推广支持微信小程序
export const isFeedWechatMiniProgram = () => getFeedFlag('feed-wechat-mini-program');

// 信息流推广支持复选微信小程序
export const isFeedWeChatMiniProgramII = () => isFeedWechatMiniProgram() && isFeedSupportWechatAndUrlUser();

export const isFeedProgFit = () => getFeedFlag('feed-prog-fit');
// 行动号召组件
export const isFeedActionCall = () => getFeedFlag('feed-action-call');

export const isFeedAudienceEducationAuth = (value: number | string) => getFeedFlag(`feed-education-value-${value}`);

export const isFeedAudienceLifeStageAuth = (value: number | string) => getFeedFlag(`feed-lifeStage-value-${value}`);

export const isFeedTargetTelecom = () => getFeedFlag('feed-telecom-intent');

// 信息流排除已转化人群-新增12个月和同客户中心快捷选项
export const isFeedExcludeTransCc = () => getFeedFlag('feed-support-exclude-trans-cc');

// 排除已转化过滤时间支持24个月和36个月
export const isFeedExclude2436Month = () => getFeedFlag('feed-exclude-24-36-month');

// 新建计划选择营销目标选择「应用推广」，推广设置选择「应用下载」或「应用调起」的情况下，可以选择【旅游库】
export const isFeedTravelOpen = () => getFeedFlag('feed-product-travel');

export const isFeedMedical = () => getFeedFlag('feed-product-medical');

// 付费观剧转化目标需强挂接短剧库
export const isDuanjuLimit = () => getFeedFlag('feed-duanju-pay-watch');

// 百青藤sdpa-游戏行业关联通路支持
export const isAppGameProductUser = () => getFeedFlag('feed_app_game_product_user');

// 升级产品库交互，单元支持继承自项目的产品
export const isFeedStructuredProduct = () => getFeedFlag('feed_structured_product_dev');

// 电商店铺营销目标支持产品库
export const isFeedEshopProductUser = () => getFeedFlag('feed-eshop-product');
// 服务直达
export const isFeedCplPlan = () => getFeedFlag('feed_cpl_plan_userlist_and_tradelist');

export const isFeedSaleLeadLive = () => getFeedFlag('feed-salelead-live-user');
// 智能调控
export const isFeedSmartControl = () => getFeedFlag('feed_aimax_smart_control_small_flow_fe');

// 原生广告审核状态披露
export const isFeedBjhAuditstatusPublish = () => getFeedFlag('feed-bjh-auditstatus-publish');
// 直播场景新增实况样式
export const isFeedLiveIdea = () => getFeedFlag('feed_idea_broadcast_live');
// 信息流项目百家号支持roi
export const isIAAShortPlay = () => getFeedFlag('feed-iaa-short-play-roi');

export const isFeedCommentInner = () => getFeedFlag('feed-comment-inner');
// 信息流百家号专属视频
export const isFeedBjhVideoUser = () => getFeedFlag('feed-bjh-video-ct');
// feed素材同步迁移轻舸用户
export const isFeedMaterialsHasMovedQingggeUser = () => getFeedFlag('feed_aix_user_move');
// 短剧行业通用版aimax名单
export const isBJHShortPlayAiMaxCommonUser = () => getFeedFlag('feed_aimax_playlet_user');
// 短剧aimax(包括通用版和行业版)
/* eslint-disable max-len */
export const isBJHShortPlayAIMaxUser = () => getFeedFlag('feed_aimax_playlet_user') || getFlag('feed-short-play-industry-aimax-user');
// 非行三运营单位禁止投放外循环短剧
export const getFeedForbiddenShortplayUser = () => getFeedFlag('feed_forbidden_shortplay_user');
// 仅支持百家号营销目标
export const getFeedBjhTongtouUser = () => getFeedFlag('feed-short-play-to-fc');
// 销售线索扩充双出价组合
export const isFeedSalesLeadTransTypeUpdate = () => getFeedFlag('feed-saleslead-transtype-update');
// 销售线索扩充【自建站】-综合线索收集
export const isFeedCustomMultiObjective = () => getFeedFlag('feed-custom-multi-objective');

// ocpx直接投放付费转化目标
export const isFeedOcpcApiSdkDirectPay = () => getFeedFlag('feed-ocpc-api-sdk-direct-pay');

// 线索API支持“登录+付费“、”登录+次留“、”付费“、”付费+ROI“转化目标投放
export const isFeedOcpcClueApiLogin = () => getFeedFlag('feed-ocpc-mini-program-deeplink');

// 付费转化目标按付费次数优化
export const isFeedOcpcPayCount = () => isFeedOcpcClueApiLogin() || isFeedOcpcApiSdkDirectPay();

// 混变LTV
export const isIaapLTVUser = () => getFeedFlag('feed-iaap-short-play-roi');
// 项目目标转化为付费短剧时必须选短剧产品
export const isFeedShortPlayMustBindProduct = () => getFeedFlag('feed-shortplay-must-bind-product');
// 法律行业业务泪目报告用户
export const isFeedBusinessPointReportUser = () => getFeedFlag('feed-business-point-report-user');
// 信息流智投项目支持小程序-微信小游戏
export const isFeedProjectWechatGame = () => getFeedFlag('feed-project-wechat-game');
// 应用推广链路设置标准化升级
export const isFeedAppUpdateUser = () => getFeedFlag('feed-app-update-user');
// 电商店铺营销目标
export const isFeedProjectEShop = () => getFeedFlag('feed-project-eshop');
// 百青藤微信小程序支持
export const isFeedBQTWechatMiniApp = () => getFeedFlag('bqt_wechat_minapp');
// 信息流预算撞线预告开放名单
export const isFeedBudgetCard = () => getFeedFlag('feed_budget_card');
export const isFeedMedicalProduct = () => getFeedFlag('feed-medical-product-required');
export const isFeedMedicalProductBlack = () => getFeedFlag('feed-medical-product-required-black');

// 百家号短剧简化场景
export const isBjhShortPlaySimplifiedSceneUser = () => getFeedFlag('feed-simple-shortplay');
// 多tabs模式 关闭浏览器立即删除db
export const getDeletedTabs = () => getFeedFlag('feed-new-tabs-delete');
// 开启多tabs模式，目前采用黑名单
export const isFeedAggregativeFlag = () => !getDeletedTabs();
// 招商加盟类目库
export const isZSJMUser = () => getFlag('feed_zsjm_product_user');
// 应用推广百青藤多商品
export const isMultiProductsUser = () => getFeedFlag('feed-bqt-app-multi-product');

// 批量修改推广身份名单，依赖基础名单 feed-bjh-native
export const isBatchAccountTypeModUser = () => getFeedFlag('feed_batch_account_type') && getFeedFlag('feed-bjh-native');

export const isFeedBjhVideoPluginUser = () => isFeedBjhVideoUser();
// ai视频修复工具
export const isFeedAIRepairVideoUser = () => getFeedFlag('feed-ai-repaire-video');
export const isFeedBroadcastVerticalVideoUser = () => getFeedFlag('feed-broadcast-vertical-video');

// 是否在列表中复制销售线索类单元或者计划时，强制绑定医疗库或者医疗产品
export const isFeedSalesLeadCopyWithMedicalLib = () => getFeedFlag('feed-medical-product-required-copy');
// 信息流评论管理升级1期
export const isFeedNegativeComment = () => getFeedFlag('feed_negative_comment');
// 百家号下的计划如果使用免费观剧或者是LTV相关的转化目标，允许展示一键起量
export const isFeedSupportBjhShortPlayLiftBudget = () => getFeedFlag('feed-simplify-lift-budget-user');

export const feedNewBroadcastFlag = () => getFeedFlag('feed-new-broadcast');
// 支持四品一械默开智能基建
export const isFeedSpyxTrade = () => getFeedFlag('feed-spyx-trade');
// 信息流创意列表行动建议卡片
export const isFeedCreativeListAdviceCard = () => getFeedFlag('feed-creative-list-advice-card');
// 应用调起链路新增支持直播场景
export const isAppLinkLive = () => getFeedFlag('feed-app-link-liveroom');
