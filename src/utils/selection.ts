import {TableProps, Toast} from '@baidu/one-ui';
import {without} from 'lodash-es';
import {MaterialListRowSelectionModeEnum, RowSelection} from '@/hooks/selection/pureConfig';

export function getSelectedIds({mode, value, isCheckAll}: RowSelection, listIds: number[]) {
    if (isCheckAll) {
        return mode === MaterialListRowSelectionModeEnum.positive ? listIds : without(listIds, ...value);
    }
    return mode === MaterialListRowSelectionModeEnum.positive ? value : without(listIds, ...value);
}


export function getSelectedCount({mode, value, isCheckAll}: RowSelection, totalCount: number) {
    // 如果是反选，选中数 = 总数 - 反选数
    // 全选 positive totalCount
    // 全选 !positive totalCount - value.length
    // 非全选 positive value.length
    if (isCheckAll) {
        return mode === MaterialListRowSelectionModeEnum.positive ? totalCount : totalCount - value.length;
    }
    return value.length;
}

export function getExcludeIds({mode, value}: RowSelection) {
    return mode !== MaterialListRowSelectionModeEnum.positive ? value : [];
}

export function toOneUIRowSelectionProps<T>(
    {selection, onSelectChange, selectAll, showSelectAll = false}: {
        selection: RowSelection;
        onSelectChange: (...args: any[]) => void;
        selectAll?: (...args: any[]) => void;
        showSelectAll?: boolean;
    },
    rows: T[],
    {getId, multiPageSelection = false, getCheckBoxEnabled, maxNumber}: {
        getId: (row: T) => number;
        multiPageSelection?: boolean;
        getCheckBoxEnabled?: (row: T) => boolean;
        maxNumber?: number;
    }
): Partial<TableProps['rowSelection']> {
    const listIds = rows.map(getId);
    const selectedIds = getSelectedIds(selection, listIds);
    const normalizedGetCheckBoxEnabled = getCheckBoxEnabled ?? getId; // 子母表方法会有区分
    function onChange(selectedRows: Array<number | string>) {
        if (maxNumber && selectedIds.length >= maxNumber && selectedRows.length >= selectedIds.length) {
            Toast.error({content: `最多选择${maxNumber}行`, showCloseIcon: false});
            return;
        }
        onSelectChange({selectedRows, ids: listIds, multiPageSelection});
    }
    return {
        type: 'checkbox',
        selectedRowKeys: selectedIds,
        getCheckboxProps: ((record: T) => {
            return {disabled: !normalizedGetCheckBoxEnabled(record), visible: !!normalizedGetCheckBoxEnabled(record)};
        }) as any,
        onChange,
        ...(showSelectAll ? {
            hideDefaultSelections: true,
            selections: selectAll && [{
                key: 'current',
                text: '选择当前页',
                onSelect: onChange as any,
            }, {
                key: 'all',
                text: '选择全部',
                onSelect: selectAll as any,
            }],
        } : {}),
    };
}

export function getOneTableLocale({isCheckAll, selectedCount, pageSize}: {
    isCheckAll: boolean;
    selectedCount: number;
    pageSize: number;
}) {
    return isCheckAll
        ? {checkAllTitle: '全部'}
        : selectedCount === pageSize ? {checkAllTitle: '当前页'} : undefined;
}
