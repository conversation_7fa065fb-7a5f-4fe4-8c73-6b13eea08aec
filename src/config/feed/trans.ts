import {DEEP_OPT_TYPE} from './bid';
import {checkSubjectType, SUBJECT_OBJECT_MAP} from './campaign';

// 转化目标枚举，内容比较多，后续用到慢慢补充
export const TRANS_TYPE_ENUM = {
    CONSULTATION_BTN_CLICK: 1, // 咨询按钮点击
    PHONE_BTN_CLICK: 2, // 电话按钮点击
    FORM_SUBMIT: 3, // 表单提交成功
    ACTIVE: 4, // 激活
    FORM_BTN_CLICK: 5, // 表单按钮点击
    APPOINT_CLICK: 6, // 预约（下载）按钮点击
    SERVICE_PAY_SUCCESS: 10, // 服务购买成功
    ORDER_SUBMIT_SUC: 14, // 订单提交成功
    THREE_SENTENCE_CONSULTATION: 17, // 三句话咨询
    LEAVE_CLUES: 18, // 留线索
    ONE_SENTENCE_CONSULTATION: 19, // 一句话咨询
    DEEP_TRANS: 20, // 关键页面浏览
    SIGN_IN: 25, // 注册
    PAY: 26, // 付费
    CUSTOMIZE: 27, // 客户自定义
    RETENTION: 28, // 次日留存
    SEVEN_RETENTION: 29, // 七日留存
    PHONE_CALL: 30, // 电话拨通
    WX_COPY_BTN_CLICK: 35, // 微信复制按钮点击
    APPLY: 41, // 申请
    CREDITS: 42, // 授信
    GOODS_PAY_SUCCESS: 45, // 商品下单成功
    ADD_CART: 46, // 加入购物车
    GOODS_COLLECT: 47, // 商品收藏
    GOODS_DETAIL_ARRIVED: 48, // 商品详情页到达
    LOGIN: 49, // 登录
    APPOINTMENT: 50, // 预约
    TO_SHOP: 56, // 到店
    ESHOP_LINK: 57, // 店铺调起
    WEIXIN_UP_BTN_CLICK: 67, // 微信调起按钮点击
    APP_UP: 71, // 调起
    TALK_RELATED_BUSINESSES: 72, // 聊到相关业务
    RETURN_VISIT_PHONE_CONNECTED: 73, // 回访-电话接通
    RETURN_VISIT_INFORMATION_CONFIRM: 74, // 回访-信息确认
    RETURN_VISIT_FOUND_INTENTION: 75, // 回访-发现意向
    RETURN_VISIT_HIGH_POTENTIAL_DEAL: 76, // 回访-高潜成交
    RETURN_VISIT_SINGLE_CUSTOMER: 77, // 回访-成单客户
    ESHOP_STAY: 78, // 店铺停留
    WX_ADD_FANS_SUC: 79, // 微信加粉成功
    LOAN: 89, // 放款
    ORDER_PAY_SUC: 90, // 商品支付成功
    EFFECTIVE_CONSULTATION: 92, // 有效咨询
    PAY_TO_READ: 93, // 付费阅读
    PAY_TO_WATCH: 118, // 付费观剧
    KEY_ACTION: 119, // 关键行为
    RETENTION_DAYS: 125, // 留存天数
    LIVE_SERVICE_PURCHASE: 131, // 直播间服务购买成功
    APP_SETUP: 71, // 应用调起
    REGISTERED: 141, // 挂号
    INTENTION_CONSULTION: 142, // 意向留联
    INTENTION_FORM: 143, // 意向表单
    LTV: 148, // LTV
    MULTIOBJECTIVE: 999999, // 多目标转化
};

// 获取深度优化方式 options
export function getDeepOptTypeOptions({transType, subject, transTypeConfig}) {

    const deepOptTypeOptions = [];

    const supportRoi = transTypeConfig?.canRoi;
    const supportRoiType = isSupportRoiType({transType, subject});

    if (supportRoi && !supportRoiType) {
        deepOptTypeOptions.push({
            label: '优化ROI',
            value: DEEP_OPT_TYPE.USE_ROI,
        });
    }

    if (supportRoiType) {
        deepOptTypeOptions.push({
            label: '优化转化价值',
            value: DEEP_OPT_TYPE.USE_ROI_TYPE,
        });
    }

    return deepOptTypeOptions;
}

// !IMPORTANT 该函数已废弃，保留函数，防止将来有人迁移老平台代码时再次使用该函数
// !废弃原因：新平台不再获取 transConfig
// 是否支持「ROI优化」
// 1. 当前所选转化目标在不在「支持ROI优化的转化目标」集合里
export function isSupportRoi(transType, subject, miniProgramType, transConfig) {
    const transConfigInSubject = getTransConfigInSubject({transConfig, subject, miniProgramType});
    const {supportRoiTransTypes} = transConfigInSubject;
    const {isAppSubject} = checkSubjectType(subject);
    if (isAppSubject) {
        const filterSuppRoiTransTypes = supportRoiTransTypes?.filter(
            i => ![TRANS_TYPE_ENUM.ORDER_PAY_SUC, TRANS_TYPE_ENUM.ORDER_SUBMIT_SUC].includes(i)
        ) ?? [];
        return filterSuppRoiTransTypes.includes(transType);
    }
    return supportRoiTransTypes?.includes(transType) ?? false;
}
// 是否支持「优化转化价值」
// 1. 百家号下付费观剧支持（小流量）
export function isSupportRoiType({transType, subject}) {
    const PayToWatch = 118;
    const {isBjhSubject} = checkSubjectType(subject);
    if (isBjhSubject) {
        return [PayToWatch].includes(transType);
    }
    return false;
}
// 获取当前营销目标下的转化追踪配置
export function getTransConfigInSubject({transConfig, subject, miniProgramType}) {
    if (subject === SUBJECT_OBJECT_MAP.program) {
        return transConfig?.[subject]?.miniProgramType?.[miniProgramType] ?? {};
    }
    return transConfig?.[subject] ?? {};
}
