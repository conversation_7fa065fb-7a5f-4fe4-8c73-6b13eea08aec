import {useState, useEffect, useRef, useMemo} from 'react';
import {uniq} from 'lodash-es';
import {request} from '@/utils/ajax';
import {parseJsonWithFallback} from '@/utils/json';
import {getErrorMsgByCode} from '@/utils/error';

export enum TaskType {
    INTELLIGENT_BUILD = 4,
    MATERIAL_GENERATION = 5,
    RECOGNIZE_BUSINESS = 8,
}

export enum TaskStatus {
    EXECUTING = 0,
    ALL_SUCCESS = 1,
    ALL_FAILED = 2,
    PARTIAL_SUCCESS = 3,
    QUEUING = 4,
}

export enum TaskStage {
    NONE = 0,
    // 智能搭建任务
    CAMPAIGN_CREATION = 401,
    ADGROUP_CREATION = 402,
    KEYWORD_CREATION = 403,
    CREATIVE_TEXT_CREATION = 404,
    CREATIVE_IMAGE_CREATION = 405,
    PROJECT_CREATION = 406,
    KXT_PROJECT_CREATION = 408,
    ADVANCED_STYLE_CREATION = 407,
    // 物料生成任务
    KEYWORD_INFO_GENERATION = 501,
    CREATIVE_TEXT_INFO_GENERATION = 502,
    CREATIVE_IMAGE_INFO_GENERATION = 503,
    RECOGNIZE_BUSINESS = 801,
    // 获取账户历史设置
    GET_ACCOUNT_PREFERENCE = 802,
    // 智能搭建任务-基于落地页搭建
    STAGE_URL_ANALYZE_CRAWLING = 901,
    STAGE_URL_ANALYZE_MERGING = 902,
    STAGE_URL_ANALYZE_EXTRACTING = 903,
}

const TaskStageMap = {
    [TaskStage.CAMPAIGN_CREATION]: '搭建计划：深入剖析营销项目，构建精准营销方案。',
    [TaskStage.ADGROUP_CREATION]: '搭建单元：细分单元，智能搭建。',
    [TaskStage.KEYWORD_CREATION]: '添加关键词：精选关键词，让信息传达更加精准。',
    [TaskStage.CREATIVE_TEXT_CREATION]: '添加创意：添加高质量的创意文案、营销图片。',
    [TaskStage.CREATIVE_IMAGE_CREATION]: '创建创意图片。',
    [TaskStage.PROJECT_CREATION]: '搭建项目：明确营销诉求，智能完成项目初始化。',
    [TaskStage.KXT_PROJECT_CREATION]: '搭建课效通项目：明确营销诉求，智能完成项目初始化。',

    [TaskStage.KEYWORD_INFO_GENERATION]: '关键词生成及筛选：根据市场研究数据，筛选出最具吸引力和相关性的关键词，并进行优化。',
    [TaskStage.CREATIVE_TEXT_INFO_GENERATION]: '优质创意生成：结合关键词和目标受众，参考同行优质案例，生成最佳创意。',
    [TaskStage.CREATIVE_IMAGE_INFO_GENERATION]: '创意图片尺寸自适应：根据投放设备自动调整创意图片尺寸，确保最佳展示效果。',
    [TaskStage.ADVANCED_STYLE_CREATION]: '生成高级样式：为营销方案绑定高级样式创意组，丰富创意展现从而吸引用户点击',


    [TaskStage.RECOGNIZE_BUSINESS]: '识别业务点：根据您的计划、落地页信息识别业务点',
    [TaskStage.GET_ACCOUNT_PREFERENCE]: '获取账户历史设置：根据您的历史设置情况，优化设置',

    [TaskStage.STAGE_URL_ANALYZE_CRAWLING]: '页面内容获取中，请稍候...',
    [TaskStage.STAGE_URL_ANALYZE_MERGING]: '解析页面信息中...',
    [TaskStage.STAGE_URL_ANALYZE_EXTRACTING]: '正在提取关键信息，即将完成',
};

enum ErrorLevel {
    CAMPAIGN = 3,
    ADGROUP = 5,
    CREATIVE = 7,
    KEYWORD = 11
}

interface TaskError {
    code: number;
    message: string;
    field?: string;
    errorLevel?: ErrorLevel;
    errorIndex?: number;
    campaignIndex?: number;
    adgroupIndex?: number;
}

export interface TaskResult {
    taskId: number;
    taskName: string;
    taskType: TaskType;
    taskStatus: TaskStatus;
    taskProgressDesc: string;
    taskStage: TaskStage;
    successTaskStages?: TaskStage[];
    content: string;
    addTime: string;
    doneTime: string;
    taskErrors?: TaskError[] | [];
}

interface TaskParams {
    taskIds: Array<TaskResult['taskId']>;
    taskType?: TaskResult['taskType'];
}

export async function fetchAiBuildTask({taskIds, taskType}: TaskParams) {
    return request<TaskResult[]>(
        'aurora/GET/AixAsyncTaskService/getTask',
        {taskType, taskIds},
        {toastOnFailure: false}
    );
}

const MAX_ERROR_COUNT = 5;
const getFormattedResult = (taskResult: TaskResult) => ({
    ...taskResult,
    content: parseJsonWithFallback(taskResult?.content, {}),
});
export const getFormattedError = (taskResult: TaskResult, error?: Error | null) => {
    const err = taskResult?.taskErrors?.map((e: any) => getErrorMsgByCode(e?.code, e?.message)) || [];
    return uniq(err.concat(error?.message || '')).filter(Boolean);
};

export function useAIBuildTaskInfo({taskId, taskType, timeout = 2000, maxErrorCount = MAX_ERROR_COUNT}:
    {taskId: number, taskType: TaskType, timeout?: number, maxErrorCount?: number}
) {
    const [taskResult, setTaskResult] = useState<TaskResult>({
        taskStatus: TaskStatus.EXECUTING,
        taskStage: TaskStage.CAMPAIGN_CREATION,
        taskErrors: [],
        content: '',
        taskId,
        taskName: '',
        taskType,
        successTaskStages: [],
        addTime: '',
        doneTime: '',
        taskProgressDesc: '',
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);
    const maxErrorCountRef = useRef(0);

    useEffect(() => {
        let isMounted = true;
        const pollTask = async () => {
            try {
                const [result] = await fetchAiBuildTask({taskIds: [taskId], taskType});
                if (isMounted) {
                    setTaskResult(result);
                    setLoading(false);
                    setError(null);
                    if (![TaskStatus.EXECUTING, TaskStatus.QUEUING].includes(result.taskStatus)) {
                        // 任务不再进行中或者不在排队中，停止轮询
                        return;
                    }
                }
            } catch (err: any) {
                if (isMounted) {
                    maxErrorCountRef.current += 1;
                    const message = err?.errors?.[0]?.message || '未知错误';
                    setError(new Error(message));
                }
                if (maxErrorCountRef.current >= maxErrorCount) {
                    setLoading(false);
                    return;
                }
            }

            if (isMounted) {
                // 继续轮询
                setTimeout(pollTask, timeout);
            }
        };

        pollTask();
        return () => {
            isMounted = false;
        };
    }, [taskId, taskType, timeout, maxErrorCount]);

    // 从taskResult中提取出已完成的steps（包含已完成和进行中）  以及当前的step
    const {
        taskStage,
        successTaskStages = [],
    } = taskResult;

    const steps = useMemo(() => {
        return [...successTaskStages, taskStage].filter(Boolean).map(s => ({
            id: s,
            content: TaskStageMap[s as keyof typeof TaskStageMap],
        }));
    }, [taskStage, successTaskStages]);

    const currentStep = steps.findIndex(s => s.id === taskStage);
    const formattedResult = useMemo(() => getFormattedResult(taskResult), [taskResult]);
    const errorMessages = useMemo(() => getFormattedError(taskResult, error), [taskResult, error]);

    const isSuccess = taskResult.taskStatus === TaskStatus.ALL_SUCCESS;
    const isPartialSuccess = taskResult.taskStatus === TaskStatus.PARTIAL_SUCCESS;
    const isPassed = isSuccess || isPartialSuccess;
    const isFailed = taskResult.taskStatus === TaskStatus.ALL_FAILED || maxErrorCountRef.current >= maxErrorCount;

    return {taskResult: formattedResult, loading, errorMessages, steps, currentStep, isPassed, isFailed};
}


export async function updateTaskFeedback(params: {
    taskId: number;
    feedback: Record<string, any>;
}) {

    const {taskId, feedback} = params;

    return request('aurora/MOD/AixAsyncTaskService/updateTaskContentFeedBack', {
        feedBackTaskTypes: [{
            taskId,
            feedBackInfo: feedback,
        }],
    }, {
        toastOnFailure: false,
    });
}
