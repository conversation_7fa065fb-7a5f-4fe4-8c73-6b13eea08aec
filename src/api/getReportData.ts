import {flatMap, uniq, concat, map, isEmpty} from 'lodash-es';
import {MarsproReportToken} from 'commonLibs/types/report/base';
import idType from 'commonLibs/config/idType';
import {request} from '@/utils/ajax';
import {
    FetchParams, ReportDataResponse, ReportResponseItem,
    FetchConfigParams, ReportConfigResponse,
} from '@/interface/report';

export async function fetchReportConfig(params: FetchConfigParams) {
    return request<ReportConfigResponse>('marsPro/GET/ReportConfigService/getReportConfig', params);
}

export async function fetchReportData<T = Record<string, never>>(params: FetchParams) {
    // 解决token中有列的排序/筛选，但是自定义列中不选此列导致marspro报错的情况
    const sortAndFilterColumns = [
        ...(params.sorts ? params.sorts.map(i => i.column) : []),
    ];
    const paramsWithNewColumns = {
        ...params,
        columns: uniq([...sortAndFilterColumns, ...params.columns]),
    };
    return request<ReportDataResponse<T & ReportResponseItem>>(
        'marsPro/GET/ReportDataService/getReportData', paramsWithNewColumns);
}

export async function fetchSplitReportData<T = Record<string, never>>(params: FetchParams) {
    const sortAndFilterColumns = [
        ...(params.sorts ? params.sorts.map(i => i.column) : []),
    ];
    const paramsWithNewColumns = {
        ...params,
        columns: uniq([...sortAndFilterColumns, ...params.columns]),
    };
    return request<ReportDataResponse<T & ReportResponseItem>>(
        'marsPro/GET/ReportDataService/getSplitReportData', paramsWithNewColumns);
}

export function normalizeKeywordRankData([response, campaignInfo]: any) {
    const campaignInfoRows = campaignInfo.rows || [];
    return {
        ...response,
        rows: response.rows.map(row => {
            return {
                ...row,
                ...campaignInfoRows.find(v => v.campaignId === row.campaignId),
            };
        }),
    };
}

export const realtimeReportKeywordCampaignFields = [
    'campaignName',
    'campaignId',
    'scheduleTemplateName',
    'schedule',
    'schedulePriceFactors',
    'marketingTargetId',
    'shopType',
    'bidPrefer',
    'priceRatio',
    'pcPriceRatio',
    'adType',
    'campaignBidType',
    'campaignAutoOptimizationStatus',
    'equipmentType',
    'sharedBudgetName',
    'sharedBudgetId',
    'sharedBudget',
    'type',
    'budgetType',
    'operations',
    'transPrice',
    'status',
    'pause',
    'businessPointId',
    'businessPointName',
    'budget',
    'reOnlineReasons',
    'priceStrategy',
    'negativeWords',
    'exactNegativeWords',
    'negativeWordPackets',
    'indepdentNegativeWords',
    'indepdentExactNegativeWords',
    'scheduleTemplateId',
    'storePageInfos',
    'campaignOcpcBidType',
    'campaignOcpcBid',
    'campaignPayTimesStatus',
    'campaignDeviceBidStatus',
    'campaignBid',
    'campaignTransTypes',
    'campaignCvSources',
    'campaignDeepTransTypes',
];


// 实时报告-关键词拉取计划物料列表 - 按计划Id筛选
function fetchCampaignList(campaignIdList: number[], payload: any) {
    const {
        userId,
        startDate: startTime,
        endDate: endTime,
    } = payload;
    return request('puppet/GET/MaterialQueryFunction/getMaterialCampaignList', {
        token: MarsproReportToken.material,
        reportType: 1620300,
        userId,
        ids: [userId],
        idType: idType.USER_LEVEL,
        startTime,
        endTime,
        limit: [0, campaignIdList.length],
        fields: realtimeReportKeywordCampaignFields,
        fieldFilters: [
            {
                field: 'campaignId',
                op: 'in',
                values: campaignIdList,
            },
        ],
    });
}

// 关键词实时消费数据报表 - 不细分
export function fetchKeywordRealtimeRankData(payload: any) {
    return fetchReportData(payload).then(response => {
        const rows: any[] = response.rows;
        const campaignIdList = uniq(map(rows, row => row.campaignId));
        if (isEmpty(campaignIdList)) {
            return Promise.resolve(response);
        }
        return fetchCampaignList(campaignIdList, payload)
            .then((campaignInfo: unknown) => normalizeKeywordRankData([response, campaignInfo]));
    });
}

// 关键词实时消费数据报表 - 细分
export function fetchKeywordRealtimeRankSplitData(payload: any) {
    return fetchSplitReportData(payload).then(response => {
        const rows: any[] = response.rows;
        const campaignIdList = uniq(
            flatMap(
                rows,
                row => concat((row.subRows || []).map(subRow => subRow.campaignId), row.parentRow.campaignId)
            )
        );
        if (isEmpty(campaignIdList)) {
            return Promise.resolve(response);
        }

        return fetchCampaignList(campaignIdList, payload)
            .then((campaignInfo: unknown) => normalizeKeywordRankData([response, campaignInfo]));
    });
}

export async function fetchReportConfigAndData({reportDataParams, reportConfigParams}: {
    reportDataParams: FetchParams; reportConfigParams: FetchConfigParams;
}) {
    const config = await fetchReportConfig(reportConfigParams);
    const data = await fetchReportData(reportDataParams);
    return {config, data};
}

export async function fetchCompareReportData(params: FetchParams) {
    return request<ReportDataResponse>('marsPro/GET/ReportDataService/getCompareReportData', params);
}

export async function apiData({api, params}) {
    return request(api, params);
}

async function fetchAccountLevelInfo() {
    const params = {
        accountFields: [
            'budget',
            'budgetType',
            'activeTimeout',
            'regionTarget',
            'userStat',
            'budgetOfflineTime',
            'balance',
            'balanceSum',
            'pcBalance',
            'mobileBalance',
            'frameStat',
            'frameType',
            'frameYear',
            'consumeDays',
            'pcConsumeDays',
            'mobileConsumeDays',
            'offlineTimeData',
        ],
    };
    const result = await request('puppet/GET/MaterialFunction/getAccountInfo', params);
    return result && result[0] || {};
}

export const SdkToken = '409f4eaa-ad19-464f-ac33-ab8b480asdds';
export const FcToken = '409f4eaa-ad19-464f-ac33-ab8b480asdds';

export const FcListToken = 'c18ea5fb-830b-417c-99c2-e03bc6a98120';

async function fetchtRealTimeOverview(indicator: string, filters?: any[]) {
    const params = {
        token: SdkToken,
        column: indicator,
        ...(filters && filters.length > 0 ? {filters} : {}),
    };
    const result = await request('marsPro/GET/MinuteRealtimeReportDataService/getMinuteRealTimeReportData', params);
    const {todaySummary, minuteDetails} = result || {todaySummary: {}, minuteDetails: []};
    const {click = 0, cost = 0} = todaySummary;
    return {
        minuteDetails,
        click,
        cost,
    };
}


export function fetchRealTimeAccountAndOverview(requestParams?: {filters?: any[]}) {
    const filters = requestParams?.filters;
    return Promise.all([
        fetchtRealTimeOverview('click', filters),
        fetchtRealTimeOverview('cost', filters),
        fetchAccountLevelInfo(),
    ]);
}

export enum QueryWordIncludeStatus {
    INCLUDE = 1,
    EXCLUDE = 0,
}
interface KerwordMatch {
    keyword: string;
    status: QueryWordIncludeStatus;
}
interface IncludeResponse {
    match: KerwordMatch[];
}
// 查询搜索词是否收录在指数里
export async function fetchQueryWordsIsInclude(rows: any[]) {
    const keyword = rows.map(i => i.queryWord);
    if (!keyword.length) {
        return [];
    }
    const data = await request<IncludeResponse>('index-api/GET/IndexApiService/checkKeywords', {keyword});
    return rows.map(r => ({
        querword: r.queryWord,
        status: data.match?.find(i => i.keyword === r.queryWord)?.status || QueryWordIncludeStatus.EXCLUDE,
    }));
}


export interface customReportInfo {
    isPinned: boolean;
    pinnedTime: string;
    reportName: string;
    reportType: number;
    reportId: string;
}

interface CustomReportResponse {
    customReports: customReportInfo[];
}

const customReportTopLength = 5;
export async function fetchTopCustomReports({weirwoodIgnored} = {weirwoodIgnored: false}) {
    const data = await request<CustomReportResponse>('marsPro/GET/CustomReportService/getCustomReports', {
        token: SdkToken,
        customReportTypes: ['DASHBOARD'],
        appId: 84,
        targetAppIds: [0, 1, 61, 84, 841, 842],
    }, {weirwoodIgnored});
    const {customReports = []} = data;
    const topCustomReportList = customReports.filter(item => item.isPinned).sort((
        a: customReportInfo, b: customReportInfo) => {
        return new Date(b.pinnedTime).getTime() - new Date(a.pinnedTime).getTime();
    }).slice(0, customReportTopLength);
    return topCustomReportList;
}
