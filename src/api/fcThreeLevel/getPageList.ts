import {AppPlatform} from '@/dicts/appInfo';
import {FC_MARKET_TARGET} from '@/dicts/marketingTarget';
import {LP_SHOW_TYPE} from '@/dicts/showType';
import {IAppPage, IJmyPage} from '@/interface/landpage';
import {request} from '@/utils/ajax';
import {getAgentList} from '@/api/getAgentList';
import {LandingPageCard} from '../landpage';


enum platFormKey {
    /**
     * 基木鱼平台
     */
    JIMUYU = 1,
    /**
     * 斗金平台
     */
    DOUJIN = 2,
    /**
     * 医美
     */
    MEDICAL = 3
}


enum IdTypeConfigEnum {
    ADGROUP = 5,
    CHANNEL = 56,
    APP_STORE = 57,
}
const productAppId = {
    duStore: 5,
};

const maxPageCount = 500;


const marketIdToPlatform = {
    [FC_MARKET_TARGET.WEB]: [platFormKey.JIMUYU, platFormKey.DOUJIN],
    [FC_MARKET_TARGET.CPQL]: [platFormKey.JIMUYU, platFormKey.MEDICAL],
};


interface GetApiParams {
    marketingTargetId: FC_MARKET_TARGET;
    showType: LP_SHOW_TYPE;
    searchValue: string;
    platform?: AppPlatform;
    channelId?: number;
    selectUrl?: string;
    filter: {
        value: string;
        type: string;
    };
}

// 以营销目标来梳理下参数，虽然会有一些重复代码，但是方便看逻辑
// 代码来源 packages/common-libs/src/components/dropdownUrlSelect/utils.ts
export async function fetchFcPageList({
    marketingTargetId,
    filter,
    searchValue,
    showType,
    platform,
    channelId,
    selectUrl,
}: // isAiShoppingTrade,
GetApiParams): Promise<LandingPageCard[]> {
    const {value: filterValue, type: filterType} = filter;
    // 应用推广
    // if (marketingTargetId === FC_MARKET_TARGET.APP) {
    //     return request<{
    //         pageList: IAppPage[];
    //     }>(
    //         'puppet/GET/NovelPromotionSiteFunction/getAppNewNovelPromotionInfo',
    //         {
    //             // 这里由于只有新建会用到卡片， 所以就不在这里传单元ids 和 单元层级了
    //             id: channelId,
    //             // 这里只处理新建的idType，编辑的时候没有下拉框，所以不会走这里
    //             idType:
    //                 platform === AppPlatform.ANDROID
    //                     ? IdTypeConfigEnum.CHANNEL
    //                     : IdTypeConfigEnum.APP_STORE,
    //             limit: maxPageCount,
    //             selectUrl,
    //             fieldFilter: {
    //                 field: 'pageName',
    //                 op: 'like',
    //                 values: [searchValue],
    //             },
    //         }
    //     ).then(({pageList}) => {
    //         return {
    //             pageList: pageList.map(item => ({
    //                 ...item,
    //                 pageUrl: item.onlineUrl,
    //                 picture: item.thumbnail,
    //                 prompt: `选择${item.pageName}落地页（${item.onlineUrl}）`,
    //             })),
    //         };
    //     });
    // }

    // // 爱采购优先级不区分营销目标，通过行业和名单区分是否调用爱采购接口
    // if (isAiShoppingTrade) {
    //     return request<{
    //         pageList: IAIShoppingPage;
    //         /**
    //          * 没有落地页时，新建的落地页的url
    //          */
    //         aiShoppingUrl: string;
    //     }>(
    //         'lightning/GET/UrlPromotionSiteService/getAiShoppingPromotionPages',
    //         {
    //             searchFields: {
    //                 pageName: searchValue,
    //             },
    //             limit: maxPageCount,
    //         }
    //     );
    // }

    return request<{ pageList: IJmyPage[] }>(
        'lightning/GET/UrlPromotionSiteService/getJmyPromotionPage',
        {
            searchFields: {
                ...(filterValue ? {
                    [filterType]: filterValue,
                } : {}),
                productAppIdNotIn: [productAppId.duStore],
            },
            showType,
            limit: maxPageCount,
            pageLimit: [0, maxPageCount],
            platformIds:
                marketIdToPlatform[
                    marketingTargetId as keyof typeof marketIdToPlatform
                ] || [],
            queryThumbnail: true,
        }
    ).then(({pageList = []}) => {
        return pageList.map(item => ({
            ...item,
            pageId: item.pageId || item.drugId || `${item.pageName}_${item.showType}`,
            pageUrl: item.onlineUrl,
            picture: item.thumbnail,
            prompt: `选择${item.pageName}落地页（${item.onlineUrl}）`,
        }));
    });
}


export async function getAllPageList(params) {
    return Promise.all([
        fetchFcPageList(params),
        getAgentList(params),
    ]);
}
