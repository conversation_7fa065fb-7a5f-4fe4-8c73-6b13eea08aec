/*
 * @file: 获取商家智能体列表
 * <AUTHOR>
 * @date 2024/11/12
 */

import {request} from '@/utils/ajax';
import {BcpAgentInfoListItem} from '@/interface/shopAgent';

interface getSameCidAgentListResult {
    totalNum: number;
    pageList: BcpAgentInfoListItem[];
}
interface getSameCidAgentListProps {
    query?: string | number;
}

// 查询同账户/同主体下的商家智能体列表
export async function getSameCidAgentList({query}: getSameCidAgentListProps = {}) {
    const params = {};
    // 后端要求query需要为number、需要做过滤处理;
    // userId目前最长为7位，超过10位就不加入userId参数了
    if (query && query.toString().length <= 10) {
        if (Number.isFinite(Number(query))) {
            params.userId = Number(query);
        }
    }

    const {pageList} = await request<getSameCidAgentListResult>(
        'lightning/GET/AgentService/getSameCidAgentList',
        params,
    );
    return pageList;
}

export interface AgentListItem {
    pageId: number;
    pageName: string;
    onlineUrl: string;
    thumbnail: string;
    storeId?: number;
}
interface getAgentListResult {
    totalNum: number;
    pageList: AgentListItem[];
}
interface getAgentListProps {
    filter: {
        type: string;
        value: string;
    };
}

export async function getAgentList({filter}: getAgentListProps) {
    const {value: filterValue, type: filterType} = filter;
    const params = {
        limit: [1, 10],
        sortBy: {field: 'updateTime', isAsc: false},
    };
    if (filterValue) {
        params.predicates = {
            field: filterType,
            op: 'like',
            values: [filterValue],
        };
    }
    const {pageList = []} = await request<getAgentListResult>(
        'lightning/GET/AgentService/getAgentList',
        params,
    );
    return pageList;
}
