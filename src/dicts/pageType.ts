
export enum AdModuleType {
    PromptCenter = 'promptCenter',
    ManageCenter = 'manageCenter',
    ToolsCenter = 'toolsCenter',
    DataCenter = 'dataCenter',
    OptCenter = 'optCenter',
    AssetsCenter = 'assetsCenter',
    PromotionMain = 'promotionMain',
    Overview = 'overview',
    FlashLink = 'flashLink',
}

export enum ChatType {
    RestartChat = 'restartChat',
    KeepOnChat = 'keepOnChat',
}

export enum PageType {
    /**
     * 测试用，线上不露出
     */
    DEV_RichTextEditor = 'DEV_RichTextEditor',

    ChatPage = 'chatPage',
    ProjectList = 'projectList',
    CampaignList = 'campaignList',
    AdgroupList = 'adgroupList',
    SegmentList = 'segmentList',
    PictureList = 'pictureList',
    VideoList = 'videoList',
    MarketPointList = 'marketPointList',
    KeywordList = 'keywordList',
    QueryWordList = 'queryWordList',
    CreativeTextList = 'creativeTextList',
    CreateProject = 'createProject',
    CreateProjectV2 = 'CreateProjectV2',
    EditProjectV2 = 'EditProjectV2',
    EditProject = 'editProject',
    AiBuildProject = 'aiBuildProject',
    CreateCPLCampaign = 'createCPLCampaign',
    CreateLiteProject = 'CreateLiteProject',
    EditLiteProject = 'EditLiteProject',
    GUIReport = 'GUIReport',
    ReportWanHuaTong = 'customReport',
    CreativeComponent = 'CreativeComponent',
    // tools and assets
    AssetsOverview = 'assetsOverview',
    ToolsOverview = 'toolsOverview',
    Tools_NegativePackets = 'negativePackets',
    Tools_ScheduleTemplates = 'scheduleTemplates',
    Tools_PriceStrategy = 'priceStrategy',
    Tools_SharedBudget = 'sharedBudget',
    Tools_ImageLibrary = 'imageLibrary',
    Tools_VideoLibrary = 'videoLibrary',
    Tools_Brands = 'brands',
    Tools_Bjh_Plugin = 'bjhPlugin',
    Tools_EventAsset = 'eventAsset',
    Tools_AppCenter = 'appCenter',
    Tools_History = 'history',
    Tools_TaskRecord = 'taskRecord',
    Tools_KR = 'kr',
    Tools_StyleAnalyse = 'styleAnalyse',
    Tools_Pna = 'pna',
    Tools_Track = 'track',
    Tools_BusinessShield = 'businessShield',
    Tools_Adpreview = 'adpreview',
    Tools_AutoRules = 'autoRules',
    Tools_AutoAdviceRules = 'autoAdviceRules',
    Tools_KeywordPreCheck = 'keywordPreCheck',
    Tools_NewDiagnosis = 'newDiagnosis',
    Tools_ClueRfq = 'clueRfq',
    Tools_AbnormalFluctuationDiagnose = 'abnormalFluctuationDiagnose',
    Tools_InvalidClueDiagnose = 'invalidClueDiagnose',
    Tools_PluginManage = 'pluginManage',
    Tools_PaBatchTools = 'paBatchTools',
    Tools_TargetPackage = 'targetPackage',
    Tools_IntentWordManage = 'intentWordManage',
    Tools_MediaManage = 'mediaManage',
    Tools_ideaWordsBag = 'IdeaWordsBag',
    Tools_ReviewManage = 'reviewManage',
    Tools_IDMP = 'idmp',
    Tools_SuperManager = 'superManager',
    Tools_FcCrowds = 'fcCrowds',
    Tools_ProductBatch = 'productBatch',
    AIBuild = 'AIBuild',
    AIBuildRefresh = 'AIBuildRefresh',
    AccountReport = 'accountReport',
    ProjectReport = 'projectReport',
    CampaignReport = 'campaignReport',
    AdgroupReport = 'adgroupReport',
    QueryWordReport = 'queryWordReport',
    MarketingPointReport = 'marketingPointReport',
    AcgGoodsReport = 'acgGoodsReport',
    InvalidClickReport = 'invalidClickReport',
    InvalidClueReport = 'invalidClueReport',
    RealtimeReport = 'realtimeReport',
    LiftBudgetReport = 'liftBudgetReport',
    SplitRegionReport = 'splitRegionReport',
    CreativeVideoReport = 'creativeVideoReport',
    CreativeImageReport = 'creativeImageReport',
    CreativeTitleReport = 'creativeTitleReport',
    CreativeDescReport = 'creativeDescReport',
    CreativeTextReport = 'creativeTextReport',
    AdvanceCreativeReport = 'advanceCreativeReport',
    DynamicContentBlock = 'dynamicContentBlock',
    OptimizeAdvices = 'optimizeAdvices',
    AdvertiseDiagnosis = 'advertiseDiagnosis',
    DiagnosisDashboard = 'diagnosisDashboard',
    IndustryInsights = 'industryInsights',
    NegativeWordList = 'negativeWordList',
    LiveRoomReport = 'LiveRoomReport',
    KeywordReport = 'keywordReport',
    GoodsReport = 'goodsReport',
    CrowdReport = 'crowdReport',
    ProgramCreativeReport = 'programCreativeReport',
    CreativeComponentReport = 'creativeComponentReport',
    LandPageReport = 'landPageReport',
    VisitDetailReport = 'visitDetailReport',
    TargetReport = 'targetReport',
    BusinessPoint = 'BusinessPoint',
    // 无实际URL页面 仅用作在标签页的key区分aix和fc
    AixCreativeTextList = 'aixCreativeTextList',
    NewCampaign = 'newCampaign',
    NewAdgroup = 'newAdgroup',
    NewCreative = 'newCreative',
    EditCreative = 'editCreative',
    NewImage = 'newImage',
    NewVideo = 'newVideo',
    CreateFeedProject = 'CreateFeedProject',
    EditFeedProject = 'EditFeedProject',
    CreateFeedCampaign = 'CreateFeedCampaign',
    CreateFeedCPLCampaign = 'CreateFeedCPLCampaign',
    EditFeedCampaign = 'EditFeedCampaign',
    CreateFeedAdgroup = 'CreateFeedAdgroup',
    EditFeedAdgroup = 'EditFeedAdgroup',
    CreateFeedCreative = 'CreateFeedCreative',
    EditFeedCreative = 'EditFeedCreative',
    FastLeague = 'FastLeague',
    EditFastLeague = 'EditFastLeague',
    TargetAudienceReport = 'TargetAudienceReport',
    AftereffectMeasureReport = 'AftereffectMeasureReport',
    NoteReport = 'NoteReport',
    QuestionKnowledgeList = 'QuestionKnowledgeList',
    QuestionKnowledgeReport = 'QuestionKnowledgeReport',
    ImageTextReport = 'ImageTextReport',
    // commodity
    CommodityGroupList = 'CommodityGroupList',
    CommodityTemplate = 'CommodityTemplate',
    CommodityCreative = 'CommodityCreative',
    CommodityCreativeComponent = 'CommodityCreativeComponent',
    CommodityGroupListInAdgroup = 'CommodityGroupListInAdgroup',
    CommodityTemplateInAdgroup = 'CommodityTemplateInAdgroup',
    CommodityCreativeInAdgroup = 'CommodityCreativeInAdgroup',
    CommodityCreativeComponentInAdgroup = 'CommodityCreativeComponentInAdgroup',
    MinmalProjectReport = 'MinmalProjectReport',
    MinmalCreativeImageReport = 'MinmalCreativeImageReport',
    MinmalCreativeTitleReport = 'MinmalCreativeTitleReport',
    MinmalCreativeDescReport = 'MinmalCreativeDescReport',
    MinmalQueryWordReport = 'MinmalQueryWordReport',
    MinmalInvalidClickReport = 'MinmalInvalidClickReport',
    MinmalInvalidClueReport = 'MinmalInvalidClueReport',
    MinmalRealtimeReport = 'MinmalRealtimeReport',
    LeagueSpecialReport = 'LeagueSpecialReport',
    OcpxCampaignReport = 'OcpxCampaignReport',
    VideoInsightReport = 'VideoInsightReport',
    InsightReport = 'InsightReport',
    CplCampaignReport = 'CplCampaignReport',
    ProgramReport = 'ProgramReport',
    StoreReport = 'StoreReport',
    ShopReport = 'shopReport',
    CrowdBidReport = 'crowdBidReport',
    PremiumCrowdsList = 'PremiumCrowdsList',
    ExcludeCrowdsList = 'ExcludeCrowdsList',
}

export function isPageType(value: string): value is PageType {
    return Object.values(PageType).includes(value as PageType);
}

export const PageName = {
    [PageType.ProjectList]: '项目管理',
    [PageType.CampaignList]: '方案管理',
    [PageType.AdgroupList]: '单元管理',
    [PageType.PictureList]: '创意图片管理',
    [PageType.VideoList]: '创意视频管理',
    [PageType.MarketPointList]: '营销要点管理',
    [PageType.KeywordList]: '关键词管理',
    [PageType.QueryWordList]: '搜索词管理',
    [PageType.CreativeTextList]: '创意管理',
    [PageType.GUIReport]: 'GUI报告',
    [PageType.ReportWanHuaTong]: '我的报告',
    [PageType.CreativeComponent]: '创意组件管理',
    [PageType.AssetsOverview]: '资产中心',
    [PageType.ToolsOverview]: '工具中心',
    [PageType.Tools_NegativePackets]: '否定关键词包',
    [PageType.Tools_ScheduleTemplates]: '时段模板',
    [PageType.Tools_PriceStrategy]: '点击出价策略',
    [PageType.Tools_SharedBudget]: '共享预算',
    [PageType.Tools_ImageLibrary]: '图片库',
    [PageType.Tools_VideoLibrary]: '视频库',
    [PageType.Tools_Brands]: '品牌信息',
    [PageType.Tools_EventAsset]: '事件管理',
    [PageType.Tools_AppCenter]: '应用中心',
    [PageType.Tools_History]: '历史操作记录',
    [PageType.Tools_TaskRecord]: '后台任务记录',
    [PageType.Tools_ProductBatch]: '商品目录投放批量工具',
    [PageType.Tools_KR]: '关键词规划师',
    [PageType.Tools_StyleAnalyse]: '广告识别',
    [PageType.Tools_Pna]: '高级样式',
    [PageType.Tools_Track]: '转化追踪',
    [PageType.Tools_BusinessShield]: '商盾-恶意点击屏蔽',
    [PageType.Tools_Adpreview]: '推广实况与诊断',
    [PageType.Tools_AutoRules]: '盯盘助手',
    [PageType.Tools_AutoAdviceRules]: '盯盘助手',
    [PageType.Tools_KeywordPreCheck]: '关键词质量度预检',
    [PageType.Tools_NewDiagnosis]: '广告诊断',
    [PageType.Tools_ClueRfq]: '服务直达-线索加油包',
    [PageType.Tools_AbnormalFluctuationDiagnose]: '推广异常波动诊断',
    [PageType.Tools_InvalidClueDiagnose]: '线索有效性诊断',
    [PageType.Tools_PluginManage]: '组件管理',
    [PageType.Tools_TargetPackage]: '定向包管理',
    [PageType.Tools_IntentWordManage]: '意图词管理',
    [PageType.Tools_MediaManage]: '媒体包管理',
    [PageType.Tools_ideaWordsBag]: '创意通配符',
    [PageType.Tools_ReviewManage]: '广告评论管理',
    [PageType.Tools_IDMP]: 'AI智选人群',
    [PageType.Tools_SuperManager]: '超管万花筒',
    [PageType.Tools_FcCrowds]: '人群包',
    [PageType.AccountReport]: '账户报告',
    [PageType.ProjectReport]: '项目报告',
    [PageType.CampaignReport]: '方案报告',
    [PageType.AdgroupReport]: '单元报告',
    [PageType.QueryWordReport]: '搜索词报告',
    [PageType.MarketingPointReport]: '营销要点报告',
    [PageType.AcgGoodsReport]: '爱采购商品报告',
    [PageType.InvalidClickReport]: '无效点击报告',
    [PageType.InvalidClueReport]: '无效线索报告',
    [PageType.RealtimeReport]: '实时报告',
    [PageType.LiftBudgetReport]: '起量报告',
    [PageType.SplitRegionReport]: '分地域报告',
    [PageType.CreativeVideoReport]: '视频报告',
    [PageType.CreativeImageReport]: '图片报告',
    [PageType.CreativeTitleReport]: '标题报告',
    [PageType.CreativeDescReport]: '描述报告',
    [PageType.CreativeTextReport]: '创意报告',
    [PageType.AdvanceCreativeReport]: '高级样式报告',
    [PageType.DynamicContentBlock]: '动态内容屏蔽',
    [PageType.NegativeWordList]: '否定关键词',
    [PageType.LeagueSpecialReport]: '百青藤报告',
    [PageType.OcpxCampaignReport]: '计划转化出价报告',
    [PageType.VideoInsightReport]: '视频洞察报告',
    [PageType.KeywordReport]: '关键词报告',
    [PageType.LandPageReport]: '落地页报告',
    [PageType.DiagnosisDashboard]: '诊断概览',
    [PageType.AdvertiseDiagnosis]: '问题排查',
    [PageType.PremiumCrowdsList]: '定向人群',
    [PageType.ExcludeCrowdsList]: '排除人群',
    [PageType.ProgramCreativeReport]: '程序化创意报告',
    [PageType.CreativeComponentReport]: '组件报告',
    [PageType.VisitDetailReport]: '访问明细',
    [PageType.InsightReport]: '数据简报',
    [PageType.CplCampaignReport]: '服务直达报告',
    [PageType.ProgramReport]: '小程序报告',
    [PageType.StoreReport]: '本地店铺报告',
    [PageType.CrowdReport]: '人群报告',
    [PageType.GoodsReport]: '商品报告',
    [PageType.NoteReport]: '笔记报告',
    [PageType.QuestionKnowledgeReport]: '问答报告',
    [PageType.ImageTextReport]: '图文报告',
    [PageType.IndustryInsights]: '行业洞察',
    [PageType.OptimizeAdvices]: '优化建议',
    [PageType.CreateProject]: '创建项目',
    [PageType.NewCampaign]: '新建方案',
    [PageType.NewAdgroup]: '新建单元',
    [PageType.CreateCPLCampaign]: '创建服务直达方案',
    [PageType.NewCreative]: '新建创意',
    [PageType.NewImage]: '新建图片',
    [PageType.NewVideo]: '新建视频',
    [PageType.AftereffectMeasureReport]: '转化效果度量报告',
} as const;
