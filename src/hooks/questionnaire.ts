/**
 * @file 敏捷调研单例 & hook
 * <AUTHOR>
 */

import {useEffect} from 'react';
import {getUserId, getOperatorId, getToken} from '@/utils';

const APP_ID = 91;
interface IQnInitOptions {
    userId: string | number;
    optId: string | number;
    token: string | number;
}

class QuestionaireManager {
    instance: object | null = null;

    constructor() {
        this.instance = null;
    }

    async init({userId, optId, token}: IQnInitOptions) {
        const module = await import('@baidu/agile-survey');
        const Questionaire = module.default;
        this.instance = new Questionaire({
            userid: userId,
            optid: optId,
            appId: APP_ID,
            token,
        });
    }
}

const qn = new Proxy(
    new QuestionaireManager(),
    {
        get(target: Record<string, any>, property: string) {
            if (target[property]) {
                return target[property];
            }
            if (target.instance?.[property]) {
                return target.instance[property];
            }
        }
    }
);

export default qn;

export enum QN_KEY {
    advertiseDiagnosis_25Q3_overCharge = 'advertiseDiagnosis_25Q3_overCharge',
    advertiseDiagnosis_25Q3_consumptionDrop = 'advertiseDiagnosis_25Q3_consumptionDrop',
    qinggeaimax_25Q3 = 'qinggeaimax_25Q3',
}
export function showQn(
    key: QN_KEY,
    {limit, storageKey}: {limit?: number, storageKey?: string} = {}
) {
    const showCount = +(localStorage.getItem(storageKey ?? key) || 0);
    // 有延时加载 所以这里判断下
    if (!qn.show) {
        return;
    }
    if (limit) {
        if (showCount < limit) {
            qn.show(key);
            localStorage.setItem(key, showCount + 1);
        }
    }
    else {
        qn.show(key);
    }
}

export const useQn = ({timeout}: {timeout: number}) => {
    const userId = getUserId();
    const optId = getOperatorId();
    const token = getToken();

    useEffect(
        () => {
            const timer = setTimeout(() => {
                qn.init({userId, optId, token});
            }, timeout);

            return () => {
                clearTimeout(timer);
            };
        },
        [userId, optId, token, timeout]
    );
};
