import {useEffect} from 'react';
// import {compressToUTF16, decompressFromUTF16} from 'lz-string';
import {DomRecordScreen, getDefaultEventReporter} from '@baidu/dom-record-screen';
import {isDev, getToken, getUserId} from '@/utils';
import {isGlobalRecordUser} from '@/utils/getFlag';
import {shouldSendLog} from '@/utils/logger/sendHm';

export const DEFAULT_RECORD_KEY = 'qingge';
export const RECORD_SEND_BOUNDARY = 3;
const RECORD_SIZE_LIMIT = 4;
const RECORD_DATA_LIMIT = 5;
const PRE_RECORD_STORAGE_KEY = 'preRecordStorage';
const PRE_RECORD_STORAGE_KEY_OLD = 'preRecordStorageOld';
interface IRecord {
    start: (key: string) => any;
    stop: (key: string) => any;
    report: ({getResult}: {getResult: () => object}) => void;
    setCustomEvent: (type: string, key: string, needTag?: boolean) => void;
    events: {
        [key: string]: any;
    };
}

export const record: IRecord = new DomRecordScreen({
    config: {},
    request: getDefaultEventReporter({
        userid: getUserId(),
        token: getToken(),
        platformId: 10,
    }),
});

interface RecordProps {
    recordKey?: string;
}

class DomRecord {
    instance = null;
    initRecord(key: string = DEFAULT_RECORD_KEY) {
        DomRecord.instance = record.start(key);
    }
    getRecordHandle() {
        if (!DomRecord.instance) {
            DomRecord.instance = new DomRecord();
        }
        return DomRecord.instance;
    }

    getEventsSize() {
        const recordHandle = DomRecord.instance;
        if (recordHandle) {
            const dataSize = new Blob([JSON.stringify(recordHandle.getResult()?.events)]).size;
            const sizeInMB = dataSize / (1024 * 1024);
            // 返回大小（以兆字节为单位）
            return sizeInMB;
        }
        return 0;
    }
}

// 保持轻舸只有一个录屏在录制，并提供一些方法
export const SingleRecord = new DomRecord();


// 为轻舸的全局录屏增加采样，组件级别录屏仍然是全量
const DEFAULT_PERCENT = 30;
function getRecordableWithProbability(percent = DEFAULT_PERCENT) {
    // 名单内必录
    if (isGlobalRecordUser()) {
        return true;
    }
    // 按概率录制
    return Math.random() < (percent / 100);
}

export const useRecordGlobal = ({recordKey = DEFAULT_RECORD_KEY}: RecordProps = {}) => {
    useEffect(
        () => {
            localStorage.removeItem(PRE_RECORD_STORAGE_KEY);
            if (!isDev && getRecordableWithProbability()) {
                // 开始录制
                SingleRecord.initRecord(recordKey);
                // 如果localstorage中有录屏，则发送上一次的录屏
                setTimeout(() => {
                    if (localStorage.getItem(PRE_RECORD_STORAGE_KEY_OLD)) {
                        const recordResult = JSON.parse(localStorage.getItem(PRE_RECORD_STORAGE_KEY_OLD) || '{}');
                        const eventHandler = {getResult: () => recordResult};
                        if (shouldSendLog() && recordResult?.duration > 10) {
                            record.report(eventHandler);
                        }
                        localStorage.removeItem(PRE_RECORD_STORAGE_KEY_OLD);
                    }
                }, 30000);
                window.addEventListener('beforeunload', () => {
                    if (SingleRecord.getEventsSize() < RECORD_DATA_LIMIT) {
                        localStorage.setItem(
                            PRE_RECORD_STORAGE_KEY_OLD,
                            JSON.stringify(SingleRecord.getRecordHandle()?.getResult()));
                    }
                });
            }
        },
        [recordKey]
    );
};

// 组件级别录制，使用时截断全量录制
export const useRecordByComponent = ({recordKey = DEFAULT_RECORD_KEY, tag = ''} = {}) => {
    useEffect(() => {
        if (Object.keys(record.events).length > 0) {
            // 存在全局录制时，直接截断
            record.stop(DEFAULT_RECORD_KEY);
        }
        // 开始组件级别录制
        record.start(recordKey);
        if (tag) {
            record.setCustomEvent(recordKey, tag, true);
        }
        return () => record.stop(recordKey);
    }, []);
};

export const sendScreenRecord = (key: string) => {
    if (record.events[DEFAULT_RECORD_KEY]) {
        record.setCustomEvent(DEFAULT_RECORD_KEY, key, true);
    }
};
