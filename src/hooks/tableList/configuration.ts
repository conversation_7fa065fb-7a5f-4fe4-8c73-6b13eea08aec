/**
 * @file 物料列表配置hook
 * <AUTHOR> <EMAIL>
 * @date 2021/08/04
 */

import {useCallback, useMemo, useEffect} from 'react';
import {ResourceController, useResource} from 'react-suspense-boundary';
import {intersection, union, difference, isEmpty, isEqual} from 'lodash-es';
import {isOnlineStage, isPreonlineStage, isDev} from '@/utils';
import {fetchMaterialListConfiguration, modMaterialListConfiguration} from '@/api/configuration';
import {getColumnsFormatter} from '@/utils/tableList/columns';
import {MaterialList as MaterialListTypes} from '@/interface/tableList';
import {sendMonitor} from '@/utils/logger';

interface FieldsToMerge {
    toRemove: string[];
    toAppend: string[];
}
export const useMaterialListConfiguration = (reportType: number, {
    fetchMaterialConfigApi = fetchMaterialListConfiguration,
    modMaterialConfigApi = modMaterialListConfiguration,
    withNewCategory = false,
    fieldsToMerge,
}: {
    fetchMaterialConfigApi?: (params: {
        reportType: number;
        withNewCategory: boolean;
    }) => Promise<MaterialListTypes.ColumnConfiguration>;
    modMaterialConfigApi?: (params: {
        reportType: number;
        columns: string[];
        withNewCategory?: boolean;
    }) => Promise<MaterialListTypes.ColumnConfiguration>;
    withNewCategory?: boolean;
    fieldsToMerge?: FieldsToMerge;
} = {}): [
    MaterialListTypes.FormatedColumnConfiguration,
    MaterialListTypes.ModConfiguration,
    ResourceController,
    () => void
] => {
    const [
        rawConfiguration_,
        manualControl,
    ] = useResource(fetchMaterialConfigApi, {reportType, withNewCategory});
    const rawConfiguration = useFieldsMerge(rawConfiguration_, {reportType, fieldsToMerge});

    // 在列配置中，统一将customColumns处理成tableColumns的结构，列表中不需要关心customColumns了
    const columnConfiguration = useMemo(
        () => {
            const {customColumns, columnConfigs, columnGroups = [], splitColumns = []} = rawConfiguration;
            const formatColumns = getColumnsFormatter(columnConfigs);
            // columnGroups为空，代表不分组，则用customColumns计算singleColumns
            const singleColumns = formatColumns(isEmpty(columnGroups) ? customColumns : intersection(
                customColumns, (columnGroups.find(group => !group.name)?.columns || [])
            ));
            // 如果需要前端分组，加一层title
            const groupColumns = columnGroups.filter(group => group.name)
                .map(g => ({title: g.name, children: formatColumns(intersection(customColumns, g.columns))}));
            return {
                ...rawConfiguration,
                tableColumns: singleColumns.concat(groupColumns),
                splitColumnItems: formatColumns(splitColumns),
            };
        },
        [rawConfiguration]
    );

    const modConfiguration = useCallback(
        async (columns: string[]) => {
            const newConf = await modMaterialConfigApi({reportType, columns, withNewCategory});
            manualControl?.refresh(); // 修改自定义列后重新获取reportConfig
            return newConf;
        },
        [modMaterialConfigApi, reportType, withNewCategory, manualControl]
    );

    const updateConfiguration = manualControl.refresh;

    return [
        columnConfiguration,
        modConfiguration,
        manualControl,
        updateConfiguration,
    ];
};

const SAMPLE = isOnlineStage && !isPreonlineStage && !isDev ? 0.1 : 1;
const EMPTY_ARR: string[] = [];

const useFieldsMerge = (
    rawConfiguration: MaterialListTypes.FormatedColumnConfiguration,
    {reportType, fieldsToMerge}: {reportType: number, fieldsToMerge?: FieldsToMerge},
): MaterialListTypes.FormatedColumnConfiguration => {
    const {toRemove = EMPTY_ARR, toAppend = EMPTY_ARR} = fieldsToMerge || {};
    const defaultColumns = useMemo(
        () => union(difference(rawConfiguration?.defaultColumns, toRemove), toAppend),
        [rawConfiguration?.defaultColumns, toRemove, toAppend]
    );
    const customColumns = useMemo(() => { // 使用了默认，才允许 merge customColumns
        if (isEqual(rawConfiguration?.customColumns, rawConfiguration?.defaultColumns)) {
            return union(difference(rawConfiguration?.customColumns, toRemove), toAppend);
        }
        return rawConfiguration?.customColumns;
    }, [rawConfiguration?.customColumns, rawConfiguration?.defaultColumns, toRemove, toAppend]);

    useEffect(
        () => {
            if (Math.random() > SAMPLE) { // 需要采样
                return;
            }
            if (defaultColumns.length && customColumns.length) {
                const sameColumns = intersection(defaultColumns, customColumns);
                sendMonitor('action', {
                    target: 'columns_diff_with_default',
                    source: `report-${reportType}`,
                    count: defaultColumns.length - sameColumns.length, // 默认推荐多了
                    count2: customColumns.length - sameColumns.length, // 默认推荐不够
                });
            }
            if (toRemove.length || toAppend.length) {
                sendMonitor('action', {
                    target: 'columns_diff_has_merged',
                    source: `report-${reportType}`,
                    count: toRemove.length,
                    count2: toAppend.length,
                });
            }
        },
        [reportType, defaultColumns, customColumns, toRemove.length, toAppend.length]
    );

    return {
        ...rawConfiguration,
        defaultColumns,
        customColumns,
    };
}
