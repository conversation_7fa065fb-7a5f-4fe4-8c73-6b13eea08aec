import {useEffect, useMemo, useState, createContext, useContext, useCallback} from 'react';
import queryString from 'query-string';
import {noop} from 'lodash';
import {useLocation} from 'react-router-dom';
import {useLocalStorage, useOriginalDeepCopy, usePreviousEquals, usePreviousValue} from 'huse';
import {useAdRoute} from '@/modules/Ad/routes';
import {PLATFORM_ENUM} from '@/dicts';
import {PRODUCT} from '@/dicts/campaign';
import {FcCampaignTypeEnum, FeedCampaignTypeEnum} from '@/interface/campaign';
import {Instructions} from '@/utils/instructions';
import {getUserId} from '@/utils';
import {parseJsonWithFallback} from '@/utils/json';
import {getStorageKey} from './storage';
import {useURLQuery} from './tableList/query';


export const platformFcProduct = [PRODUCT.FC, PRODUCT.FC_CPL, PRODUCT.B2B_PROMOTION];


export const useAutoCheckProductLine = (
    allowProductLineArr: Array<[PLATFORM_ENUM, PRODUCT, FcCampaignTypeEnum | FeedCampaignTypeEnum | '*']>
) => {
    const key = useMemo(() => getStorageKey('manageCenter', '', `productLine_${getUserId()}`), []);
    const productLineValue = JSON.parse(window.localStorage.getItem(key) || '[]');

    const isInAllowProductLineArr = allowProductLineArr.some((arr: string[]) => {
        return arr.every((item, index) => item === productLineValue[index] || item === '*');
    });
    if (!isInAllowProductLineArr) {
        window.localStorage.removeItem(key);
    }
};

// 全局产品线设定
const productContext = {
    product: PLATFORM_ENUM.FC,
    setProduct: noop,
    previousProduct: undefined,
    instructions: undefined,
};

interface ProductContextType {
    product: PLATFORM_ENUM;
    setProduct: (product: PLATFORM_ENUM) => void;
    previousProduct: PLATFORM_ENUM | undefined;
    instructions: Instructions | undefined;
}

const GlobalProductContext = createContext<ProductContextType>(productContext);
export const useGlobalProductContext = () => useContext(GlobalProductContext);
export const GlobalProductContextProvider = GlobalProductContext.Provider;

export const useIsFeed = () => {
    const {product} = useGlobalProductContext();
    return product === PLATFORM_ENUM.FEED;
};

// eslint-disable-next-line max-len
export const useGlobalProductWithSyncURLQuery = (): [PLATFORM_ENUM, (product: PLATFORM_ENUM) => void, {previousProduct: PLATFORM_ENUM | undefined}] => {
    const {adPageType, linkTo} = useAdRoute();
    const [query, {setQueryWithReplace}] = useURLQuery();
    const GlobalProductKey = useMemo(
        () => `globalProduct_${getUserId()}`,
        []
    );
    const [storageProduct, setStorageProduct] = useLocalStorage(GlobalProductKey, '');
    const [product, setProduct] = useState(() => {
        if (query?.globalProduct) {
            return Number(query.globalProduct);
        }
        else if (storageProduct) {
            return Number(storageProduct);
        }
        return PLATFORM_ENUM.FC;
    });

    const previousProduct = usePreviousValue(product);

    useEffect(() => {
        if (query?.globalProduct) {
            setProduct(Number(query.globalProduct));
            if (storageProduct !== query.globalProduct) {
                setStorageProduct(query.globalProduct);
            }
        }
        else if (storageProduct) {
            setProduct(Number(storageProduct));
        }
    }, [query]);

    // 如果URL中没有globalProduct，则添加globalProduct参数
    useEffect(() => {
        if (!query?.globalProduct) {
            setQueryWithReplace({globalProduct: product});
        }
    }, [query?.globalProduct]);

    const setProductQuery = useCallback(globalProduct => {
        setStorageProduct(globalProduct);
        linkTo(adPageType!, {inehritQuery: false, query: {globalProduct}});
    }, [adPageType]);

    return [product, setProductQuery, {previousProduct}];
};


// 项目列表产品线选择
export function useProjectProductWithSyncURLQuery(
    initialValue: any
) {
    const {search} = useLocation();
    const {aixProduct: valueOfQuery} = useMemo(() => queryString.parse(search), [search]);
    const parsedValue = useOriginalDeepCopy(parseJsonWithFallback(valueOfQuery));

    const [value, set] = useState(initialValue);
    useEffect(
        () => {
            if (parsedValue != null && parsedValue !== '') {
                set(parsedValue);
            }
        },
        [parsedValue]
    );

    // 初始值变化时同步为初始值
    useEffect(() => {
        set(initialValue);
    }, [initialValue]);
    return [value, set];
}

// 方案列表产品线选择
export function useCampaignProductWithSyncURLQuery(
    initialValue: any,
) {
    const {search} = useLocation();
    const {productLine: valueOfQuery} = useMemo(() => queryString.parse(search), [search]);
    const parsedValue = useOriginalDeepCopy(parseJsonWithFallback(valueOfQuery));

    const [value, set] = useState(initialValue);
    useEffect(
        () => {
            if (parsedValue != null && parsedValue !== '') {
                set(parsedValue);
            }
        },
        [parsedValue]
    );

    const equal = usePreviousEquals(initialValue, (prev, current) => {
        return prev?.[0] === current?.[0];
    });
    // 初始值变化时同步为初始值
    useEffect(() => {
        if (!equal) {
            set(initialValue);
        }
    }, [equal]);
    return [value, set];
}
