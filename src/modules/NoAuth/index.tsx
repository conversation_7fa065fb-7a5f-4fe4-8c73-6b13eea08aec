import React, {useEffect, useState} from 'react';
import {AiChatBotProps} from '@/components/AiChat/interface';
import {RichTextRenderer} from '@/components/common/RichTextRenderer';
import {createInstructions} from '@/utils/instructions';
import {sessionConfig} from '@/utils/config';
import {SceneType} from '@/modules/Ad/config';
import '../Ad/index.global.less';
import {Activities} from './Activities';

const content = '您当前暂无完整使用投放平台的权限。<a href="https://www2.baidu.com">请重新登录</a>。';
const accountInfo = {optAccountName: '您'};

function ChatArea(props: AiChatBotProps) {
    const {instructions} = props;

    useEffect(
        () => {
            let aiChat: any = {};
            import('./bot').then(module => {
                aiChat = module.default({instructions, accountInfo});
                aiChat.renderChat(document.getElementById('ai-chat') as HTMLElement);

                const initialMessage = {
                    render: RichTextRenderer,
                    props: {aiChat, instructions, content},
                };
                aiChat.pushMessage('assistant', initialMessage);
            });
            return () => {
                if (aiChat) {
                    aiChat.clearMessage();
                    aiChat.setInputContent('');
                }
                sessionConfig.resetSessionId();
            };
        },
        []
    );

    return (
        <div className="chat-area"><div id="ai-chat" /></div>
    );
}

export default function NoAuth() {
    const [instructions] = useState(() => createInstructions({}));
    const [globalSceneData] = useState<{scene: SceneType}>({scene: SceneType.DEFAULT});

    return (
        <div className="aix-ad-main">
            <ChatArea instructions={instructions} />
            <Activities globalData={globalSceneData} instructions={instructions} />
        </div>
    );
}
