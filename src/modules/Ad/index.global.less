.aix-ad-main {
    @g7-color: #848b99;

    display: flex;
    height: calc(100% - 56px - 64px); // 减去header和footer的高度
    margin: 24px 56px 24px 0;
    border-radius: 26px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    // background: rgba(255, 255, 255, .2);
    box-shadow: 0 2px 24px 1px rgba(0, 71, 194, .02), 0 4px 26px 2px rgba(0, 71, 194, .03), 0 6px 28px 2px rgba(0, 71, 194, .04), -1px 1px 0 0 rgba(255, 255, 255, .45) inset;
    animation: fadeIn 1.2s ease-out;
    width: calc(100% - 112px);
    position: relative;
    flex-direction: row-reverse;

    &.aix-ad-main-c1g2 {
        .light-ai-bubble-avatar {
            display: none;
        }

        .universal-report {
            h4 {
                position: static;
                padding: 10px 0 0 10px;
            }

            .analysis-data {
                width: 100%;
                min-width: auto;

                .data-item {
                    width: 45%;
                }
            }
        }

        .line-trend-report {
            .line-chart-container {
                width: 100%;
                margin-left: 0;
                padding-left: 0;
                min-width: auto;
                max-width: none;
            }
        }

        .date-switch {
            .one-ai-date-picker-title-text {
                display: none;
            }
        }

        .cpu-footer-copyright {
            padding: 0;
        }
    }

    .chat-area {
        flex: 1;
        width: 0; // !防止子元素撑开父元素导致右侧宽度不足
        background-color: #fff;
        border-radius: 26px;
        box-shadow: 0 2px 8px 0 rgba(0, 71, 194, .01), 0 3px 9px 0 rgba(0, 71, 194, .02), 0 4px 10px 0 rgba(0, 71, 194, .03);
        transition: flex-grow .3s ease-in-out;

        .user-avatar {
            background: #2D66F3;
            color: #fff;
        }

        .assist-avatar {
            height: 32px;
            border: none;
            background-color: transparent;
        }

        .empty-content-chat-container {
            padding: 0;
            // gap: 8px;

            & > .light-ai-slot-main {
                margin-right: 0;
                padding-right: 0;
            }
        }
    }

    #ai-chat {
        height: calc(100% - 36px);
        display: flex; // 这里使用flex时子元素会默认撑满父元素
    }

    .back-icon {
        width: 20px;
        margin-right: 4px;
        font-size: 20px;
        vertical-align: text-bottom;
        color: @g7-color;
        cursor: pointer;
    }

    .aix-overview-area {
        box-sizing: border-box;
        height: 100%;
        display: flex;
        flex-direction: column;

        .aix-overview-area-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            color: var(--g-9, #282c33);
            font-size: 18px;
            font-weight: bold;
            line-height: 26px;
            // 下面这个超长显示... CSS没卵用，因为display = flex; 先不删了，影响面有点大。如果想要显示..., 可以用下面的name class
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            .edit-name {
                display: inline-block;
                height: 16px;
                margin-left: 4px;
                color: @g7-color;
                cursor: pointer;
            }

            .operation {
                color: var(--g-7, @g7-color);
                font-size: 14px;
                font-weight: 400;
                gap: 6px;
            }

            .back-to-list {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .name {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .campaign-id {
            margin: -16px 0 16px 16px;
            color: @g7-color;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
        }
    }

    .common-card-content-wrapper {
        padding: 16px;
        border-radius: 10px;
        background: #fff;
    }
}

.aix-common-scroll-view {
    overflow-y: scroll;
    overflow-x: visible;
    flex: 1;
    // border-radius: 10px; 这里有点问题，因为滚动条会把右边的border-radius右移滚动条的宽度，所以这里先注释掉border-radius
    width: calc(100% + var(--aix-scroll-view-realtime-scrollbar-width));
    transition: width .1s ease-in-out;
}

.aix-GUI-area {
    flex-basis: 0;
    transition: flex-basis .5s ease-in-out;
    overflow: hidden;

    &.NORMAL {
        flex-basis: 440px;
        padding: 20px 20px 0 20px;
    }

    &.GUI_FULL {
        flex-basis: 100%;
        padding: 32px;
        border-left: none;

        & + .chat-area {
            visibility: hidden;

            .ai-x-main-foot {
                display: none !important;
            }
        }
    }

    &.C1G2 {
        flex-basis: calc(100% - 440px);
        padding: 32px;
        min-width: 664px;
        overflow-x: auto;
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

.scene-error-boundary {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.GUI-activity {
    height: 100%;
    flex-direction: column;

    .GUI-activity-title {
        display: flex;
        align-items: center;
        color: #282c33;
        font-size: 18px;
        font-weight: 500;
        line-height: 26px;
        margin-bottom: 16px;
        gap: 0 8px;
    }

    .GUI-activity-content {
        flex: 1;
        height: calc(100% - 42px);

        .ka-wrapper,
        .ka-content {
            height: 100%;
        }
    }
}

.aix-content-container {
    flex: 1;
    display: flex;
    width: 100%;
    justify-content: space-between;
    padding: 0 24px;
    transition: width .3s;

    .aix-content {
        display: none;
        height: 100%;
        width: 100%;
        border-radius: 10px;

        .aix-ad-main {
            height: calc(100vh - 68px);
            width: 100%;
            margin: 0;
            // background: #fff;
            box-shadow: none;
            border-radius: 10px 0 0 0;

            .chat-area {
                background: transparent;
                box-shadow: none;
                position: relative;
                height: 100%;
                display: flex;
                flex-direction: column;
            }

            .ai-x-main-foot {
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
            }
        }
    }

    .aix-content-visible {
        display: flex;
        justify-content: center;
        width: 100%;
    }

    #ai-chat-root {
        padding-top: 0;
        padding-bottom: 0;
        background: transparent;
        box-shadow: none;
    }

    .aix-content-hidden {
        height: auto;
        min-height: 112px;
        position: fixed;
        bottom: 0;
        background: #f7f7f7;
        z-index: 10;
        border-radius: 0;
        margin-left: -24px;

        .aix-GUI-area {
            display: none;
        }

        #ai-chat-root {
            padding-top: 0;
            background: transparent;
            box-shadow: none;

            > .light-ai-slot-main:first-child {
                display: none;
            }
        }

        .empty-container {
            display: none;
        }

        .aix-ad-main {
            height: auto;
            min-height: 112px;

            .chat-area {
                margin-bottom: 44px;
            }
        }
    }
}

@media screen and (max-width: 1280px) {
    .aix-content-container {
        min-width: calc(1280px - 64px);
    }
}

// 嵌套 drawer 时， 内层 drawer 的阴影 加深
.one-ai-drawer .one-ai-drawer-body .one-ai-drawer {
    // 重置行高，不然会继承body的1.8
    line-height: 1;
    // 重置回默认行高
    .one-ai-drawer-body {
        line-height: 1.8;
    }

    .one-ai-drawer-content-wrapper {
        box-shadow: 0 -6px 28px 2px rgba(0, 71, 194, 0.04), 0 -4px 26px 2px rgba(0, 71, 194, 0.03), 0 -2px 24px 1px rgba(0, 71, 194, 0.02);
    }
}


.baidu-number {
    font-family: 'Baidu Number';
}

// 数字显示为等宽 例如  多行展示日期时不会参差不齐
.tabular-num {
    font-variant-numeric: tabular-nums;
}

.aix-adModule-vertical-nav {
    width: 172px;
    overflow-y: auto;
    flex-shrink: 0;

    .one-ai-ui-pro-vertical-nav-toggle-btn {
        position: fixed;
        bottom: 0;
        left: 170px;
    }

    .one-ai-ui-pro-vertical-nav-content {
        background: transparent;
        height: 100%;

        .one-ai-menu-inline-box {
            background: transparent;
            width: 162px;

            .one-ai-menu {
                background: transparent;
            }
        }
    }

    &.one-ai-ui-pro-vertical-nav-collapsed {
        width: 40px;

        .one-ai-ui-pro-vertical-nav-toggle-btn {
            left: 36px;
        }

        .one-ai-menu-inline-box {
            width: 36px;

            .one-ai-menu-inline-collapsed {
                width: 36px;
            }
        }
    }
}
