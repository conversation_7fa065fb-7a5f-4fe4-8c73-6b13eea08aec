import {useCallback, useEffect, useMemo, useState, ReactNode} from 'react';
import useS<PERSON>, {Key} from 'swr';
import {
    IllustrationUnderReview,
    IllustrationReviewError,
    IllustrationReviewSuccess,
} from 'dls-illustrations-react';
import {BlankPage} from '@baidu/one-ui-pro';
import {getExceedCostDiagnosisResult, ExceedCostResponse} from '@/api/diagnosis/exceedCost';
import {Instructions} from '@/utils/instructions';
import {sendMonitor} from '@/utils/logger';
import {isProductionOnline} from '@/utils';
import {toFixed} from '@/utils/number';
import {DiagnosisInfoRange, sendDiagnosisMonitor} from '@/api/diagnosis/common';
import {getDiagnosisMonitorParams} from '@/modules/Ad/OptCenter/AdvertiseDiagnosis/util/monitor';
import {PriceGuardType} from '@/api/diagnosis/consumeFluctuation';
import {DiagnosisScene} from '@/api/diagnosis/common';
import {getExceedCostProjectInfo} from '@/api/diagnosis/exceedCostDetail';
import {useRecordByComponent} from '@/hooks';
import {DIAGNOSIS_RECORD_CONFIG} from '@/config/diagnosisRecordConfig';
import './style.global.less';
import ExceedCostSelector from './selector';
import {SelectorFormData} from './types';
import ExceedCostDiagnosisResult from './diagnosisResult';
import {ExceedCostContextType, ExceedCostProvider} from './context';
import {ExceedCostConclusionLoading} from './diagnosisResult/conclusion';


export default function ExceedCost({instructions}: {instructions: Instructions}) {
    const [selectValue, setSelectValue] = useState<SelectorFormData>();

    useRecordByComponent({tag: DIAGNOSIS_RECORD_CONFIG.EXCEED_COST});

    useEffect(() => {
        sendMonitor('click', {
            ...getDiagnosisMonitorParams('diagnosisPageOnLoad'),
            target: 'GUI页面展示',
        });
    }, []);

    const {
        data,
        isLoading,
        error,
        refresh,
        DiagnosisResultProvider,
    } = useDiagnosisResult({
        key: !selectValue?.project?.projectId ? null : undefined,
        projectId: selectValue?.project?.projectId,
        diagnosisTime: selectValue?.diagnosisTime as DiagnosisInfoRange['diagnosisTime'],
        baseTime: selectValue?.baseTime as DiagnosisInfoRange['baseTime'],
    });

    const onSelectChange = useCallback(
        (value: SelectorFormData, target?: string) => {
            setSelectValue(value);

            sendMonitor('click', {
                ...getDiagnosisMonitorParams('diagnosisConsumptionDropSubmitClick'),
                target: target || '查询按钮点击',
            });
        },
        []
    );

    return (
        <div className="over-cost">
            <div className="over-cost-header">
                <ExceedCostSelector
                    onSelectChange={onSelectChange}
                    onClickSearch={refresh}
                    isSubmitLoading={isLoading}
                />
            </div>

            <div className="over-cost-content">
                <div className="content-analysis">
                    {
                        selectValue && !isLoading && !error && data?.analysisData
                            ? (
                                <DiagnosisResultProvider instructions={instructions} value={selectValue}>
                                    <ExceedCostDiagnosisResult />
                                </DiagnosisResultProvider>
                            )
                            : (
                                <EmptyContent
                                    selectValue={selectValue}
                                    analysisData={data?.analysisData}
                                    error={error}
                                    isLoading={isLoading}
                                />
                            )
                    }
                </div>
            </div>
        </div>
    );
}

function EmptyContent({
    selectValue,
    analysisData,
    error,
    isLoading,
}: {
    selectValue?: SelectorFormData;
    analysisData?: ExceedCostResponse;
    error?: string;
    isLoading: boolean;
}) {

    if (isLoading) {
        return <ExceedCostConclusionLoading />;
    }

    if (error) {
        return (
            <BlankPage
                className="over-cost-content-empty"
                content={{
                    description: (
                        <div>
                            {
                                isProductionOnline
                                    ? '出错了'
                                    : error || '出错了'
                            }
                        </div>
                    ),
                    icon: <IllustrationReviewError />,
                }}
                mode="global"
            />
        );
    }


    return (
        <BlankPage
            className="over-cost-content-empty"
            content={
                selectValue ? {
                    description: (
                        <EmptyDescription
                            analysisData={analysisData}
                            selectValue={selectValue}
                        />
                    ),
                    icon: <IllustrationReviewSuccess />,
                } : {
                    description: (
                        <div>
                            请选择项目与日期
                            <br />
                            点击查询波动情况
                        </div>
                    ), icon: <IllustrationUnderReview />,
                }}
            mode="global"
        />
    );
}

function EmptyDescription({
    analysisData, selectValue,
}: {analysisData?: ExceedCostResponse, selectValue?: SelectorFormData}) {

    if (!analysisData) {
        return (
            <div>
                环比周期内未存在超成本情况
                <br />
                请重新选择项目/对比日期

            </div>
        );
    }

    const isDeep = selectValue?.project?.priceGuardType === PriceGuardType.DEEP;
    const dateRange = `${selectValue?.diagnosisTime[0]}~${selectValue?.diagnosisTime[1]}`;
    const bidType = isDeep ? '深度转化出价' : '目标转化出价';
    const costType = isDeep ? '深度转化成本' : '目标转化成本';
    const bidValue = isDeep ? analysisData.deepSetCpa : analysisData.setCpa;
    const costValue = isDeep ? analysisData.deepTransPrice : analysisData.transPrice;
    const overRatio = toFixed(analysisData.overchargeRatio * 100, 2);

    return (
        <div>
            项目在{dateRange}{bidType}{bidValue}元，{costType}{costValue}元，超成本比例为{overRatio}%，
            实际成本未超出目标出价的120%，属于正常波动范围
            <br />
            请重新选择项目/对比日期
        </div>
    );
}

async function fetchExceedCostDiagnosisData({
    projectId,
    diagnosisTime,
    baseTime,
    triggerSource,
}: DiagnosisInfoRange & {projectId: number}) {
    const [
        analysisData,
        projectInfo,
    ] = await Promise.all([
        getExceedCostDiagnosisResult({projectId, diagnosisTime, baseTime, triggerSource}),
        getExceedCostProjectInfo({projectId}),
    ]);

    return {analysisData, projectInfo};
}

export function useDiagnosisResult({
    projectId,
    diagnosisTime,
    baseTime,
    triggerSource,
    key = {key: 'SWR_getProjectOverchargeResult', projectId, diagnosisTime, baseTime, triggerSource},
}: DiagnosisInfoRange & {projectId?: number, key?: Key}) {
    const {
        data,
        isLoading,
        error,
        mutate,
    } = useSWR(
        key,
        fetchExceedCostDiagnosisData,
        {revalidateOnFocus: false, revalidateOnReconnect: false, revalidateIfStale: true}
    );

    const refresh = useCallback(
        () => {
            mutate(undefined, {revalidate: true});
        },
        [mutate]
    );

    const diagnosisLog = useCallback((params: Record<string, any>) => {
        sendMonitor('click', {
            diagnosisScene: DiagnosisScene.projectOverCharge,
            ...params,
        });
        sendDiagnosisMonitor({
            projectId,
            processId: data?.analysisData?.processId,
            diagnosisScene: DiagnosisScene.projectOverCharge,
            adviceType: params.item,
            ...params,
        } as any);
    }, [data, projectId]);

    const DiagnosisResultProvider = useMemo(
        () => {
            return function ({
                children,
                instructions,
                value,
            }: {instructions: Instructions, children: ReactNode, value?: SelectorFormData}) {
                const ctxValue = {
                    analysisData: data?.analysisData,
                    selectValue: value,
                    instructions,
                    diagnosisScene: DiagnosisScene.projectOverCharge,
                    projectInfo: data?.projectInfo,
                    diagnosisLog,
                } as ExceedCostContextType;
                return (
                    <ExceedCostProvider value={ctxValue}>
                        {children}
                    </ExceedCostProvider>
                );
            };
        },
        [data]
    );

    return {data, isLoading, error, refresh, DiagnosisResultProvider};
}
