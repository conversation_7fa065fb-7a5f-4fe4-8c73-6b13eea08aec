
import {useCallback, useEffect, useMemo, useState, ReactNode} from 'react';
import useS<PERSON>, {Key} from 'swr';
import {IllustrationReviewError, IllustrationReviewSuccess} from 'dls-illustrations-react';
import {BlankPage} from '@baidu/one-ui-pro';
import {getDiagnosisResult, IGetDiagnosisResultResponse} from '@/api/diagnosis/consumeFluctuation';
import {DiagnosisInfoRange, DiagnosisScene, sendDiagnosisMonitor} from '@/api/diagnosis/common';
import {Instructions} from '@/utils/instructions';
import {sendMonitor} from '@/utils/logger';
import {isProductionOnline} from '@/utils';
import {toFixed} from '@/utils/number';
import {getDiagnosisMonitorParams} from '@/modules/Ad/OptCenter/AdvertiseDiagnosis/util/monitor';
import {useRecordByComponent} from '@/hooks';
import {DIAGNOSIS_RECORD_CONFIG} from '@/config/diagnosisRecordConfig';
import './style.global.less';
import ConsumeFluctuationSelector from './selector';
import {SelectorFormData} from './types';
import ConsumeFluctuationDiagnosisResult from './diagnosisResult';
import {ConsumeFluctuationContextType, ConsumeFluctuationProvider} from './context';
import {ConsumeFluctuationConclusionLoading} from './diagnosisResult/conclusion';

export default function ConsumeFluctuation({instructions}: {instructions: Instructions}) {

    const [selectValue, setSelectValue] = useState<SelectorFormData>();

    const [hasFluctuationDrop, setHasFluctuationDrop] = useState(false);

    useRecordByComponent({tag: DIAGNOSIS_RECORD_CONFIG.CONSUME_FLUCTUATION});

    useEffect(() => {
        sendMonitor('click', {
            ...getDiagnosisMonitorParams('diagnosisPageOnLoad'),
            target: '消费波动GUI页面展示',
        });
    }, []);

    const {
        analysisData,
        isLoading,
        error,
        refresh,
        DiagnosisResultProvider,
    } = useDiagnosisResult({
        key: !selectValue?.project?.isFluctuationDrop && !selectValue?.projectListPending ? null : undefined,
        projectId: selectValue?.project?.projectId,
        diagnosisTime: selectValue?.diagnosisTime as DiagnosisInfoRange['diagnosisTime'],
        baseTime: selectValue?.baseTime as DiagnosisInfoRange['baseTime'],
    });

    const onSelectChange = useCallback(
        (value: SelectorFormData, target?: string) => {
            setSelectValue(value);

            sendMonitor('click', {
                ...getDiagnosisMonitorParams('diagnosisConsumptionDropSubmitClick'),
                target: target || '消费波动查询按钮点击',
            });
        },
        []
    );

    return (
        <div className="consume-fluctuation">
            <div className="consume-fluctuation-header">
                <ConsumeFluctuationSelector
                    onSelectChange={onSelectChange}
                    setHasFluctuationDrop={setHasFluctuationDrop}
                    onClickSearch={refresh}
                    isSubmitLoading={isLoading}
                />
            </div>

            <div className="consume-fluctuation-content">
                <div className="content-analysis">
                    {
                        analysisData?.isFluctuationDrop && selectValue && !isLoading ? (
                            <DiagnosisResultProvider instructions={instructions} value={selectValue}>
                                <ConsumeFluctuationDiagnosisResult />
                            </DiagnosisResultProvider>
                        ) : (
                            <EmptyContent
                                hasFluctuationDrop={hasFluctuationDrop}
                                selectValue={selectValue}
                                analysisData={analysisData}
                                error={error && (error instanceof Error ? error?.message : '出错了')
                                }
                                isLoading={isLoading}
                            />
                        )
                    }
                </div>
            </div>
        </div>
    );
}

function EmptyContent({
    selectValue,
    analysisData,
    error,
    isLoading,
    hasFluctuationDrop,
}: {
    selectValue?: SelectorFormData;
    analysisData?: IGetDiagnosisResultResponse;
    error?: string;
    isLoading: boolean;
    hasFluctuationDrop: boolean;
}) {

    if (isLoading) {
        return <ConsumeFluctuationConclusionLoading />;
    }

    if (error) {
        return (
            <BlankPage
                className="consume-fluctuation-content-empty"
                content={{
                    description: (
                        <div>
                            {
                                isProductionOnline
                                    ? '出错了'
                                    : error || '出错了'
                            }
                        </div>
                    ),
                    icon: <IllustrationReviewError />,
                }}
                mode="global"
            />
        );
    }
    if (!selectValue || (selectValue && !analysisData?.isFluctuationDrop && !hasFluctuationDrop)) {
        return (
            <BlankPage
                className="consume-fluctuation-content-empty"
                content={{
                    description: (
                        <div>
                            该时间范围内暂无项目存在消费下降问题
                            <br />
                            请重新选择日期
                        </div>
                    ),
                    icon: <IllustrationReviewSuccess />,
                }}
                mode="global"
            />
        );
    }


    return (
        <BlankPage
            className="consume-fluctuation-content-empty"
            content={
                {
                    description: (
                        <EmptyDescription
                            analysisData={analysisData}
                        />
                    ),
                    icon: <IllustrationReviewSuccess />,
                }}
        />
    );
}

function EmptyDescription({analysisData}: {analysisData?: IGetDiagnosisResultResponse}) {
    if (!analysisData
        || !analysisData.isFluctuationDrop
        || typeof analysisData.projectCharge !== 'number'
    ) {
        return (
            <div>
                环比周期内未存在消费突降情况
                <br />
                请重新选择项目/对比日期

            </div>
        );
    }

    return (
        <div>
            问题日期消费{
                toFixed(analysisData.projectCharge)
            }元，环比基准日期{
                (analysisData.projectChargeDiffRatio) > 0
                    ? `上升${(Math.abs(analysisData.projectChargeDiffRatio) * 100).toFixed(2)}%`
                    : `下降${(Math.abs(analysisData.projectChargeDiffRatio) * 100).toFixed(2)}%`
            }，属于正常波动范围
            <br />
            请重新选择项目/对比日期
        </div>
    );
}

export function useDiagnosisResult({
    projectId,
    diagnosisTime,
    baseTime,
    triggerSource,
    key = {key: 'SWR_getConsumeFluctutionDiagnosisResult', projectId, diagnosisTime, baseTime, triggerSource},
}: DiagnosisInfoRange & {projectId?: number, key?: Key}) {
    const {
        data: analysisData,
        isLoading,
        error,
        mutate,
    } = useSWR(
        key,
        getDiagnosisResult,
        {revalidateOnFocus: false, revalidateOnReconnect: false, revalidateIfStale: true}
    );

    const refresh = useCallback(
        () => {
            mutate(undefined, {revalidate: true});
        },
        [mutate]
    );


    const diagnosisLog = useCallback((params: Record<string, any>) => {
        sendMonitor('click', {
            diagnosisScene: DiagnosisScene.projectFluctuationDrop,
            ...params,
        });
        sendDiagnosisMonitor({
            projectId,
            processId: analysisData?.processId,
            diagnosisScene: DiagnosisScene.projectFluctuationDrop,
            adviceType: params.item,
            ...params,
        } as any);
    }, [analysisData, projectId]);

    const DiagnosisResultProvider = useMemo(
        () => {
            return function ({
                children,
                instructions,
                value,
            }: {instructions: Instructions, children: ReactNode, value?: SelectorFormData}) {
                const ctxValue = {
                    analysisData,
                    selectValue: value, instructions, diagnosisLog,
                } as ConsumeFluctuationContextType;
                return (
                    <ConsumeFluctuationProvider value={ctxValue}>
                        {children}
                    </ConsumeFluctuationProvider>
                );
            };
        },
        [analysisData]
    );

    return {analysisData, isLoading, error, refresh, DiagnosisResultProvider};
}
