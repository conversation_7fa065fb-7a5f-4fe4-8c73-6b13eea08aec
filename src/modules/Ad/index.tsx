import {useState, useEffect, Suspense, lazy} from 'react';
import classNames from 'classnames';
import {Switch, Route} from 'react-router-dom';
import {SWRConfig} from 'swr';
import {useCounter} from 'huse';
import {Toast} from '@baidu/one-ui';
import {Boundary, useResource} from 'react-suspense-boundary';
import {markCustomPerf} from '@/utils/logger/weirwood';
import {InitialPayloadState} from '@/components/AiChat/interface';
import Header from '@/components/Header';
import {useRecordGlobal, useQn} from '@/hooks';
import {PLATFORM_ENUM} from '@/dicts';
import {AdModuleType, PageType} from '@/dicts/pageType';
import {useAdRoute, AdRouteProvider} from '@/modules/Ad/routes';
import {parseJsonWithFallback, decodeObject} from '@/utils/json';
import {getQueryString} from '@/utils';
import globalData from '@/utils/globalData';
import aiChat from '@/utils/aiChat';
import {QN_KEY, showQn} from '@/hooks/questionnaire';
import {Instructions, createInstructions, useRegisterInstructions} from '@/utils/instructions';
import {ASSIST_INSTRUCTION_KEY_MAP, SceneType} from '@/modules/Ad/config';
import {LaunchAccountSettingEditorProps} from '@/utils/instructions';
import createCacheProvider, {GlobalSwrCacheContextProvider, memorySwrProvider} from '@/dbProvider';
import {preload, AIX_PRELOAD_POOL_KEY} from '@/hooks/request/preload';
import {useGlobalProductWithSyncURLQuery, GlobalProductContextProvider} from '@/hooks/productLine';
import {isAutoQinggeMoveUser, isFlashLinkUser, isAiBuildUser} from '@/utils/getFlag';
import {usePreloadBySwrCallback, TypePreloadBySwr} from '@/hooks/request/swr';
import {useShowFeedPlatformGuide} from '@/modules/Ad/hooks/useShowFeedPlatformGuide';
import {guideIdConfig} from '@/config/guideId';
import FlashLinkTrigger from '@/components/FlashLink/FlashLinkTrigger';
import {useURLQuery} from '@/hooks/tableList/query';
import {initIframe} from '@/utils/iframeManager';
import {getIframeUrl} from '@/utils/iframe';
import {PRODUCT} from '@/dicts/campaign';
import AdRoutes from './routes';
import {OptCenterDetailIframeDrawer} from './OptCenter/OptimizeAdvices';
import ChatDrawer from './ChatDrawer';
import './index.global.less';

const ChatMain = lazy(() => import(/* webpackPreload: true */ '@/modules/Ad/ChatMain'));
const PromotionMainRoutes = AdRoutes.getRoutesByPrefixs?.(['/ad/promotionMain']);

function AdMain() {
    const {linkTo, adModule} = useAdRoute();
    const [chatDOMKey, {increment: resetChat}] = useCounter();

    // 自动设置全局默认产品线，注：默认产品线 session 级别有效。此后各个页面涉及产品线选择的地方，会优先读取默认产品线作为初始值。
    const [globalProduct, setGlobalProduct, {previousProduct}] = useGlobalProductWithSyncURLQuery();

    const [globalSceneData, updateGlobalSceneData] = useState<{scene: SceneType}>({scene: SceneType.DEFAULT});
    const [instructions] = useState(() => createInstructions({
        updateGlobalSceneData,
        [ASSIST_INSTRUCTION_KEY_MAP.InsertContentToChatInput]: ({insertTemplate}) => {
            aiChat.setInputContent(insertTemplate);
        },
        [ASSIST_INSTRUCTION_KEY_MAP.SetBackendPayload]: ({sessionId, ...extraPayload}) => {
            globalData.set({extraPayload});
        },
        [ASSIST_INSTRUCTION_KEY_MAP.ToReportCenter]: ({guiToken}) => {
            linkTo(PageType.GUIReport, guiToken ? {query: {prompt: guiToken}} : {});
        },
        [ASSIST_INSTRUCTION_KEY_MAP.LinkTo]: ({pageType, options}) => {
            linkTo(pageType, options);
        },
        [ASSIST_INSTRUCTION_KEY_MAP.RestartChat]: () => {
            const messageList = aiChat.getMessageList();
            if (messageList?.length === 0) {
                Toast.info({content: '当前已是最新对话', showCloseIcon: false});
                return;
            }
            resetChat();
        },
    }));

    useRegisterInstructions(instructions, [
        [
            ASSIST_INSTRUCTION_KEY_MAP.LaunchAccountSettingEditor,
            (payload: void | LaunchAccountSettingEditorProps) => {
                instructions?.[ASSIST_INSTRUCTION_KEY_MAP.OpenChatDrawer]?.({
                    name: 'AccountSetting',
                    title: '账户设置',
                    props: {
                        drawerProps: {
                            width: '800px',
                            zIndex: 1000,
                        },
                        openField: payload?.openField,
                        aixProduct: payload?.aixProduct,
                        options: payload?.options,
                    },
                });
            },
        ],
    ]);

    useRecordGlobal(); // 每次录完存储localstorage，下次登录发送上次的录屏
    useQn({timeout: 5000}); // 接入敏捷调研

    const preloadMethod = usePreloadBySwrCallback();
    useEffect(
        () => {
            const [, destructor] = homePagePreload(preloadMethod, 2000);
            return destructor;
        },
        []
    );

    useShowFeedPlatformGuide(
        guideIdConfig.FEED_PLATFORM_GUIDE_HOMEPAGE,
        {
            // 等全局的新手引导不再展现了才展出信息流自己的引导
            showCondition: () => {
                return !isAutoQinggeMoveUser()
                && globalProduct === PLATFORM_ENUM.FEED
                && location.pathname === '/ad/overview';
            },
        }
    );

    useEffect(() => {
        // 首页didmount 埋点
        markCustomPerf('performance_ad_page_did_mount');
        window?.performance && window.performance.measure(
            'x_qingge_adMain',
            'navigationStart',
            'performance_ad_page_did_mount'
        );
    }, []);

    useEffect(() => {
        // 初始化iframe，首页不初始化，避免iframe内的资源影响首页ITTI指标
        if (
            globalProduct === PLATFORM_ENUM.FC
            && ![AdModuleType.Overview].includes(adModule)
        ) {
            initIframe(
                getIframeUrl(
                    '/fc/datacenter/qingge/querywordReport/init/user/${userId}',
                    {product: PRODUCT.FC, extraSearch: {init: 'true'}},
                )
            );
        }
    }, [globalProduct, adModule]);

    useEffect(
        () => {
            setTimeout(
                () => {
                    if (isAiBuildUser() && location.pathname === '/ad') {
                        showQn(QN_KEY.qinggeaimax_25Q3, {limit: 2});
                    }
                },
                8000
            );
        },
        []
    );

    return (
        <GlobalProductContextProvider
            value={{
                product: globalProduct,
                setProduct: setGlobalProduct,
                previousProduct,
                instructions,
            }}
        >
            <Switch>
                {
                    PromotionMainRoutes.map(route => {
                        const Component = route.component;
                        return (
                            <Route key={route.name} path={route.fullPath}>
                                <Suspense fallback={null}>
                                    <Component instructions={instructions} />
                                </Suspense>
                            </Route>
                        );
                    })
                }
                <Route>
                    <DefaultAdMain
                        chatDOMKey={chatDOMKey}
                        globalSceneData={globalSceneData}
                        instructions={instructions}
                        globalProduct={globalProduct}
                    />
                </Route>
            </Switch>
            <ChatDrawer instructions={instructions} />
        </GlobalProductContextProvider>
    );
}

interface DefaultAdMainProps {
    chatDOMKey: number;
    globalSceneData: {scene: SceneType};
    instructions: Instructions;
    globalProduct: PLATFORM_ENUM;
}
function DefaultAdMain({chatDOMKey, globalSceneData, instructions, globalProduct}: DefaultAdMainProps) {
    const [{prompt: initialPrompt = ''}] = useURLQuery<{prompt?: string}>();
    const [initialPayload] = useState<InitialPayloadState>(
        decodeObject(parseJsonWithFallback(getQueryString('payload') || '', {}))
    );
    const {adModule, fromGuiPathname} = useAdRoute();

    const chatMainCls = classNames(
        'aix-content',
        {
            // eslint-disable-next-line max-len
            'aix-content-visible': [AdModuleType.PromptCenter, AdModuleType.Overview].includes(adModule) || fromGuiPathname,
            'aix-content-hidden': adModule === AdModuleType.Overview, // 概览页只留下输入框
        }
    ); // 有 fromGuiPathname 说明是来自 GUI 但目前切到了 LUI
    const guiMainCls = classNames('aix-content', {'aix-content-visible': !fromGuiPathname});
    return (
        <>
            <Header instructions={instructions} showPlatformSwitch />
            <Boundary renderError={() => null}>
                {isFlashLinkUser() && globalProduct === PLATFORM_ENUM.FC && <FlashLinkTrigger />}
            </Boundary>
            <div className="aix-content-container">
                <Suspense fallback={null}>
                    <div className={chatMainCls}>
                        <ChatMain
                            key={chatDOMKey}
                            initialPrompt={initialPrompt}
                            initialPayload={initialPayload}
                            globalSceneData={globalSceneData}
                            instructions={instructions}
                        />
                    </div>
                    <OptCenterDetailIframeDrawer instructions={instructions} />
                </Suspense>
                <Switch>
                    {
                        AdRoutes.map(route => {
                            const Component = route.component;
                            return (
                                <Route key={route.name} path={route.fullPath}>
                                    <Suspense fallback={null}>
                                        <div className={guiMainCls}>
                                            <Component instructions={instructions} />
                                        </div>
                                    </Suspense>
                                </Route>
                            );
                        })
                    }
                </Switch>
            </div>
        </>
    );
}

export function AdModule() {
    const [dbProvider] = useResource(createCacheProvider, {storeName: 'swrCache'});
    return (
        <AdRouteProvider>
            <SWRConfig value={{provider: memorySwrProvider}}>
                <GlobalSwrCacheContextProvider value={{dbProvider}}>
                    <AdMain />
                </GlobalSwrCacheContextProvider>
            </SWRConfig>
        </AdRouteProvider>
    );
}

export default function AdModuleWithBoundary() {
    return (
        <Boundary>
            <AdModule />
        </Boundary>
    );
}

function homePagePreload(preloadMethod: TypePreloadBySwr, timeout: number) {
    let controller: any;
    let t: any;
    function onLoad() {
        t = setTimeout(
            () => {
                if (window.requestIdleCallback) {
                    window.requestIdleCallback(
                        idle => {
                            if (idle.timeRemaining() > 0) {
                                controller = preload(AIX_PRELOAD_POOL_KEY.HOMEPAGE, {preloadMethod});
                            }
                        },
                        {timeout}
                    );
                }
                else {
                    controller = preload(AIX_PRELOAD_POOL_KEY.HOMEPAGE, {preloadMethod});
                }
            },
            timeout
        );
    }

    window.addEventListener('load', onLoad);

    const destructor = () => {
        controller?.abort();
        clearTimeout(t);
        window.removeEventListener('load', onLoad);
    };

    return [{controller}, destructor] as const;
}
