/* eslint-disable react/jsx-no-bind */
import {useCallback, useEffect, useState, ReactNode} from 'react';
import {Checkbox, Dropdown, Popover, Menu, Loading, Button as OneUiButton} from '@baidu/one-ui';
import {But<PERSON>} from '@baidu/light-ai-react';
import {
    IconChevronUp, IconTimes, IconLink, IconCnyCircle,
    IconSettings, IconDoubleCircle, IconChevronDown,
    IconMiniAppWechat, IconFileAdd
} from 'dls-icons-react';
import classNames from 'classnames';
import {OpenEditorFunc} from '@/components/common/materialList/EditorContainer';
import {useIsFeed} from '@/hooks/productLine';
import {makeLogger} from '../utils';
import './index.global.less';

const sendLog = makeLogger({source: 'operation_bar'});

interface DropdownPanelOption {
    label: string;
    value: string;
    icon?: ReactNode;
    disabled?: boolean;
    groupChildren?: DropdownPanelOption[];
    tip?: string;
}

interface ContentProps {
    childrenItem?: DropdownPanelOption[];
    dropdownPanelProps: any;
}

function Content({childrenItem = [], dropdownPanelProps}: ContentProps) {
    const {
        onOperationClick,
    } = dropdownPanelProps;
    const onChildrenItemClick = ({item, disabled}: {
        item: DropdownPanelOption;
        disabled?: boolean;
    }) => {
        if (disabled) {
            return;
        }
        onOperationClick({key: item.value, value: item.value});
    };
    return (
        <div className="drop-down-panel-item-content">
            {childrenItem.map(child => {
                return (
                    <Popover
                        placement="top"
                        content={child.tip}
                        key={child.label}
                    >
                        <div
                            key={child.label}
                            className={
                                classNames(
                                    'drop-down-panel-item-content-ele',
                                    {'drop-down-panel-item-content-ele-disabled': child.disabled}
                                )
                            }
                            onClick={() => onChildrenItemClick({item: child, disabled: child.disabled})}
                        >
                            {child.label}
                        </div>
                    </Popover>
                );
            })}
        </div>
    );
}

const GroupIconMap = {
    SETTING: <IconSettings />,
    PRICE: <IconCnyCircle />,
    WEBSITE: <IconLink />,
    PRICEANDBUDGET: <IconCnyCircle />,
    OPERATION: <IconDoubleCircle />,
    MONITOR: <IconMiniAppWechat />,
};

export default function DropdownPanel(props: {
    options: DropdownPanelOption[];
    onOperationClick: (evt: {value: string, key: string}) => void;
    size?: 'small' | 'medium';
}) {
    const {
        options,
        size = 'medium',
    } = props;
    return (
        <div className={`drop-down-panel drop-down-panel-size-${size}`}>
            {options.map(item => {
                return (
                    <div key={item.label} className="drop-down-panel-item">
                        <span className="panel-item-icon">
                            {item.icon || GroupIconMap[item.value]}
                        </span>
                        {item.label}
                        <Content
                            childrenItem={item.groupChildren}
                            dropdownPanelProps={props}
                        />
                    </div>
                );
            })}
        </div>
    );
}


interface IBaseProps {
    selectedCount: number;
    totalCount: number;
    level?: string;
    resetRowSelection(): void;
}

interface BatchOperatorsProps extends IBaseProps {
    children?: ReactNode;
    closable?: boolean;
    checkboxText?: string;
    placement?: 'top' | 'bottom';
    options?: DropdownPanelOption[];
    openEditor?: OpenEditorFunc;
    selectAll(): void;
}

function useBatchOperators({resetRowSelection, selectedCount, totalCount, level}: IBaseProps) {
    const closeOperationBar = useCallback(
        () => {
            resetRowSelection();
            sendLog({
                event: 'close',
                level,
                count: selectedCount,
            });
        },
        [resetRowSelection, level, selectedCount]
    );

    const [checked, setChecked] = useState(!!selectedCount && selectedCount === totalCount);
    useEffect(
        () => {
            setChecked(!!selectedCount && selectedCount === totalCount);
        },
        [selectedCount, totalCount]
    );
    return [{setChecked, closeOperationBar}, {checked}];
}

export function BatchOperators({
    selectedCount,
    totalCount,
    level,
    selectAll,
    resetRowSelection,
    children,
    closable = false,
    checkboxText = '全选',
    options,
    openEditor,
}: BatchOperatorsProps) {
    const [{setChecked, closeOperationBar}, {checked}] = useBatchOperators({
        level, resetRowSelection,
        selectedCount, totalCount,
    });

    const [visible, setVisible] = useState(false);

    const handleMenuClick = e => {
        openEditor && openEditor('batch', e.value);
        setVisible(false);
    };

    const dropDownProps = {
        options,
        handleMenuClick,
        trigger: ['click'],
        visible,
        onVisibleChange: setVisible,
        size: 'medium',
        buttonProps: {
            type: 'primary',
            className: 'batch-operators-button',
        },
        overlay: (
            <DropdownPanel
                options={(options || []).filter(v => v.value)}
                onOperationClick={handleMenuClick}
            />
        ),
    };

    return (
        <div className="manage-center-operators">
            <Checkbox
                indeterminate={!!selectedCount && selectedCount !== totalCount}
                checked={checked}
                onChange={e => {
                    const checked = e.target.checked as boolean;
                    setChecked(checked);
                    checked ? selectAll() : resetRowSelection();
                }}
            >
                {checkboxText}（已选{selectedCount}/{totalCount}）
            </Checkbox>
            {children}
            {
                options && (
                    <Dropdown {...dropDownProps}>
                        <Button variant="strong">批量编辑 <IconChevronUp /></Button>
                    </Dropdown>
                )
            }
            {closable && <IconTimes className="icon-close" onClick={closeOperationBar} />}
        </div>
    );
}

export function BatchCurrentPageOperators({
    selectedCount,
    totalCount,
    level,
    selectAll,
    resetRowSelection,
    children,
    closable = false,
    checkboxText = '全选',
    currentPageCount,
    placement = 'bottom',
}: BatchOperatorsProps & {
    currentPageCount: number;
}) {
    const [{setChecked, closeOperationBar}, {checked}] = useBatchOperators({
        level, resetRowSelection,
        selectedCount, totalCount: currentPageCount,
    });

    const cls = classNames('manage-center-operators', {
        'manage-center-operators-top': placement === 'top',
        'manage-center-operators-bottom': placement === 'bottom',
    });

    return (
        <div className={cls}>
            {
                placement === 'top' ? (
                    <span className="batch-checkbox-text">{checkboxText}（已选{selectedCount}/{totalCount}）</span>
                ) : (
                    <Checkbox
                        indeterminate={!!selectedCount && selectedCount !== currentPageCount}
                        checked={checked}
                        onChange={e => {
                            const checked = e.target.checked as boolean;
                            setChecked(checked);
                            checked ? selectAll() : resetRowSelection();
                        }}
                    >
                        <span className="batch-checkbox-text">{checkboxText}（已选{selectedCount}/{totalCount}）</span>
                    </Checkbox>
                )
            }
            <div className="batch-operation-buttons">
                {children}
            </div>
            {closable && <IconTimes className="icon-close" onClick={closeOperationBar} />}
        </div>
    );
}

// 列表上的批量条框架，没有checkbox，支持点击文字全选。
// eslint-disable-next-line complexity
interface BatchOperationBarProps {
    selectedCount: number;
    totalCount: number;
    children?: ReactNode;
    closable?: boolean;
    showTotalCount?: boolean;
    showCheckAllTip?: boolean;
    selectTitle?: string;
    placement?: 'top' | 'bottom';
    options?: DropdownPanelOption[];
    openEditor?: (type: ini, value?: string) => void;
    selectAll?(): void;
    onClose?(): void;
    level?: string;
    className?: string;
}
// eslint-disable-next-line complexity
export function BatchOperationBar(props: BatchOperationBarProps) {

    const {
        selectedCount,
        totalCount,
        selectAll,
        showTotalCount = true,
        closable = false,
        children = null,
        showCheckAllTip = true,
        selectTitle = '',
        onClose,
        placement = 'bottom',
        options,
        openEditor,
        className,
    } = props;

    const isFeedProduct = useIsFeed();

    const cls = classNames('manage-center-operators', {
        'manage-center-operators-top': placement === 'top',
        'manage-center-operators-bottom': placement === 'bottom',
        [className]: !!className,
        'is-feed': isFeedProduct
    });

    if (!selectedCount) {
        return null;
    }
    return (
        <div className={cls}>
            <div>
                {
                    selectedCount === totalCount
                        ? (
                            <div className="title">
                                {(totalCount && showTotalCount) ? `已选择全部${totalCount}行` : '已选择全部'}
                            </div>
                        ) : (
                            <div className="title no-wrap">
                                <span>{selectTitle || `已选${selectedCount}行`}</span>
                                {
                                    showCheckAllTip && !isFeedProduct
                                        ? (
                                            // @todo fc替换全选样式直接删除此处即可
                                            <span onClick={selectAll}>
                                                (或
                                                <span className="check-all-tip">
                                                    {(totalCount && showTotalCount) ? `选择全部${totalCount}行` : '选择全部'}
                                                </span>
                                                )
                                            </span>
                                        )
                                        : null
                                }
                            </div>
                        )
                }
            </div>
            <div className="batch-operation-buttons">
                {/* 别问为什么把这个全选切换按钮加这里，别问 */}
                {showCheckAllTip && isFeedProduct && selectedCount !== totalCount ? (
                    <Button variant="primary" className="check-all-btn" onClick={selectAll}>
                        <IconFileAdd />
                        {(totalCount && showTotalCount) ? `选择全部${totalCount}行` : '选择全部'}
                    </Button>
                ) : null}
                {children}
            </div>
            {options && <BatchOperationsDropdown options={options} openEditor={openEditor} placement={placement} />}
            {closable ? <IconTimes className="icon-close" onClick={onClose} /> : null}
        </div>
    );
}

export function BatchOperationsDropdown({
    options, openEditor, placement,
}: Pick<BatchOperationBarProps, 'options' | 'openEditor'> & {placement: 'top' | 'bottom'}) {
    const [visible, setVisible] = useState(false);

    const handleMenuClick = e => {
        openEditor && openEditor('batch', e.value);
        setVisible(false);
    };

    const dropDownProps = {
        options,
        handleMenuClick,
        trigger: ['click'],
        visible,
        onVisibleChange: setVisible,
        size: 'medium' as const,
        buttonProps: {
            type: 'primary',
        },
        overlay: (
            <DropdownPanel
                options={(options || []).filter(v => v.value)}
                onOperationClick={handleMenuClick}
            />
        ),
    };
    if (placement === 'top') {
        return (
            <Dropdown {...dropDownProps}>
                <OneUiButton type="text-strong" style={{color: '#fff'}}>
                    批量编辑 <IconChevronDown />
                </OneUiButton>
            </Dropdown>
        );
    }

    return (
        <Dropdown {...dropDownProps}>
            <Button variant="strong">
                批量编辑 <IconChevronUp />
            </Button>
        </Dropdown>
    );
}

export function ViewReportDropdown({onViewReport, disabled, options}: {
    onViewReport: (reportType: string) => void;
    disabled?: boolean;
    options: DropdownPanelOption[];
}) {
    const [visible, setVisible] = useState(false);

    const [pendingItem, setPendingItem] = useState('');
    const handleMenuClick = async e => {
        setPendingItem(e.value);
        try {
            onViewReport && await onViewReport(e.value);
        } catch (e) {
            console.error(e);
        } finally {
            setPendingItem('');
            setVisible(false);
        }
    };

    const dropDownProps = {
        disabled,
        options,
        handleMenuClick,
        trigger: ['click'],
        visible,
        onVisibleChange: setVisible,
        size: 'medium' as const,
        buttonProps: {
            type: 'primary',
        },
        overlay: (
            <Menu>
                {
                    options.map(v => (
                        <Menu.Item
                            disabled={v.value === pendingItem}
                            key={v.value}
                            onClick={() => handleMenuClick(v)}
                        >
                            {v.label}
                            {v.value === pendingItem && <Loading style={{marginLeft: 4}} />}
                        </Menu.Item>
                    ))
                }
            </Menu>
        ),
    };

    return (
        <Dropdown {...dropDownProps}>
            <OneUiButton type="primary">查看报告<IconChevronUp /></OneUiButton>
        </Dropdown>
    );
}
