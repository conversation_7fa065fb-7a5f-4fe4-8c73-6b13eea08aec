.manage-center-operators {
    flex-shrink: 0;
    height: 68px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 24px;
    position: sticky;
    bottom: 40px;
    width: calc(100% + 12px);
    background-color: #fff;
    z-index: 6;
    box-shadow:
        0 4px 28px 1px rgba(0, 71, 194, .04),
        0 5px 30px 1px rgba(0, 71, 194, .05),
        0 6px 32px 2px rgba(0, 71, 194, .06);

    .no-wrap {
        white-space: nowrap;
    }

    .check-all-btn {
        > span {
            display: inline-flex;
            align-items: center;

            svg {
                font-size: 18px;
            }
        }
    }

    // @todo 替换全选样式后可删除此处
    .check-all-tip {
        cursor: pointer;

        &:hover {
            text-decoration: underline;
        }
    }

    &.manage-center-operators-top {
        margin-left: 0;
        width: 100%;
        position: relative;
        background-color: #3a5bfd;
        height: 36px;
        border-radius: 6px;
        padding: 8px 16px;
        gap: 48px;
        font-weight: 600;
        font-size: 14px;
        line-height: 20px;
        bottom: 0;
        color: #fff;

        .one-ai-checkbox-wrapper {
            color: #fff;
        }

        .batch-operation-buttons {
            display: flex;
            align-items: center;
        }

        &.is-feed {
            gap: 24px;
        }
    }

    .icon-close {
        position: absolute;
        right: 24px;
        cursor: pointer;
        width: 18px;
        height: 18px;
    }

    .batch-operators-button {
        background: var(--Translucent-1, #6D9FF712);
        color: var(--Brand-7, #3A5BFD);
    }
}

.drop-down-panel {
    padding: 16px;
    gap: 4px;
    border-radius: 6px;
    background-color: #fff;
    width: 384px;
    box-shadow:
        0 4px 28px 1px rgba(0, 71, 194, .04),
        0 5px 30px 1px rgba(0, 71, 194, .05),
        0 6px 32px 2px rgba(0, 71, 194, .06);

    &-size-small {
        .drop-down-panel-item,
        .drop-down-panel-item-content {
            font-size: 12px;
        }
    }

    &-size-medium {
        .drop-down-panel-item,
        .drop-down-panel-item-content {
            font-size: 14px;
        }
    }

    &-item {
        color: #282c33;
        font-weight: 500;

        .panel-item-icon {
            color: #0052cc;
            line-height: 16px;
            font-weight: 500;
            margin-right: 4px;
        }
    }

    .drop-down-panel-item-content {
        margin: 12px 0 8px 0;
        line-height: 20px;
        color: #282c33;
        font-weight: 400;

        &-ele {
            min-width: 112px;
            line-height: 28px;
            padding-bottom: 8px;
            cursor: pointer;
            display: inline-block;

            &-disabled {
                cursor: not-allowed;
                color: #a9b0bf;

                &:hover {
                    color: #a9b0bf;
                }
            }

            &:hover {
                color: #3a5bfd;
            }
        }
    }

    .drop-down-panel-item:last-child {
        .drop-down-panel-item-content {
            margin-top: 12;
            margin-bottom: 0;
        }
    }
}
