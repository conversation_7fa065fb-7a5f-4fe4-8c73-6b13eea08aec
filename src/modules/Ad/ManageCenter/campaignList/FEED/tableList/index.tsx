/* eslint-disable no-negated-condition */
import {memo, useMemo, useEffect, useState, useCallback} from 'react';
import {Pagination, Table} from '@baidu/one-ui';
import classNames from 'classnames';
import {FeedProviderForQingge} from 'feedCommonLibs/qinggeProvider';
import getDepsFields from '@/utils/materialList/getDepsFields';
import globalData from '@/utils/globalData';
import {useDraggableColumns, useSortableColumns} from '@/hooks/tableList/columns';
import {toOneUIPaginationProps} from '@/hooks/pagination';
import {useControl} from '@/hooks/externalControl';
import {unshift} from '@/utils/array';
import {getOneTableLocale, toOneUIRowSelectionProps} from '@/utils/selection';
import {MaterialList} from '@/interface/tableList';
import {Empty} from '@/components/common/materialList/emptyText';
import EditorContainer from '@/components/common/materialList/EditorContainer';
import AsyncBanner from '@/components/FeedAsyncBanner';
import {useGetTables} from '@/components/common/FeedMaterialTabs/table';
import {getUserId} from '@/utils';
import {PageType} from '@/dicts/pageType';
import {scrollbarBottomFixedTableProps} from '@/config/tableList/config';
import {useURLQuery} from '@/hooks/tableList/query';
import {parseJsonWithFallback} from '@/utils/json';
import {useRegister} from '@/hooks/tableList/register';
import {levelTypeMapForLogOrSwr, platformPrefix} from '@/hooks/request/levelType';
import {makeLogger, ENTER_FORM_EDIT} from '@/modules/Ad/ManageCenter/common/utils';
import {PRODUCT} from '@/dicts/campaign';
import DetailDrawer_ from '@/components/DetailDrawer';
import {getColumnWidthStorageKeyPath, useObservedLocalStorage} from '@/hooks/storage';
import {Base} from '@/interface/base';
import {useManageCenterDateRange} from '../../../context';
import {getFeedCampaignRowKey, useFeedCampaignList} from './materialQuery';
import {relatedFields, needReplaceFields} from './tableConfig';
import {OperationBar} from './operationBar';
import {BatchOperation} from './batchOperation';
import editorConfig from './editorConfig';
import './index.global.less';

interface FeedCampaignTableListProps extends Pick<
    MaterialList.useColumnsRes, 'columns' | 'handleColumnAction' | 'filters'
> {
    reportType: number;
    columnConfiguration: MaterialList.ColumnConfiguration;
    modConfiguration: MaterialList.ModConfiguration;
    updateConfiguration: MaterialList.ModConfiguration;
    projectIds?: string[];
}

const feedBasicInfo = globalData.get('feedBasicInfo');
const FIELD = levelTypeMapForLogOrSwr.manageCampaignList;
const levelType = `${platformPrefix.FEED}-${FIELD}`;
const sendLog = makeLogger({level: FIELD, 'extra_params': PRODUCT.FEED});

function FeedCampaignTableList({
    reportType,
    filters,
    columns: columns_,
    handleColumnAction,
    updateConfiguration,
    modConfiguration,
    columnConfiguration,
}: FeedCampaignTableListProps) {
    const {startDate, endDate} = useManageCenterDateRange();
    const [query] = useURLQuery();
    const projectIds = parseJsonWithFallback(query.projectIds, []);
    const dateRange = useMemo(() => ({startDate, endDate}), [endDate, startDate]);
    const {customColumns} = columnConfiguration;
    const tables = useGetTables({
        accountId: getUserId(), type: PageType.CampaignList,
    });
    const customFields = useMemo(
        () => getDepsFields(customColumns, relatedFields, needReplaceFields),
        [customColumns]
    );

    // 详情抽屉
    const [DetailDrawer, {open: openDetailDrawer_}] = useControl(DetailDrawer_);
    const openDetailDrawer = useCallback(
        (...args: any[]) => {
            openDetailDrawer_(...args);
        },
        [openDetailDrawer_]
    );
    const registerOpenDetailDrawer = useCallback(
        () => handleColumnAction('openDetailDrawer', openDetailDrawer),
        [handleColumnAction, openDetailDrawer]
    );
    useRegister(registerOpenDetailDrawer);

    const [
        {
            pending,
            error,
            data: {summary, rows, totalCount},
            sorter,
            pagination,
            selection,
            asyncTasks,
        },
        {
            setPageNo,
            setPageSize,
            refresh,
            onSort,
            getCampaignById,
            getCampaignsByKeys,
            inlineSaveCampaign,
            selectionOperations,
            batchSaveCampaigns,
            batchDeleteCampaigns,
            modBatchCampaigns,
            inlineCopyCampaign,
            batchCopyCampaigns,
            batchBindProductLib,
            inlineBindProductLib,
        },
    ] = useFeedCampaignList({filters, date: dateRange, customFields, projectIds});
    const {onSelectChange, selectAll, resetRowSelection, getSelectedInfo} = selectionOperations;
    const dataSource = useMemo(
        () => (rows.length ? unshift(rows, {...summary, totalCount}) : []),
        [rows, summary, totalCount]
    );

    const {selectedCount, isCheckAll} = getSelectedInfo();
    const columns = useSortableColumns(columns_, sorter);
    const headBordered = useMemo(() => columns.some(col => col.children), [columns]);
    const paginationProps = toOneUIPaginationProps({...pagination, setPageNo, setPageSize}, totalCount);

    const columnWidthStorageKeyPath = getColumnWidthStorageKeyPath(levelType);
    const columnWidthStorage = useObservedLocalStorage(columnWidthStorageKeyPath, {});

    const filteredColumns = useMemo(() => {
        return feedBasicInfo?.userInfo?.isRtaUserAdmin
            ? columns.filter(column => column.accessorKey !== 'rtaStatus')
            : columns;
    }, [feedBasicInfo?.userInfo?.isRtaUserAdmin, columns]);
    const [
        columnsWithCustomWidth,
        {onDragEnd, resetColumnsWidth},
    ] = useDraggableColumns(
        filteredColumns,
        {storage: columnWidthStorage as Base.LocalStorageTuple<MaterialList.Columns>}
    );

    // to one-ui props
    const rowSelectionProps = useMemo(
        () => toOneUIRowSelectionProps(
            {selection, onSelectChange, selectAll, showSelectAll: true},
            rows,
            {getId: getFeedCampaignRowKey, multiPageSelection: false}
        ),
        [selection, onSelectChange, selectAll, rows],
    );

    const refreshAndResetRowSelection = useCallback(
        () => {
            refresh();
            resetRowSelection();
        },
        [refresh, resetRowSelection]
    );
    const tableListProps = {
        rowKey: getFeedCampaignRowKey,
        size: 'small',
        autoHideOperation: 'filter',
        locale: {
            emptyText: (
                <Empty
                    hasFilter={!!filters.length}
                    error={error}
                    text="当前账户下暂无营销方案"
                    name="营销方案"
                />
            ),
            ...(getOneTableLocale({isCheckAll, pageSize: pagination.pageSize, selectedCount}) || {})
        },
        pagination: false, // 如果采用表格的分页器，数据要为前端分页，如果是后端排序，现在仅支持自己写pager
        updateWidthChange: true, // 注意此属性，如果为false，浏览器缩放时不会重新计算表格宽度
        loading: pending,
        onSortClick: onSort,
        columns: columnsWithCustomWidth,
        onDragEnd,
        dataSource,
        headBordered,
        rowSelection: rowSelectionProps,
        headerBottom: <AsyncBanner {...asyncTasks} refresh={refreshAndResetRowSelection} />,
        ...scrollbarBottomFixedTableProps,
        headerFixTop: selectedCount ? 36 : 0,
    };

    useEffect(
        () => handleColumnAction('refresh', refresh),
        [handleColumnAction, refresh]
    );

    useEffect(
        () => handleColumnAction('inlineSaveCampaign', inlineSaveCampaign),
        [handleColumnAction, inlineSaveCampaign]
    );

    useEffect(
        () => handleColumnAction('batchSaveCampaigns', batchSaveCampaigns),
        [handleColumnAction, batchSaveCampaigns]
    );

    const [ControlledEditor, {openEditor}] = useControl(EditorContainer);
    useEffect(
        () => handleColumnAction('openEditor', openEditor),
        [handleColumnAction, openEditor]
    );

    const filterBarProps = {
        updateConfiguration,
        columnConfiguration,
        modConfiguration,
        summary,
        reportType,
        pending,
        resetColumnsWidth,
        refresh,
    };

    const batchEditProps = {
        selectionOperations,
        totalCount,
        batchSaveCampaigns,
        batchDeleteCampaigns,
        getCampaignsByKeys,
        openEditor,
        dateRange,
    };

    const editorSaveMethods = useMemo(
        () => ({
            inlineSaveCampaign, modBatchCampaigns, inlineCopyCampaign,
            batchSaveCampaigns, batchDeleteCampaigns, batchCopyCampaigns,
            batchBindProductLib, inlineBindProductLib,
        }),
        [
            batchDeleteCampaigns, batchSaveCampaigns, modBatchCampaigns,
            inlineSaveCampaign, inlineCopyCampaign, batchCopyCampaigns,
            batchBindProductLib, inlineBindProductLib,
        ]
    );

    const dialogEditorProps = {
        rows,
        getSelectedInfo,
        getMaterialById: getCampaignById,
        refreshList: refresh,
        saveMethods: editorSaveMethods,
        resetRowSelection,
        editorConfig,
        filters,
        sorter,
        dateRange,
        targetNameField: 'campaignFeedName',
        targetLevelName: '方案',
    };

    const cls = classNames({
        'manage-center-campaign-list__feed': true,
        'manage-center-campaign-list__feed_tabs': tables.length > 1,
        'has-filters': !!filters.length,
    });
    return (
        <FeedProviderForQingge basicInfo={feedBasicInfo}>
            <div className={cls}>
                <div>
                    <OperationBar {...filterBarProps} />
                </div>
                {!!selectedCount && <BatchOperation {...batchEditProps} />}
                <div className="table-list-container">
                    {(
                        // @ts-ignore
                        <Table {...tableListProps} />
                    )}
                    <Pagination className="table-pagination" {...paginationProps} />
                </div>
                {ControlledEditor && <ControlledEditor {...dialogEditorProps} />}
                <DetailDrawer level="campaign" />
            </div>
        </FeedProviderForQingge>
    );
}

export default memo(FeedCampaignTableList);
