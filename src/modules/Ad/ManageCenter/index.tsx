import {Suspense, useMemo, useEffect} from 'react';
import {partial} from 'lodash-es';
import {useBoolean} from 'huse';
import {Boundary} from 'react-suspense-boundary';
import {ConfigProvider} from '@baidu/one-ui';
import {Switch, Route, Redirect} from 'react-router-dom';
import AdRoutes, {getRoutesByPlatform, RoutesType} from '@/modules/Ad/routes';
import {Instructions} from '@/utils/instructions';
import {PLATFORM_ENUM} from '@/dicts';
import {useGlobalProductContext} from '@/hooks/productLine';
import {IActivities} from '../Activities/config';
import {renderError, recoverMethods} from '../hooks/routes';
import FCAccountTree from '../accountTree/FC';
import FeedAccountTree from '../accountTree/FEED';
import {ManageCenterDateRangeProvider} from './context';
import './index.global.less';
import {FcHorizontalNav, FeedHorizontalNav} from './horizontalNav';
import LevelInfo from './levelInfo';
import {FcToolsBar} from './ToolsBar/FC';
import {FeedToolsBar} from './ToolsBar/FEED';


interface ManageCenterContainerProps {
    instructions: Instructions;
}
const ManageCenter = ({instructions}: ManageCenterContainerProps) => {
    const {product} = useGlobalProductContext();
    const ManageCenterRoutes = useMemo(
        () => getRoutesByPlatform(AdRoutes.getRoutesByPrefixs?.(['ManageCenter']) as RoutesType, product),
        [product]
    );
    const [isHideHeader, {on, off}] = useBoolean(false);
    useEffect(() => {
        function eventHandle(event: any) {
            if (typeof event.data === 'object' && event.data.type === 'hideReportHeader') {
                on();
            }
            else if (typeof event.data === 'object' && event.data.type === 'showReportHeader') {
                off();
            }
        }
        window.addEventListener('message', eventHandle);
        return () => window.removeEventListener('message', eventHandle);
    }, []);
    return (
        <div className={`aix-manage-center-container${isHideHeader ? ' hide-header-and-padding' : ''}`}>
            {product === PLATFORM_ENUM.FC && <FCAccountTree />}
            {product === PLATFORM_ENUM.FEED && <FeedAccountTree />}
            <div className="aix-manage-center">
                <LevelInfo instructions={instructions} />
                <ManageCenterDateRangeProvider>
                    <div className="aix-manage-center__content">
                        {product === PLATFORM_ENUM.FC && <FcHorizontalNav />}
                        {product === PLATFORM_ENUM.FEED && <FeedHorizontalNav />}
                        <Switch>
                            {
                                ManageCenterRoutes.map(route => {
                                    const Component = route.component;
                                    if (route.type === 'redirect') {
                                        return <Redirect key={route.name} to={route.fullPath!} />;
                                    }

                                    return (
                                        <Route
                                            key={route.name}
                                            path={route.fullPath}
                                        >
                                            <Suspense fallback={null}>
                                                <Component instructions={instructions} {...route.props} />
                                            </Suspense>
                                        </Route>
                                    );
                                })
                            }
                        </Switch>
                    </div>
                </ManageCenterDateRangeProvider>
            </div>
            {product === PLATFORM_ENUM.FC && <FcToolsBar />}
            {product === PLATFORM_ENUM.FEED && <FeedToolsBar />}
        </div>
    );
};

export default function Manage(props: IActivities) {
    const {instructions} = props;
    return (
        <Boundary renderError={partial(renderError, {instructions, methods: [recoverMethods.refresh]})}>
            <ConfigProvider theme="light-ai">
                <ManageCenter instructions={instructions} />
            </ConfigProvider>
        </Boundary>
    );
}
