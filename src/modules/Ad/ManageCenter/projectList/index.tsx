/* eslint-disable complexity */
/* eslint-disable no-negated-condition */
import {memo, useCallback, useRef, useMemo, useEffect} from 'react';
import {useOriginalDeepCopy} from 'huse';
import {SWRConfig} from 'swr';
import {partial} from 'lodash-es';
import {CacheProvider, Boundary} from 'react-suspense-boundary';
import {useGlobalSwrCacheContext} from '@/dbProvider';
import {isDQA} from '@/dicts/marketingTarget';
import {useMouseEfficiencyMonitor, useDurationStay, sendScreenRecord} from '@/hooks';
import {useInitialFCProjectMarketingTargetId} from '@/modules/Ad/PromotionMain/FC/project/hooks';
import {PageType} from '@/dicts/pageType';
import {PRODUCT} from '@/dicts/campaign';
import {useAdRoute} from '@/modules/Ad/routes';
import aiChat from '@/utils/aiChat';
import {Instructions} from '@/utils/instructions';
import {getUserId, getQueryString} from '@/utils';
import {getManageCenterStorageKeyPath} from '@/config/storage';
import {useLocalStorageWithSyncURLQuery} from '@/hooks/storage';
import {parseJsonWithFallback} from '@/utils/json';
import globalData from '@/utils/globalData';
import {levelTypeMapForLogOrSwr} from '@/hooks/request/levelType';
import {INDUSTRY_SOLUTION} from '@/dicts/project';
import {useGlobalProductContext, useProjectProductWithSyncURLQuery} from '@/hooks/productLine';
import {renderError, recoverMethods} from '../../hooks/routes';
import {makeLogger, ENTER_LUI_CREATE, ENTER_GUI_CREATE} from '../common/utils';
import {GridMode} from '../common/GridModeSwitch';
import B2BProjectCardList from './B2B/cardList';
import {FEEDProjectListContainer} from './FEED/Container';
import {FcProjectListContainer} from './FC/Container';
import {CplProjectListContainer} from './CPL/Container';
import './index.global.less';

const FIELD = levelTypeMapForLogOrSwr.manageProjectList;
const sendLog = makeLogger({level: FIELD});

function getInitialProjectProductLine(globalProduct?: number) {
    const {fcProjectNum, feedProjectNum} = globalData.get('existingMaterialInfo') || {};
    if (globalProduct === PRODUCT.FC) {
        return PRODUCT.FC;
    }
    if ((feedProjectNum && !fcProjectNum) || globalProduct === PRODUCT.FEED) {
        return PRODUCT.FEED;
    }
    return PRODUCT.FC;
}

interface ProjectListContainerProps {
    instructions: Instructions;
}

/**
 * 此组件支持从url-query获取参数
 * @name initialFilters
 * @name aixProduct
 */
function ProjectListContainer({instructions}: ProjectListContainerProps) {
    const {linkToChat, linkTo} = useAdRoute();

    const gridModeStorageKeyPath = useMemo(
        () => getManageCenterStorageKeyPath('', `gridMode_${getUserId()}`),
        []
    );
    const [
        gridMode,
        setGridMode_,
    ] = useLocalStorageWithSyncURLQuery(gridModeStorageKeyPath, GridMode.Table, 'gridMode');

    const initialFilters = useOriginalDeepCopy(parseJsonWithFallback(getQueryString('initialFilters')) || []);

    const {product: globalProduct} = useGlobalProductContext();
    const initialProductLine = getInitialProjectProductLine(globalProduct);
    // 注：这里去除了原有的localStorage上保留上一次productLine逻辑，改为统一取全局context。除非url query上明确指定
    const [aixProduct, setAixProduct] = useProjectProductWithSyncURLQuery(initialProductLine);

    const onChangeAixProduct = useCallback(
        value => {
            setAixProduct(value);
            sendLog({event: 'change_productLine', source: 'operation_bar', 'extra_params': aixProduct});
        },
        [aixProduct, setAixProduct]
    );

    const setGridMode = useCallback(
        value => {
            setGridMode_(value);
            sendLog({event: 'change_gridMode', source: 'operation_bar', 'extra_params': aixProduct, target: gridMode});
        },
        [aixProduct, setGridMode_, gridMode]
    );

    const contentRef = useRef<HTMLDivElement>(null);
    useMouseEfficiencyMonitor(contentRef.current, FIELD);
    useDurationStay({field: FIELD, level: 'manage_list'});

    useEffect(
        () => {
            sendLog({
                event: 'pageview',
                source: aixProduct,
                target: gridMode,
                'extra_params': aixProduct,
            });
        },
        [aixProduct, gridMode]
    );

    const linkToCreateProjectInChat = useCallback(
        () => {
            linkToChat();
            aiChat.pushMessage(
                'user',
                ['新建项目'],
                {trigger: 'manage_center'}
            );
            sendScreenRecord('new_material');
        },
        [linkToChat]
    );

    const fcInitialMarketingTarget = useInitialFCProjectMarketingTargetId();

    const onClickCreateProjectMenu = useCallback(
        e => {
            if (e.key === 'linkToCreateFcProject') {
                // 新建项目时默认定位到上次使用的营销目标的项目表单
                const {marketingTargetId, subMarketingTargetId} = fcInitialMarketingTarget;
                linkTo(
                    isDQA(subMarketingTargetId) ? PageType.CreateProjectV2 : PageType.CreateProject,
                    {query: {marketingTargetId, ...(subMarketingTargetId ? {subMarketingTargetId} : {})}},
                );
                sendLog({event: ENTER_GUI_CREATE, source: 'operation_bar', 'extra_params': aixProduct});
            }
            if (e.key === 'linkToCreateLiteProject') {
                linkTo(PageType.CreateLiteProject);
                sendLog({event: ENTER_GUI_CREATE, source: 'operation_bar', 'extra_params': aixProduct});
            }
            if (e.key === 'linkToCreateFeedProject') {
                linkTo(PageType.CreateFeedProject);
                sendLog({event: ENTER_GUI_CREATE, source: 'operation_bar', 'extra_params': aixProduct});
            }
            if (e.key === 'linkToCreateProjectInChat') {
                linkToCreateProjectInChat();
                sendLog({event: ENTER_LUI_CREATE, source: 'operation_bar', 'extra_params': aixProduct});
            }
        },
        [linkTo, linkToCreateProjectInChat, aixProduct, fcInitialMarketingTarget]
    );

    const onSearchProject = useCallback(
        () => {
            sendLog({event: 'filter_project_name', source: 'operation_bar', 'extra_params': aixProduct});
        },
        [aixProduct]
    );

    const {dbProvider} = useGlobalSwrCacheContext();
    return (
        <>
            <CacheProvider>
                {
                    aixProduct === PRODUCT.FEED && (
                        <Boundary renderError={partial(renderError, {methods: [recoverMethods.refresh]})}>
                            <FEEDProjectListContainer
                                initialFilters={initialFilters}
                                gridMode={gridMode}
                                instructions={instructions}
                                linkToCreateProjectInChat={linkToCreateProjectInChat}
                                onClickCreateProjectMenu={onClickCreateProjectMenu}
                                onChangeGridMode={setGridMode}
                                onSearchProject={onSearchProject}
                            />
                        </Boundary>
                    )
                }
                {
                    aixProduct === PRODUCT.FC && (
                        <Boundary renderError={partial(renderError, {methods: [recoverMethods.refresh]})}>
                            <SWRConfig value={{provider: dbProvider}}>
                                <FcProjectListContainer
                                    initialFilters={initialFilters}
                                    onClickCreateProjectMenu={onClickCreateProjectMenu}
                                    onSearchProject={onSearchProject}
                                    onChangeAixProduct={onChangeAixProduct}
                                />
                            </SWRConfig>
                        </Boundary>
                    )
                }
                {
                    aixProduct === PRODUCT.B2B_PROMOTION && (
                        <Boundary renderError={partial(renderError, {methods: [recoverMethods.refresh]})}>
                            <B2BProjectCardList
                                initialFilters={initialFilters}
                                instructions={instructions}
                                onChangeAixProduct={onChangeAixProduct}
                                onClickCreateProjectMenu={onClickCreateProjectMenu}
                            />
                        </Boundary>
                    )
                }
                {
                    aixProduct === PRODUCT.FC_CPL && (
                        <Boundary renderError={partial(renderError, {methods: [recoverMethods.refresh]})}>
                            <CplProjectListContainer
                                initialFilters={initialFilters}
                                defaultFilters={
                                    [{field: 'industrySolution', op: 'in', values: [INDUSTRY_SOLUTION.KXT]}]
                                }
                                onClickCreateProjectMenu={onClickCreateProjectMenu}
                                onSearchProject={onSearchProject}
                                onChangeAixProduct={onChangeAixProduct}
                            />
                        </Boundary>
                    )
                }
            </CacheProvider>
        </>
    );
}

export default memo(ProjectListContainer);
