import {useMemo, useCallback, useImperativeHandle, forwardRef, Ref} from 'react';
import {useSWRConfig} from 'swr';
import {FC_PROJECT_LIST_REPORT_TYPE} from '@/dicts/reportType';
import {useColumns} from '@/hooks/tableList/columns';
import {FcIdType} from '@/dicts/idType';
import {isLiteProjectUser} from '@/utils/getFlag';
import {parseJsonWithFallback} from '@/utils/json';
import {useMaterialListConfiguration} from '@/hooks/tableList/configuration';
import FilterList, {FilterListOperations} from '@/components/common/materialList/FilterList';
import FilterEntry from '@/components/common/materialList/FilterEntry';
import {MaterialList} from '@/interface/tableList';
import {defaultOperatorConfig} from '@/config/tableList/defaultOperatorConfig';
import {ProductLineSelect} from '@/components/List/ProjectList/filters/ProductFilter';
import {PLATFORM_ENUM} from '@/dicts';
import {PRODUCT} from '@/dicts/campaign';
import {QuickSearchOpEnums, QuickSearchOptions} from '@/config/filters';
import {LiteProjectSimpleEntry} from '../../../LiteMain/FC/entry';
import {Calendar} from '../../common/Calendar';
import SearchBox from '../../common/SearchBox';
import {CreateProjectMultiEntry, CreateProjectMultiEntryProps} from '../slots/ProjectNewEntry';
import {tableFieldsMap} from './tableList/tableConfig';
import FcProjectTableList from './tableList';
import {usePrefetchCreateProjectResource} from './prefetch';

interface FcProjectListContainerProps extends CreateProjectMultiEntryProps {
    initialFilters?: MaterialList.FilterItem[];
    defaultFilters?: MaterialList.FilterItem[];
    onSearchProject: (...args: any[]) => void;
    onChangeAixProduct: (productId: PRODUCT) => void;
}

function FcProjectListContainer_(
    {
        initialFilters,
        defaultFilters,
        onClickCreateProjectMenu,
        onSearchProject,
        onChangeAixProduct,
    }: FcProjectListContainerProps,
    ref: Ref<unknown>
) {
    const {cache} = useSWRConfig(); // 不想额外在 getReportConfig 之前再搞个 useResource，直接用 homepage 预热的列表
    const fieldsToMerge = useMemo(() => fetchMergeFieldsByCache(cache, FC_PROJECT_LIST_REPORT_TYPE), [cache]);
    const [columnConfiguration, modConfiguration, control_, updateConfiguration] = useMaterialListConfiguration(
        FC_PROJECT_LIST_REPORT_TYPE,
        {withNewCategory: true, fieldsToMerge}
    );

    const {
        filters,
        columns,
        handleColumnAction,
        filterMethods,
        getFilterContentByField,
        getFilterDropdownByField,
    } = useColumns({
        columnConfiguration: {
            ...columnConfiguration,
            allColumns: columnConfiguration.allColumns.concat('consumptionProgress'),
        },
        tableFieldsMap,
        extraConfig: {initialFilters, enableParamsToQuery: true},
    });

    const filterListProps = {
        filters,
        deleteFilterByIndex: filterMethods.deleteFilterByIndex,
        changeFilterByIndex: filterMethods.changeFilterByIndex,
        getFilterContentByField,
    };

    const onSearch = useCallback(
        (value?: string, {operator}: {operator?: string} = {}) => {
            if (!value) {
                return;
            }
            const op = [QuickSearchOpEnums.idIn, QuickSearchOpEnums.nameIn].includes(operator as QuickSearchOpEnums)
                ? 'in'
                : operator;
            const field = operator === QuickSearchOpEnums.idIn ? 'projectId' : 'projectName';
            filterMethods.changeFilterByField(
                field,
                {
                    values: [value],
                    operator: op || defaultOperatorConfig.string[0].value,
                }
            );
        },
        [filterMethods.changeFilterByField]
    );
    useImperativeHandle(ref, () => ({onSearch}));

    // 预加载新建项目依赖的慢接口
    usePrefetchCreateProjectResource();

    return (
        <>
            {isLiteProjectUser() && <LiteProjectSimpleEntry className="lite-project-entry-in-list" />}
            <div className="manage-center-project-list-filter">
                <div id="manage-center-project-list-filter-create-entry">
                    <CreateProjectMultiEntry
                        onClickCreateProjectMenu={onClickCreateProjectMenu}
                        options={[
                            {label: '新建项目', value: 'linkToCreateFcProject'},
                            ...(
                                isLiteProjectUser()
                                    ? [{label: '新建极简投项目' as any, value: 'linkToCreateLiteProject'}]
                                    : []
                            ),
                        ]}
                    />
                </div>

                {/* 产品线筛选 */}
                <div id="project-list-product-line-select">
                    <ProductLineSelect platform={PLATFORM_ENUM.FC} value={PRODUCT.FC} onChange={onChangeAixProduct} />
                </div>

                <Calendar />
                <SearchBox
                    fieldName="项目"
                    onSearch={(...args: any[]) => {
                        onSearch(...args);
                        onSearchProject && onSearchProject(...args);
                    }}
                    operatorOptions={QuickSearchOptions}
                    width={155}
                    placeholder="查询项目名称/ID"
                />

                <FilterEntry
                    materialLevel={FcIdType.PROJECT_LEVEL}
                    columnConfigs={columnConfiguration.columnConfigs}
                    replaceFilters={filterMethods.replaceFilters}
                    changeFilterByField={filterMethods.changeFilterByField}
                    getFilterDropdownByField={getFilterDropdownByField}
                />
            </div>
            <FilterList {...filterListProps}>
                <FilterListOperations
                    materialLevel={FcIdType.PROJECT_LEVEL}
                    filters={filters}
                    resetFilters={filterMethods.resetFilters}
                />
            </FilterList>
            <FcProjectTableList
                reportType={FC_PROJECT_LIST_REPORT_TYPE}
                modConfiguration={modConfiguration}
                filters={filters}
                defaultFilters={defaultFilters}
                columns={columns}
                handleColumnAction={handleColumnAction}
                columnConfiguration={columnConfiguration}
                updateConfiguration={updateConfiguration}
            />
        </>
    );
}

export const FcProjectListContainer = forwardRef(FcProjectListContainer_);

interface ICachedValue {
    data?: {
        rows?: {
            transTypes?: number[];
            assistTransTypes?: number[];
        }[];
    };
}
function fetchMergeFieldsByCache(cache: Map<string, ICachedValue>, reportType: number) {
    const toAppend: string[] = [];
    let toRemove = ['ocpcBidType', 'projectModeType', 'targetRoiRatio', 'marketingTargetId'];
    try {
        for (const key of cache.keys()) { // 这里的 cache 已确保是这个 userid 的
            const {data = {}} = cache.get(key) as ICachedValue;
            if (parseJsonWithFallback(key, {}).reportType === reportType) {
                let deepTransNum = 0;
                const transTypes: Record<string, number> = {};
                const {rows = []} = data;

                if (rows.length) {
                    rows.forEach(row => {
                        if (row.assistTransTypes && row.assistTransTypes.length) {
                            deepTransNum++;
                        }
                        if (row.transTypes && row.transTypes.length) {
                            row.transTypes.forEach(item => {
                                transTypes[item] = (transTypes[item] || 0) + 1;
                            });
                        }
                    });
                    if (deepTransNum < rows.length / 10) {
                        toRemove = [ // 如果有深度但没选为自定义列的话也会被干掉（符合预期）
                            ...toRemove, 'assistTransTypes', 'suggestDeepBid',
                            'deepTransTypeMode', 'deepTransTypeStatus',
                        ];
                    }
                    const selectedTypes = Object.keys(transTypes);
                    if (selectedTypes.length > 1) { // 多个目标转化的话，先取缓存里最多的3个
                        selectedTypes.map(key => ({key, num: transTypes[key]}))
                            .sort((a, b) => (b.num - a.num)).slice(0, 3)
                            .forEach(item => toAppend.push(`ocpcConversionsDetail${item.key}`));
                    }
                    else if (selectedTypes.includes('120')) { // 或者有综合线索收集，取大盘比较多的3个
                        [1, 18, 30].forEach(type => toAppend.push(`ocpcConversionsDetail${type}`));
                    }
                    console.log('diff_result', toRemove, toAppend);
                    break;
                }
            }
        }
        return {toRemove, toAppend};
    }
    catch (e) {
        return {toRemove, toAppend};
    }
}
