import {identity} from 'lodash-es';
import {withSumCell} from '@/utils/handleSummary';
import {isMiniProgramChat} from '@/utils/getFeedFlag';
import ftyperange from '@/modules/Ad/ManageCenter/common/ftyperange';
import {SUBJECT_OBJECT_MAP} from '@/config/feed/campaign';
import {hasMbaidu} from '@/config/feed';
import planname from './columns/planname';
import ideaSource from './columns/ideaSource';
import ideaname from './columns/ideaname';
import ideatype from './columns/ideatype';
import unitname from './columns/unitname';
import ftype from './columns/ftype';
import sourceMaterial from './columns/sourceMaterial';
import subjecttype from './columns/subjecttype';
import ideastat from './columns/ideastat';
import ideamaterial from './columns/ideamaterial';
import operation from './columns/operation';
import accountType from './columns/accountType';
import appSubType from './columns/appSubType';
import applicationType from './columns/applicationType';

const idFilters = {
    filterType: 'string',
    operatorOptions: [
        {value: 'in', label: '包含'},
    ],
    defaultOperatorValue: 'in',
    maxLine: 50,
};

export const tableFieldsMap = {
    accountType,
    operation,
    ideastat,
    ftype,
    ideatype,
    ideamaterial,
    ideaname,
    unitname,
    planname,
    subjecttype,
    ideaSource,
    sourceMaterial,
    ideaid: {
        render: withSumCell(identity),
        filters: idFilters,
    },
    planid: {
        render: withSumCell(identity),
        filters: idFilters,
    },
    unitid: {
        render: withSumCell(identity),
        filters: idFilters,
    },
    appSubType,
    applicationType,
    ftyperange,
};

export const relatedFields = {
    ideamaterial: [
        'ideaid',
        'bstype',
        'subjecttype',
        'ideamaterial',
        'materialstyle',
        'bidtype',
        'planid',
        'unitid',
        'approvemsg',
        'pausestat',
        'shadow',
        'pageid',
        'noTriggerAuditProgress',
        'planType',
        'ideaBotStatus',
        'bjhFcTag',
        'bjhDoctorTag',
        ...(isMiniProgramChat()
            ? [
                'miniProgramType',
            ] : []
        ),
    ],
};

export const needReplaceColmns = {
    operation: [],
};

const IS_SIMPLE = 1;

export function checkSupportModAccountType(creative: any) {
    const {feedSubject, ftype, ocpcTransType, simpleType} = creative;
    return feedSubject === SUBJECT_OBJECT_MAP.bjh
        && hasMbaidu(ftype)
        && ocpcTransType === '付费观剧人数'
        && simpleType !== IS_SIMPLE;
}
