import {statusKeyMap, statusTextMap} from '@/config/feed/creative';
import {isFeedAIRepairVideoUser} from '@/utils/getFeedFlag';

export const classNameTypes = {
    normal: 'normal',
    warning: 'warning',
    error: 'error',
    success: 'success',
    info: 'info',
};

export const statusClassNames = {
    [statusKeyMap.ENABLE]: [classNameTypes.normal],
    [statusKeyMap.AUDIT_NO_PASS]: [classNameTypes.error],
    [statusKeyMap.AUDIT_ING]: [classNameTypes.warning],
    [statusKeyMap.PAUSE]: [classNameTypes.error],
    [statusKeyMap.INVALID]: [classNameTypes.error],
    [statusKeyMap.PART_VALID]: [classNameTypes.warning],
    [statusKeyMap.NOT_AUDIT]: [classNameTypes.warning],
};

const commonStatusFilterOptions = [
    {
        label: statusTextMap[statusKeyMap.ENABLE],
        value: statusKeyMap.ENABLE,
    },
    {
        label: statusTextMap[statusKeyMap.AUDIT_ING],
        value: statusKeyMap.AUDIT_ING,
    },
    {
        label: statusTextMap[statusKeyMap.PAUSE],
        value: statusKeyMap.PAUSE,
    },
    {
        label: statusTextMap[statusKeyMap.NOT_AUDIT],
        value: statusKeyMap.NOT_AUDIT,
    },
];
export const statusFilterOptions = [
    ...commonStatusFilterOptions,
    {
        label: statusTextMap[statusKeyMap.AUDIT_NO_PASS],
        value: statusKeyMap.AUDIT_NO_PASS,
    },
    // 随百家号创编需求，去除【无效】状态筛选
    // {
    //     label: statusTextMap[statusKeyMap.INVALID],
    //     value: statusKeyMap.INVALID,
    // },
    {
        label: statusTextMap[statusKeyMap.PART_VALID],
        value: statusKeyMap.PART_VALID,
    },
];
export const statusFilterOptionsWithRepair = [
    ...commonStatusFilterOptions,
    {
        label: statusTextMap[statusKeyMap.AUDIT_NO_PASS],
        value: 'audit_no_pass',
        children: [
            {
                label: statusTextMap[statusKeyMap.AUDIT_NO_PASS],
                value: statusKeyMap.AUDIT_NO_PASS,
            },
            {
                label: statusTextMap[statusKeyMap.ONLY_DISPLAY_REPAIR_NO_PASS],
                value: statusKeyMap.ONLY_DISPLAY_REPAIR_NO_PASS,
            },
        ],
    },
    {
        label: statusTextMap[statusKeyMap.PART_VALID],
        value: 'part_valid',
        children: [
            {
                label: statusTextMap[statusKeyMap.PART_VALID],
                value: statusKeyMap.PART_VALID,
            },
            {
                label: statusTextMap[statusKeyMap.ONLY_DISPLAY_REPAIR_PART_VALID],
                value: statusKeyMap.ONLY_DISPLAY_REPAIR_PART_VALID,
            },
        ],
    },
];

export const getStatusFilterOptions = () => {
    return isFeedAIRepairVideoUser() ? statusFilterOptionsWithRepair : statusFilterOptions;
};

export const BJH_AUDIT_STATUS_TEXT_MAP = {
    FAIL: '视频被删除或内容有安全问题。',
    // 部分通过
    PARTIAL_VALID: '目前在部分场景（作者主页/自然分发场景等）针对部分用户（作者/粉丝/全部用户）可见。如有疑问建议联系运营或销售发起内部流程协助复议。',
};

// 百家号视频内容审核状态
const BJH_AUDIT_STATUS = {
    // 审核中
    AUDIT_ING: 0,
    // 审核不通过
    NO_PASS: 1,
    // 自己可见
    SELF_VISIBLE: 2,
    // 仅作者主页可见
    AUTHOR_HOME_VISIBLE: 3,
    // 仅粉丝可见
    FANS_VISIBLE: 4,
    // 全部可见
    RECOMMEND: 5,
};

const PARTIAL_PASS_STATUS = [
    BJH_AUDIT_STATUS.SELF_VISIBLE,
    BJH_AUDIT_STATUS.AUTHOR_HOME_VISIBLE,
    BJH_AUDIT_STATUS.FANS_VISIBLE,
];

const NOT_PASS_BJH_STATUS = [
    BJH_AUDIT_STATUS.AUDIT_ING,
    BJH_AUDIT_STATUS.NO_PASS,
    BJH_AUDIT_STATUS.SELF_VISIBLE,
    BJH_AUDIT_STATUS.AUTHOR_HOME_VISIBLE,
    BJH_AUDIT_STATUS.FANS_VISIBLE,
];

export function getBjhAuditStatusText(bjhFcTag: number) {
    if (bjhFcTag === BJH_AUDIT_STATUS.NO_PASS) {
        return BJH_AUDIT_STATUS_TEXT_MAP.FAIL;
    }
    if (PARTIAL_PASS_STATUS.includes(bjhFcTag)) {
        return BJH_AUDIT_STATUS_TEXT_MAP.PARTIAL_VALID;
    }
    return '';
}

export const BjhDoctorTag = {
    OFF: 0,
    ON: 1,
};

export function isShowBjhContentAuditDetial(bjhFcTag: number, astat: number, ideastat: number) {
    if (ideastat === statusKeyMap.ENABLE) {
        return false;
    }
    return astat === statusKeyMap.ENABLE && NOT_PASS_BJH_STATUS.includes(bjhFcTag)
    || astat === statusKeyMap.AUDIT_NO_PASS || astat === statusKeyMap.AUDIT_ING;
}

export function getBjhHoverText(fcTag: number, astat: number, ideaStat: number) {

    if (ideaStat === statusKeyMap.ENABLE && fcTag === BJH_AUDIT_STATUS.RECOMMEND && astat === statusKeyMap.ENABLE) {
        return '最优投放中，可获得更多自然流量。';
    }
    if (ideaStat !== statusKeyMap.PART_VALID) {
        return '';
    }
    if (astat === statusKeyMap.ENABLE && NOT_PASS_BJH_STATUS.includes(fcTag)
        || (astat === statusKeyMap.AUDIT_NO_PASS || astat === statusKeyMap.AUDIT_ING)
        && fcTag === BJH_AUDIT_STATUS.RECOMMEND) {
        return '投放中，可进一步优化视频内容。';
    }
    return '';
}
