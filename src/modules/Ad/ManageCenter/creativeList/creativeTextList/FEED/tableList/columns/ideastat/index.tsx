/*
 * @Description: feed 创意列表 - 状态
 * @Author: lv<PERSON><PERSON>(<EMAIL>)
 * @Date: 2024-09-18 20:56:00
 * @Last Modified by: liuye11
 * @Last Modified time: 2025-07-15 17:10:40
 */

/* eslint-disable complexity */
import {useRef, useState} from 'react';
import {Tooltip, Button, Popover} from '@baidu/one-ui';
import {
    IconPlayCircle,
    IconPauseCircle,
    IconEllipsisCircle,
    IconAi,
    IconBulb,
} from 'dls-icons-react';
import {partial} from 'lodash-es';
import classnames from 'classnames';
import {PRODUCT} from '@/dicts/campaign';
import {withSumCell} from '@/utils/handleSummary';
import {createCustomRender} from '@/utils/render';
import {ENUM} from '@/config/filters';
import {displayError} from '@/utils/error';
import {IDEA_TYPE, PAUSE_STAT_MAP} from '@/config/feed';
import {checkHasProcessingDigitalVideo, statusKeyMap, statusTextMap} from '@/config/feed/creative';
import {makeLogger} from '@/modules/Ad/ManageCenter/common/utils';
import {levelTypeMapForLogOrSwr} from '@/hooks/request/levelType';
import {isFeedAIRepairVideoUser, isFeedBjhAuditstatusPublish} from '@/utils/getFeedFlag';
import {sendMonitor} from '@/utils/logger';
import {getBjhHoverText, getStatusFilterOptions, isShowBjhContentAuditDetial, statusClassNames} from './config';
import Status from './Status';
import {getUnitPlugin} from './api';
import ApproveMsgLayer from './approveMsgLayer';
import {getApproveContent} from './utils';
import './style.global.less';

const commonDigitalVideoTip = '该创意中的数字人视频正在合成中，预计需要1-2分钟，请在视频合成后再进行';
const FIELD = levelTypeMapForLogOrSwr.manageCreativeTextList;
const sendLog = makeLogger({level: FIELD, source: 'inline', 'extra_params': PRODUCT.FEED});

// 获取组件审核信息
function getPluginMessage(astat, failReason) {
    switch (+astat) {
        case 0: return '审核通过';
        case 2: return '审核中';
        case 1: return failReason;
        default: return 'astat未返回';
    }
}

export function IdeaStat(
    {record, inlineUpdateCreativeApi, onOpenAuditDrawer: onOpenAuditDrawer_, onOpenAiRepairDrawer}:
    {
        record: Record<string, any>;
        inlineUpdateCreativeApi: (ideaid: number, data: {pause: boolean}) => Promise<void>;
        onOpenAuditDrawer: () => void;
        onOpenAiRepairDrawer: () => void;
    }
) {
    const carouselRef = useRef();
    const [current, setCurrent] = useState(0);
    const [pluginState, setPluginState] = useState({message: '', isRejected: false});
    const {
        ideastat, bjhFcTag, auditstat, pausestat, ideaid, ideatype,
        approvemsgnew, approvemsg, unitid, bjhapprovemsg,
    } = record;
    const paused = PAUSE_STAT_MAP[pausestat as keyof typeof PAUSE_STAT_MAP] === 'PAUSED';
    const hasProcessingDigitalVideo = checkHasProcessingDigitalVideo(record);
    const bjhAduitStatusText = isFeedBjhAuditstatusPublish() && !paused
        ? getBjhHoverText(bjhFcTag, auditstat, ideastat) : '';
    const statusProps = {
        status: ideastat,
        statusClassNames,
        statusKeyMap,
    };
    const label = statusTextMap[ideastat] || `未识别的状态：${JSON.stringify(ideastat)}`;
    const dottedUnderscore = statusClassNames[ideastat]?.[1];
    const statusTextClassName = classnames(
        'feed-creative-list-status-text',
        'feed-creative-list-multiple-cut',
        {
            'feed-creative-list-status-text-dotted-underscore': dottedUnderscore,
        }
    );

    // 未审核
    const isNoAudit = [
        statusKeyMap.NOT_AUDIT,
    ].includes(+ideastat);

    // 自定义创意-审核通过 且有返回信息 layer显示信息
    const isCustomSuccessWithMsg = (+ideatype === IDEA_TYPE.CUSTOM)
    && (+ideastat === statusKeyMap.ENABLE);

    // 自定义创意-审核详情-审核不通过
    const isCustomNoValid = (+ideatype === IDEA_TYPE.CUSTOM)
        && (+ideastat === statusKeyMap.AUDIT_NO_PASS || +ideastat === statusKeyMap.PART_VALID);

    const isBjhAudit = isFeedBjhAuditstatusPublish() && !!record.bjhNid;
    // 程序化创意-部分拒绝和审核不通过 可以打开弹窗查看详细拒绝理由
    const isProgramNolValid = (+ideatype === IDEA_TYPE.PROGRAM)
        && (+ideastat === statusKeyMap.AUDIT_NO_PASS || +ideastat === statusKeyMap.PART_VALID);
    const isShowBjhContentAudit = isBjhAudit && isShowBjhContentAuditDetial(bjhFcTag, auditstat, ideastat);
    // 程序化部分拒绝和审核不通过时 打开审核详情
    const onOpenResult = async () => {
        const res = await getUnitPlugin({unitId: unitid});
        // message为false代表：没有填写组件信息
        let message = false;
        // 是否被拒绝
        let isRejected = false;
        if (res.listData.length > 0) {
            const astat = +res.listData[0].astat;
            isRejected = astat === 1;
            const rejectReason = getPluginMessage(astat, res.listData[0].reason);
            try {
                message = JSON.parse(rejectReason).join('；');
            }
            catch (err) {
                message = rejectReason;
            }
        }
        setPluginState({message, isRejected});
        sendMonitor('click', {
            level: 'feed_idea_list',
            field: 'idea_list',
            'item': 'openResult',
        });
    };


    const updateStatus = async (e: React.MouseEvent<HTMLElement>) => {
        // TODO 埋点对齐老平台
        e.stopPropagation();
        try {
            await inlineUpdateCreativeApi(ideaid, {pause: !paused});
        }
        catch (err: any) {
            err.optName = '修改创意状态';
            displayError(err);
            throw err;
        }
        sendMonitor('click', {
            level: 'feed_idea_list',
            field: 'idea_list',
            'item': 'updateStatus',
        });
    };
    const Icon = paused ? IconPlayCircle : IconPauseCircle;

    const {
        customNolValidContent,
    } = getApproveContent({
        carouselRef,
        item: record,
        isCustomNoValid,
        isProgramNolValid,
        current,
        // 自定义只有拒绝时才显示
        ideaPluginError: pluginState.isRejected && pluginState.message,
        // callback
        onCarouselChange: state => setCurrent(state),
        onJumpToSilde: idx => carouselRef.current?.goTo(idx),
        onOpenResult,
    });

    const onOpenAuditDrawer = (e: any) => {
        onOpenAuditDrawer_({record, oldAuditDetail: customNolValidContent}, e);
        sendMonitor('click', {
            level: 'feed_idea_list',
            field: 'idea_list',
            'item': 'audit',
        });
    };

    const onClickAIRepairBtn = (e: any) => {
        sendLog({event: 'click_ai_repair_creative_btn'});
        onOpenAiRepairDrawer({record, oldAuditDetail: customNolValidContent}, e);
    };

    const statusDetailProps = {
        isNoAudit,
        onOpenAuditDrawer,
        isCustomNoValid,
        approvemsgnew,
        approvemsg,
        bjhapprovemsg,
        isProgramNolValid,
        isCustomSuccessWithMsg,
        isShowBjhContentAudit,
    };

    const canRepairByAI = isFeedAIRepairVideoUser()
        && [statusKeyMap.AUDIT_NO_PASS, statusKeyMap.PART_VALID].includes(+ideastat)
        && record?.repairSolutions?.length > 0;
    return (
        <Status {...statusProps}>
            <div className="creative-status-content-wrapper">
                <Tooltip title={bjhAduitStatusText}>
                    <span className={statusTextClassName} title={label}>{label}</span>
                </Tooltip>
                <StatusDetail {...statusDetailProps} />
                <Tooltip title={hasProcessingDigitalVideo ? `${commonDigitalVideoTip}操作` : ''}>
                    <Button
                        type="text"
                        size="small"
                        icon={Icon}
                        className="inline-operation-icon button-icon"
                        disabled={hasProcessingDigitalVideo}
                        onClick={updateStatus}
                    />
                </Tooltip>
            </div>
            {
                canRepairByAI && (
                    <Popover
                        zIndex={1}
                        placement="right"
                        overlayClassName="feed-creative-repair-popover"
                        content={(
                            <div className="feed-creative-repair-popover-content">
                                <div className="creative-repair-popover-title">
                                    <IconBulb />
                                    拒审视频一键修复
                                </div>
                                <div className="creative-repair-popover-desc">
                                    基于视频素材审核的建议，系统利用AI修复违规问题，生成新的视频素材供投放，您可在「状态」列快速筛选可修复创意，快来使用吧！
                                </div>
                                <div className="creative-repair-popover-operate-btn">
                                    <div onClick={onClickAIRepairBtn}>立即查看</div>
                                </div>
                            </div>
                        )}
                    >
                        <div className="creative-repair-tag" onClick={onClickAIRepairBtn}>
                            <IconAi />
                            一键修复
                        </div>
                    </Popover>
                )
            }
        </Status>
    );
}

export default {
    render: createCustomRender((_, {trigger}) => withSumCell(
        (_, record) => {
            const onOpenAuditDrawer = partial(trigger, 'openAuditDrawer');
            const onOpenAiRepairDrawer = partial(trigger, 'openAiRepairDrawer');
            return (
                <IdeaStat
                    record={record}
                    inlineUpdateCreativeApi={partial(trigger, 'inlineUpdateCreative')}
                    onOpenAuditDrawer={onOpenAuditDrawer}
                    onOpenAiRepairDrawer={onOpenAiRepairDrawer}
                />
            );
        }
    )),
    filters: {
        options: getStatusFilterOptions(),
        filterType: ENUM,
    },
};
function StatusDetail(props) {
    const {
        isNoAudit,
        onOpenAuditDrawer,
        isCustomNoValid,
        approvemsgnew,
        approvemsg,
        bjhapprovemsg,
        isProgramNolValid,
        isCustomSuccessWithMsg,
        isShowBjhContentAudit,
    } = props;
    const shwoCustomDetail = isNoAudit
    || isCustomNoValid && (approvemsgnew || approvemsg || bjhapprovemsg)
    || isShowBjhContentAudit;
    return (
        <>
            {
            // 未审核 || 自定义创意-审核详情-审核不通过 || 百家号有内审信息
                shwoCustomDetail
            && (
                <Tooltip placement="topLeft" title="点击查看审核详情">
                    <IconEllipsisCircle className="inline-audit-icon" onClick={onOpenAuditDrawer} />
                </Tooltip>
            )}
            {
            // 程序化创意-审核详情-部分拒绝和审核不通过
                isProgramNolValid
            && (
                <Tooltip placement="topLeft" title="点击查看审核详情">
                    <IconEllipsisCircle className="inline-audit-icon" onClick={onOpenAuditDrawer} />
                </Tooltip>
            )}
            {
            // 自定义创意-审核通过 且有返回信息
                isCustomSuccessWithMsg && <ApproveMsgLayer />}
        </>
    );
}

