import {useCallback, useEffect, forwardRef, Ref} from 'react';
import {SWRConfig} from 'swr';
import {useColumns} from '@/hooks/tableList/columns';
import {FcIdType} from '@/dicts/idType';
import {PageType} from '@/dicts/pageType';
import {useMaterialListConfiguration} from '@/hooks/tableList/configuration';
import FilterList, {FilterListOperations} from '@/components/common/materialList/FilterList';
import {MaterialList} from '@/interface/tableList';
import MaterialTabs from '@/components/common/MaterialTabs';
import {defaultOperatorConfig} from '@/config/tableList/defaultOperatorConfig';
import {FC_ADGROUP_LIST_REPORT_TYPE} from '@/dicts/reportType';
import FilterEntry from '@/components/common/materialList/FilterEntry';
import {useGlobalSwrCacheContext} from '@/dbProvider';
import {QuickSearchOpEnums, QuickSearchOptions} from '@/config/filters';
import {Calendar} from '../../common/Calendar';
import SearchBox from '../../common/SearchBox';
import {
    CreateAdgroupMultiEntry,
    CreateAdgroupEntryProps,
    CreateAdgroupMultiEntryProps,
} from '../slots/AdgroupNewEntry';
import {tableFieldsMap, supportedFilterEntryFields} from './tableList/tableConfig';
import FcAdgroupTableList from './tableList';

interface FcAdgroupListContainerProps extends CreateAdgroupEntryProps, CreateAdgroupMultiEntryProps {
    initialFilters?: MaterialList.FilterItem[];
    onSearchAdgroup?: (...args: any[]) => void;
}
function FcAdgroupListContainer_(
    {
        initialFilters,
        onClickCreateAdgroupMenu,
        onSearchAdgroup,
    }: FcAdgroupListContainerProps,
    ref: Ref<unknown>
) {
    const [columnConfiguration, modConfiguration, control_, updateConfiguration] = useMaterialListConfiguration(
        FC_ADGROUP_LIST_REPORT_TYPE,
        {withNewCategory: true}
    );

    const {
        filters,
        columns,
        handleColumnAction,
        filterMethods,
        getFilterContentByField,
        getFilterDropdownByField,
    } = useColumns({columnConfiguration, tableFieldsMap, extraConfig: {initialFilters, enableParamsToQuery: true}});

    const {
        updateOrAddFilterByField,
        deleteFilterByIndex,
        changeFilterByIndex,
    } = filterMethods;

    const filterListProps = {
        filters,
        deleteFilterByIndex,
        changeFilterByIndex,
        getFilterContentByField,
    };

    useEffect(
        () => handleColumnAction('updateOrAddFilterByField', updateOrAddFilterByField),
        [handleColumnAction, updateOrAddFilterByField]
    );

    const onSearch = useCallback(
        (value?: string, {operator}: {operator?: string} = {}) => {
            if (!value) {
                return;
            }
            const op = [QuickSearchOpEnums.idIn, QuickSearchOpEnums.nameIn].includes(operator as QuickSearchOpEnums)
                ? 'in'
                : operator;
            const field = operator === QuickSearchOpEnums.idIn ? 'adgroupId' : 'adgroupName';
            filterMethods.changeFilterByField(
                field,
                {
                    values: [value],
                    operator: op || defaultOperatorConfig.string[0].value,
                }
            );
        },
        [filterMethods.changeFilterByField]
    );
    const {dbProvider} = useGlobalSwrCacheContext();
    return (
        <>
            <MaterialTabs currentPageType={PageType.AdgroupList} />
            <div className="manage-center-adgroup-list-filter">
                <CreateAdgroupMultiEntry onClickCreateAdgroupMenu={onClickCreateAdgroupMenu} />

                {/* 日期区间 */}
                <Calendar />

                <SearchBox
                    fieldName="单元"
                    onSearch={(...args: any[]) => {
                        onSearch(...args);
                        onSearchAdgroup && onSearchAdgroup(...args);
                    }}
                    operatorOptions={QuickSearchOptions}
                    width={190}
                    placeholder="查询单元名称/ID"
                />

                <FilterEntry
                    materialLevel={FcIdType.UNIT_LEVEL}
                    filterFields={supportedFilterEntryFields}
                    columnConfigs={columnConfiguration.columnConfigs}
                    replaceFilters={filterMethods.replaceFilters}
                    changeFilterByField={filterMethods.changeFilterByField}
                    getFilterDropdownByField={getFilterDropdownByField}
                />
            </div>
            <FilterList {...filterListProps}>
                <FilterListOperations
                    materialLevel={FcIdType.UNIT_LEVEL}
                    filters={filters}
                    resetFilters={filterMethods.resetFilters}
                />
            </FilterList>
            <SWRConfig value={{provider: dbProvider}}>
                <FcAdgroupTableList
                    reportType={FC_ADGROUP_LIST_REPORT_TYPE}
                    modConfiguration={modConfiguration}
                    updateConfiguration={updateConfiguration}
                    filters={filters}
                    columns={columns}
                    handleColumnAction={handleColumnAction}
                    columnConfiguration={columnConfiguration}
                    onSearch={onSearch}
                    ref={ref}
                />
            </SWRConfig>
        </>
    );
}

export const FcAdgroupListContainer = forwardRef(FcAdgroupListContainer_);
