import {useRequest} from 'huse';
import {concat} from 'lodash-es';
import {FormConfig, FormProvider, useQuickForm} from '@baidu/react-formulator';
import {CascaderPane, ProviderConfig, Select} from '@baidu/one-ui';
import {ForwardedRef, forwardRef, useCallback, useImperativeHandle, useMemo, useState} from 'react';
import {PRODUCT} from '@/dicts/campaign';
import {useAdRoute} from '@/modules/Ad/routes';
import {PageType} from '@/dicts/pageType';
import {getFeedPlanTree} from '@/api/manage/campaign';
import {SUPPORT_BSTYPE_LIST, SUPPORT_SUBJECT_LIST} from '@/config/feed/guiEditor';
import {levelTypeMapForLogOrSwr} from '@/hooks/request/levelType';
import {ENTER_GUI_CREATE, makeLogger} from '@/modules/Ad/ManageCenter/common/utils';
import {QueryFromType} from '@/dicts/query';
import {CHOOSE_LEVEL} from '@/components/ChatCards/SelectedCampaignAdgroup/pureConfig';
import {FeedSubjectType, FeedSubjectTypeNamesMap} from '@/dicts/subject';
import {FeedSaleTypeNameMap} from '@/config/project/feed';
import {BJHTypeMap} from '@/components/Project/FeedEditor/BJHType/config';
import {transNameMapByValue} from '@/config/transType';
import {subjectTypeFilterOptions} from '@/modules/Ad/ManageCenter/campaignList/FEED/tableList/columns/subjecttype';
import {BsType} from '@/interface/aixProject/feedProject';
import './style.global.less';

const FIELD = levelTypeMapForLogOrSwr.manageAdgroupList;
const sendLog = makeLogger({level: FIELD, source: 'operation_bar', 'extra_params': `${PRODUCT.FEED}|0`});

function getInfoOptions(selectedValueData: any) {
    return [
        {
            key: 'subject',
            label: '营销目标',
            value: FeedSubjectTypeNamesMap[selectedValueData.subjecttype] || '-',
        },
        ...(selectedValueData.subjecttype === FeedSubjectType.salesLead ? [{
            key: 'saleType',
            label: '营销场景',
            value: FeedSaleTypeNameMap[selectedValueData.saleType] || '-',
        }] : []),
        ...(selectedValueData.subjecttype === FeedSubjectType.bjh ? [{
            key: 'bjhType',
            label: '营销场景',
            value: BJHTypeMap[selectedValueData.bjhType] || '-',
        }] : []),
        ...(selectedValueData.ocpc?.transType ? [{
            key: 'transType',
            label: '优化目标',
            value: transNameMapByValue[selectedValueData.ocpc?.transType] || '-',
        }] : []),
        ...(selectedValueData.ocpc?.ocpcBid ? [{
            key: 'ocpcBid',
            label: '目标转化成本',
            value: `${selectedValueData.ocpc?.ocpcBid}元` || '-',
        }] : []),
        ...(selectedValueData.ocpc?.deepTransType ? [{
            key: 'deepTransType',
            label: '深度优化目标',
            value: transNameMapByValue[selectedValueData.ocpc?.deepTransType] || '-',
        }] : []),
        ...(selectedValueData.ocpc?.deepOcpcBid ? [{
            key: 'deepOcpcBid',
            label: '深度目标转化成本',
            value: `${selectedValueData.ocpc?.deepOcpcBid}元` || '-',
        }] : []),
    ];
}

interface CampaignSelectorWithInfoProps {
    campaignList: any[];
    pending: boolean;
    value?: number;
    onChange?: (value: any) => void;
}

export const SubjectTypeSelectOptions = concat(
    {
        value: FeedSubjectType.todo,
        label: '不限',
    },
    ...subjectTypeFilterOptions,
);

const CATALOGUE_SUBJECT_TYPE = -1;

export const filterSubject = (subject: FeedSubjectType | number, campaignListOptions: any[]) => {
    switch (subject) {
        case FeedSubjectType.todo:
            return campaignListOptions;
        case CATALOGUE_SUBJECT_TYPE:
            return campaignListOptions.filter(item => {
                return [BsType.pa].includes(item.bstype);
            });
        case FeedSubjectType.app:
            return campaignListOptions.filter(item => {
                return [
                    FeedSubjectType.android, FeedSubjectType.ios, FeedSubjectType.hmos,
                    FeedSubjectType.androidLive, FeedSubjectType.iosLive, FeedSubjectType.appLink,
                ].includes(item.subjecttype) && [
                    BsType.na, BsType.store, BsType.onlineStore, BsType.rta
                ].includes(item.bstype);
            });
        default:
            return campaignListOptions.filter(item => {
                return item.subjecttype === subject
                && [BsType.na, BsType.store, BsType.onlineStore, BsType.rta].includes(item.bstype);
            });
    }
};

function CampaignSelectorWithInfo({
    campaignList,
    pending,
    value,
    onChange,
}: CampaignSelectorWithInfoProps) {

    const campaignListOptions = useMemo(() => {
        const formattedList = campaignList.filter(item => {
            return SUPPORT_SUBJECT_LIST.includes(item.subjecttype) && SUPPORT_BSTYPE_LIST.includes(item.bstype);
        }).map(item => ({
            ...item,
            campaignName: item.planname,
            campaignId: item.planid,
            label: item.planname,
            value: item.planid,
            level: CHOOSE_LEVEL.CAMPAIGN_LEVEL,
            isLeaf: true,
        }));

        return formattedList;
    }, [campaignList]);

    const selectedValueData = useMemo(() => (campaignList.find(item => item.planid === value) || {})
        , [value, campaignList]);

    const infoOptions = useMemo(() => getInfoOptions(selectedValueData), [selectedValueData]);

    const onSelect = useCallback((targetOption, index, activeValue) => {
        onChange(targetOption.campaignId);
    }, [onChange]);

    const [selectedSubject, setSelectedSubject] = useState(FeedSubjectType.todo);

    const subjectSelectProps = {
        value: selectedSubject,
        onChange: setSelectedSubject,
        width: 348,
        customRenderTarget: (v, item) => {
            return (
                <span>
                    <span>营销目标:</span>
                    <span>{item.label}</span>
                </span>
            );
        },
    };

    const subjectFilteredCampaignList = useMemo(() => {
        return filterSubject(+selectedSubject, campaignListOptions);
    }, [campaignListOptions, selectedSubject]);

    const onClickSearchItem = useCallback((searchOption: any[]) => {
        const values = searchOption.map(option => option.value);
        onChange(values[0]);
    }, [onChange]);

    const cascaderPaneStyle = {
        width: '100%',
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0,
        '--dls-dropdown-max-display-items': 9.5,
        'boxShadow': 'none',
    };

    return (
        <div className="selected-campaign-adgroup-wrapper-drawer">
            <div className="level-title">
                <div className="left-text">
                    可选范围
                </div>
                <div className="right-filter">
                    <Select {...subjectSelectProps}>
                        {SubjectTypeSelectOptions.map(item => (
                            <Select.Option key={item.value} value={item.value}>
                                {item.label}
                            </Select.Option>
                        ))}
                    </Select>
                </div>
            </div>
            <div className="selected-campaign-adgroup-container">
                <div className="selected-campaign-adgroup-select">
                    <div className="selected-campaign-adgroup-select-title">
                        可选方案（{subjectFilteredCampaignList.length}）
                    </div>
                    <CascaderPane
                        className="selected-campaign-adgroup-cascader"
                        options={subjectFilteredCampaignList || []}
                        value={value ? [value] : []}
                        onSelect={onSelect}
                        emptyNode={pending ? <>加载中</> : <>暂无数据</>}
                        cascaderPaneStyle={cascaderPaneStyle}
                        showSearch
                        onClickSearchItem={onClickSearchItem}
                    />
                </div>
                <div className="selected-campaign-adgroup-info">
                    <div className="title">已选内容</div>
                    {value && (
                        <div className="info">
                            {infoOptions.map((item: any) => (
                                <div className="info-item" key={item.key || item.label}>
                                    <span className="info-item-label">{item.label}：</span>
                                    <span>{item.value}</span>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

const getFormConfig = ({campaignList, pending}: {campaignList: any[], pending: boolean}) => {
    const formConfig: FormConfig<{campaignFeedId: number}> = {
        fields: [{
            field: 'campaignFeedId',
            label: null,
            use: [CampaignSelectorWithInfo],
            rules: [['required', '请选择方案']],
            componentProps: {campaignList, pending},
        }],
    };
    return formConfig;
};

function CampaignSeletorForm(props, ref: ForwardedRef<any>) {

    const {data = {}, pending} = useRequest(getFeedPlanTree, {});
    const campaignList = useMemo(() => data.rows || [], [data.rows]);

    const formConfig = useMemo(() => {
        return getFormConfig({campaignList, pending});
    }, [campaignList, pending]);

    const {linkTo} = useAdRoute();
    const [Form, {validateFields}] = useQuickForm();
    const onSave = async () => {
        const values = await validateFields();
        const selecetdItem = campaignList.find((item: any) => item.planid === values.campaignFeedId);
        sendLog({event: ENTER_GUI_CREATE});
        linkTo(
            PageType.CreateFeedAdgroup,
            {
                query: {
                    campaignId: selecetdItem.planid,
                    subjectType: selecetdItem.subjecttype,
                    bsType: selecetdItem.bstype,
                    from: QueryFromType.adgroupList,
                },
            }
        );
    };
    useImperativeHandle(ref, () => ({onSave}));

    return (
        <div className="campaign-selector">
            <FormProvider
                value={{
                    inputErrorClassName: 'one-ai-invalid',
                    showFirstError: true,
                }}
            >
                <ProviderConfig theme="light-ai">
                    <Form
                        config={formConfig}
                        data={{campaignId: null}}
                        className="ai-build-form-element use-rf-preset-form-ui use-horizontal use-label-top"
                    />
                </ProviderConfig>
            </FormProvider>
        </div>
    );
}

export default forwardRef(CampaignSeletorForm);
