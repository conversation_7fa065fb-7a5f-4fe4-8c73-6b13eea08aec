import {memo, useMemo, useCallback, useEffect, useState, useImperativeHandle, forwardRef} from 'react';
import {Pagination, Table, TableProps} from '@baidu/one-ui';
import classNames from 'classnames';
import queryString from 'query-string';
import {FeedProviderForQingge} from 'feedCommonLibs/qinggeProvider';
import {Empty} from '@/components/common/materialList/emptyText';
import {useControl} from '@/hooks/externalControl';
import {useDraggableColumns, useSortableColumns} from '@/hooks/tableList/columns';
import {toOneUIPaginationProps} from '@/hooks/pagination';
import {useRegister} from '@/hooks/tableList/register';
import {unshift} from '@/utils/array';
import getDepsFields from '@/utils/materialList/getDepsFields';
import {getOneTableLocale, toOneUIRowSelectionProps} from '@/utils/selection';
import globalData from '@/utils/globalData';
import {CATALOG_SOURCE} from '@/config/feed';
import EditorContainer from '@/components/common/materialList/EditorContainer';
import AsyncBanner from '@/components/FeedAsyncBanner';
import {MaterialList} from '@/interface/tableList';
import {makeLogger, ENTER_FORM_EDIT} from '@/modules/Ad/ManageCenter/common/utils';
import {useGetTables} from '@/components/common/FeedMaterialTabs/table';
import {getUserId} from '@/utils';
import {PageType} from '@/dicts/pageType';
import {scrollbarBottomFixedTableProps} from '@/config/tableList/config';
import {PRODUCT} from '@/dicts/campaign';
import {useAdRoute} from '@/modules/Ad/routes';
import {useFeedConfByQuery} from '@/hooks/tableList/query';
import {levelTypeMapForLogOrSwr, platformPrefix} from '@/hooks/request/levelType';
import {guideIdConfig} from '@/config/guideId';
import {showFeedPlatformGuide, JUMP_TO_NEXT_PAGE_MAP} from '@/modules/Ad/hooks/useShowFeedPlatformGuide';
import DetailDrawer_ from '@/components/DetailDrawer';
import {QueryFromType} from '@/dicts/query';
import {getColumnWidthStorageKeyPath, useObservedLocalStorage} from '@/hooks/storage';
import {Base} from '@/interface/base';
import {useManageCenterDateRange} from '../../../context';
import {relatedFields} from './tableConfig';
import {useTableList, getAdgroupRowKey} from './hooks';
import ProductDrawer from './components/product';
import JiMuYuAssoluteProductDrawer from './components/jiMuYuAssoluteProductDrawer';
import MedicAssoluteProductDrawer from './components/medicAssoluteProductDrawer';
import JiMuYuAssoluteProductDrawerWithBatch from './components/jiMuYuAssoluteProductDrawer/batch';
import MedicAssoluteProductDrawerWithBatch from './components/medicAssoluteProductDrawer/batch';
import AdgroupDiagnosisDrawer_ from './columns/operation/diagnosisDrawer';
import OperationBar from './components/operationBar';
import {BatchOperation} from './components/batchOperation';
import {editorConfig} from './editor';
import './index.global.less';


const FIELD = levelTypeMapForLogOrSwr.manageAdgroupList;
const levelType = `${platformPrefix.FEED}-${FIELD}`;
const sendLog = makeLogger({level: FIELD, 'extra_params': PRODUCT.FEED});

interface FeedAdgroupTableListProps extends Pick<
    MaterialList.useColumnsRes, 'columns' | 'handleColumnAction' | 'filters'
> {
    reportType: number;
    columnConfiguration: MaterialList.ColumnConfiguration;
    modConfiguration: MaterialList.ModConfiguration;
    updateConfiguration: MaterialList.ModConfiguration;
    onSearch: (value?: string, options?: {operator?: string}) => void;
}

function Main({
    reportType,
    filters,
    columns: columns_,
    handleColumnAction,
    updateConfiguration,
    modConfiguration,
    columnConfiguration,
    onSearch,
}: FeedAdgroupTableListProps, ref: any) {
    const {startDate, endDate} = useManageCenterDateRange();
    const dateRange = useMemo(() => ({startDate, endDate}), [endDate, startDate]);
    const {customColumns} = columnConfiguration;
    const tables = useGetTables({
        accountId: getUserId(), type: PageType.AdgroupList,
    });
    const customFields = useMemo(
        () => getDepsFields(customColumns, relatedFields),
        [customColumns]
    );

    const [
        {
            pending,
            error,
            data: {summary, rows, totalCount},
            sorter,
            pagination,
            selection,
            asyncTasks,
        },
        {
            setPageNo,
            setPageSize,
            refresh,
            onSort,
            selectionOperations,
            getAdgroupById,
            getAdgroupsByKeys,
            inlineUpdateAdgroup,
            inlineCopyAdgroupApi,
            batchUpdateAdgroups,
            batchDeleteAdgroups,
            batchCopyAdgroups,
            batchUpdateAdgroupsBid,
        },
    ] = useTableList({filters, date: dateRange, customFields});

    const dataSource = useMemo(
        () => (rows.length ? unshift(rows, {...summary, totalCount}) : []),
        [rows, summary, totalCount]
    );
    const columns = useSortableColumns(columns_, sorter);

    const columnWidthStorageKeyPath = getColumnWidthStorageKeyPath(levelType);
    const columnWidthStorage = useObservedLocalStorage(columnWidthStorageKeyPath, {});
    const [
        columnsWithCustomWidth,
        {onDragEnd, resetColumnsWidth},
    ] = useDraggableColumns(columns, {storage: columnWidthStorage as Base.LocalStorageTuple<MaterialList.Columns>});

    const {onSelectChange, selectAll, resetRowSelection, selectRows, getSelectedInfo} = selectionOperations;
    const rowSelectionProps = useMemo(
        () => toOneUIRowSelectionProps(
            {selection, onSelectChange, selectAll, showSelectAll: true},
            rows,
            {getId: getAdgroupRowKey, multiPageSelection: false}
        ),
        [selection, onSelectChange, selectAll, rows],
    );

    const refreshAndResetRowSelection = useCallback(
        () => {
            refresh();
            resetRowSelection();
        },
        [refresh, resetRowSelection]
    );

    const {selectedCount, isCheckAll} = getSelectedInfo();
    // 表格props
    const tableListProps = {
        className: 'manage-center-project-table',
        rowKey: getAdgroupRowKey,
        size: 'small',
        autoHideOperation: 'filter',
        locale: {
            emptyText: (
                <Empty
                    hasFilter={!!filters.length}
                    error={error}
                    text="暂无数据"
                    name="单元"
                />
            ),
            ...(getOneTableLocale({isCheckAll, selectedCount, pageSize: pagination.pageSize}) || {})
        } as TableProps['locale'],
        pagination: false, // 如果采用表格的分页器，数据要为前端分页，如果是后端排序，现在仅支持自己写pager
        updateWidthChange: true, // 注意此属性，如果为false，浏览器缩放时不会重新计算表格宽度
        loading: pending,
        onSortClick: onSort as TableProps['onSortClick'],
        columns: columnsWithCustomWidth,
        onDragEnd,
        dataSource,
        rowSelection: rowSelectionProps,
        headerBottom: <AsyncBanner {...asyncTasks} refresh={refreshAndResetRowSelection} />,
        ...scrollbarBottomFixedTableProps,
        headerFixTop: selectedCount ? 36 : 0,
    };
    // 分页器props
    const paginationProps = toOneUIPaginationProps({...pagination, setPageNo, setPageSize}, totalCount);

    // 顶部header-自定义列 or 下载组件props
    const operationBarProps = {
        updateConfiguration,
        columnConfiguration,
        modConfiguration,
        summary,
        reportType,
        refresh,
        resetColumnsWidth,
        pending,
    };

    // 刷新操作
    const registerRefresh = useCallback(() => {
        return handleColumnAction('refresh', refresh);
    }, [handleColumnAction, refresh]);
    useRegister(registerRefresh);

    // 行内编辑
    useEffect(
        () => handleColumnAction('inlineUpdateAdgroup', inlineUpdateAdgroup),
        [handleColumnAction, inlineUpdateAdgroup]
    );
    const [ControlledEditor, {openEditor, closeEditor}] = useControl(EditorContainer);
    useEffect(
        () => handleColumnAction('openEditor', openEditor),
        [handleColumnAction, openEditor]
    );

    // 行内编辑-关联产品
    const [AdgroupProductDrawer, {open: openProductDrawer_}] = useControl(ProductDrawer);
    const openProductDrawer = useCallback(
        (...args: any[]) => {
            openProductDrawer_(...args);
            sendLog({event: ENTER_FORM_EDIT, source: 'inline'});
        },
        [openProductDrawer_]
    );
    const registerOpenProductDrawer = useCallback(
        () => handleColumnAction('openProductDrawer', openProductDrawer),
        [handleColumnAction, openProductDrawer]
    );
    useRegister(registerOpenProductDrawer);

    // 这里下线了，移到详情了，暂时注释掉
    // const [AdgroupDiagnosisDrawer, {open: openDiagnosisDrawer_}] = useControl(AdgroupDiagnosisDrawer_);
    // const openDiagnosisDrawer = useCallback(
    //     (...args: any[]) => {
    //         openDiagnosisDrawer_(...args);
    //         sendLog({event: ENTER_FORM_EDIT, source: 'inline'});
    //     },
    //     [openDiagnosisDrawer_]
    // );
    // const registerOpenDiagnosisDrawer = useCallback(
    //     () => handleColumnAction('openDiagnosisDrawer', openDiagnosisDrawer),
    //     [handleColumnAction, openProductDrawer]
    // );
    // useRegister(registerOpenDiagnosisDrawer);

    // 行内编辑-关联产品-基木鱼内容库来源（销售线索）
    const [
        AdgroupJiMuYuAssoluteProductDrawer,
        {open: openJimuyuProductDrawer_},
    ] = useControl(JiMuYuAssoluteProductDrawer);
    const openJimuyuProductDrawer = useCallback(
        (...args: any[]) => {
            openJimuyuProductDrawer_(...args);
            sendLog({event: ENTER_FORM_EDIT, source: 'inline'});
        },
        [openJimuyuProductDrawer_]
    );
    const registerOpenJimuyuProductDrawer = useCallback(
        () => handleColumnAction('openjimuyuDataDrawer', openJimuyuProductDrawer),
        [handleColumnAction, openJimuyuProductDrawer]
    );
    useRegister(registerOpenJimuyuProductDrawer);

    // 详情抽屉
    const [DetailDrawer, {open: openDetailDrawer_}] = useControl(DetailDrawer_);
    const openDetailDrawer = useCallback(
        (...args: any[]) => {
            openDetailDrawer_(...args);
        },
        [openDetailDrawer_]
    );
    const registerOpenDetailDrawer = useCallback(
        () => handleColumnAction('openDetailDrawer', openDetailDrawer),
        [handleColumnAction, openDetailDrawer]
    );
    useRegister(registerOpenDetailDrawer);

    // 行内编辑-关联产品-医疗库来源（销售线索）
    const [
        AdgroupMedicAssoluteProductDrawer,
        {open: openMedicProductDrawer_},
    ] = useControl(MedicAssoluteProductDrawer);
    const openMedicProductDrawer = useCallback(
        (...args: any[]) => {
            openMedicProductDrawer_(...args);
            sendLog({event: ENTER_FORM_EDIT, source: 'inline'});
        },
        [openMedicProductDrawer_]
    );
    const registerOpenMedicProductDrawer = useCallback(
        () => handleColumnAction('openMedicDataDrawer', openMedicProductDrawer),
        [handleColumnAction, openMedicProductDrawer]
    );
    useRegister(registerOpenMedicProductDrawer);

    // 批量编辑-关联产品-基木鱼内容库来源（销售线索）
    const [jiMuYuAssoluteVisible, setJiMuYuAssoluteVisible] = useState(false);
    const onBatchProductSubmit = useCallback(async (values: any) => {
        await batchUpdateAdgroups(values);
        setJiMuYuAssoluteVisible(false);
        resetRowSelection();
    }, [resetRowSelection, batchUpdateAdgroups]);
    const jiMuYuDrawerWithBatchProps = {
        visible: jiMuYuAssoluteVisible,
        onConfirm: onBatchProductSubmit,
        onCancel: () => setJiMuYuAssoluteVisible(false),
    };

    const [medicAssoluteVisible, setMedicAssoluteVisible] = useState(false);
    const medicDrawerWithBatchProps = {
        medicAssoluteVisible,
        setMedicAssoluteVisible,
        batchUpdateAdgroups,
    };

    const onBatchProduct = useCallback((catalogSource: number) => {
        if (catalogSource === CATALOG_SOURCE.content) {
            setJiMuYuAssoluteVisible(true);
        }
        else if (catalogSource === CATALOG_SOURCE.medic) {
            setMedicAssoluteVisible(true);
        }
        else {
            openEditor('batch', 'product', undefined, {
                containerType: 'drawer',
                width: 800,
                hideDefaultFooter: true,
                type: 'basic',
            });
        }
    }, [openEditor]);

    const editorSaveMethods = useMemo(
        () => ({
            batchUpdateAdgroups, batchCopyAdgroups,
            inlineUpdateAdgroup, inlineCopyAdgroupApi,
            batchUpdateAdgroupsBid,
        }),
        [batchUpdateAdgroups, batchCopyAdgroups, inlineUpdateAdgroup, inlineCopyAdgroupApi, batchUpdateAdgroupsBid]
    );

    // 批量操作器props
    const batchOperationProps = {
        selectionOperations,
        totalCount,
        getAdgroupsByKeys,
        batchUpdateAdgroups,
        batchDeleteAdgroups,
        onBatchProduct,
        openEditor,
    };

    const dialogEditorProps = {
        rows,
        getSelectedInfo,
        getMaterialById: getAdgroupById,
        refreshList: refresh,
        saveMethods: editorSaveMethods,
        resetRowSelection,
        selectRows,
        editorConfig,
        filters,
        sorter,
        dateRange,
        targetNameField: 'unitname',
        targetLevelName: '单元',
        closeEditor,
    };

    const {linkTo} = useAdRoute();
    const {campaignId, bsType} = useFeedConfByQuery();
    const onAddAdgroup = useCallback(() => {
        // 计划层级面包屑的情况需要处理不同的逻辑
        if (campaignId) {
            linkTo(
                PageType.CreateFeedAdgroup,
                {query: {campaignId: campaignId, bsType, from: QueryFromType.adgroupList}}
            );
        }
        openEditor('batch', 'newAdgroup');
    }, [openEditor, campaignId, linkTo, bsType]);
    useImperativeHandle(ref, () => ({onSearch, onAddAdgroup}));

    useEffect(() => {
        // 添加单元sug点击会跳转到本页面，如果url中有openDrawer=true，则打开新建单元抽屉
        const query = queryString.parse(location.search);
        if (query.openDrawer) {
            openEditor('batch', 'newAdgroup');
        }
    }, [openEditor]);

    useEffect(() => {
        if (dataSource.length) {
            const guideId = guideIdConfig.FEED_PLATFORM_GUIDE_ADGROUP_LIST;
            showFeedPlatformGuide({
                guideId,
                onFinish: () => {
                    linkTo(JUMP_TO_NEXT_PAGE_MAP[guideId]);
                },
            });
        }
    }, [dataSource]);

    const [feedBasicInfo] = useState(() => globalData.get('feedBasicInfo'));

    const cls = classNames({
        'manage-center-adgroup-list': true,
        'manage-center-adgroup-list__feed_tabs': tables.length > 1,
        'has-filters': filters.length > 0,
    });
    return (
        <FeedProviderForQingge basicInfo={feedBasicInfo}>
            <div className={cls}>
                <div>
                    <OperationBar {...operationBarProps} />
                </div>
                {!!selectedCount && <BatchOperation {...batchOperationProps} />}
                <div className="table-list-container">
                    <Table {...tableListProps} />
                    <Pagination className="table-pagination" {...paginationProps} />
                </div>
                <AdgroupProductDrawer refresh={refresh} />
                <AdgroupJiMuYuAssoluteProductDrawer refresh={refresh} />
                <AdgroupMedicAssoluteProductDrawer refresh={refresh} />
                {/* <AdgroupDiagnosisDrawer /> */}
                <JiMuYuAssoluteProductDrawerWithBatch {...jiMuYuDrawerWithBatchProps} />
                <MedicAssoluteProductDrawerWithBatch {...medicDrawerWithBatchProps} />
                {ControlledEditor && <ControlledEditor {...dialogEditorProps} />}
                <DetailDrawer level="adgroup" />
            </div>
        </FeedProviderForQingge>
    );
}

export default memo(forwardRef(Main));
