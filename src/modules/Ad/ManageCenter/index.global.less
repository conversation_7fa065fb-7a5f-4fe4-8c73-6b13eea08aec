@import 'src/styles/mixin/list.global.less';

.aix-manage-center {
    flex: 1 0 auto;
    width: 0;
    transition: width .4s linear 0ms;
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding-bottom: 40px;

    .page-title-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
        height: 36px;
        margin-bottom: 16px;

        .page-title-with-breadcrumb {
            display: flex;
            align-items: flex-start;
            gap: 12px;

            .title {
                font-size: 18px;
                font-weight: 600;
                line-height: 22px;
                white-space: nowrap;
            }
        }
    }

    .page-title {
        font-size: 18px;
        font-weight: 600;
        line-height: 26px;
        margin-bottom: 12px;
    }

    .sheet-tabs {
        margin-top: -12px;
    }

    &__content {
        padding: 4px 24px 24px 24px;
        display: flex;
        flex-direction: column;
        position: relative;
        background: #fff;
        border-radius: 10px;

        .common-table-columns();
        .common-filter-list();

        .operation-bar-and-operator-container {
            background-color: #fff;
            padding-bottom: 16px;
        }

        .operation-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #fff;
            height: 40px;

            .area {
                display: flex;
                align-items: center;
                height: 32px;
                gap: 8px;

                .tip {
                    display: flex;
                    align-items: center;
                    color: #848b99;
                    font-size: 12px;
                }
            }
        }
    }

    .page-title + &__content {
        height: calc(100% - 38px);
    }

    &__loading {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;

        &-image {
            width: 100%;
        }
    }

    &__empty {
        height: 300px;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
    }
}

.manage-center-material-list-inline-popover-editor {
    .brave-the-winds-common-libs-save-footer {
        margin-top: 16px;
    }
}

.aix-manage-center-container.hide-header-and-padding {
    padding-bottom: 0;

    .aix-tool-bar,
    .qingge-account-tree-affix,
    .aix-manage-center-level-info,
    .aix-manage-center-horizontal-nav {
        display: none;
    }

    .aix-manage-center__content {
        padding: 0;
    }
}

.aix-manage-center-container {
    width: 100%;
    display: flex;
    align-items: flex-start;
    gap: 12px;

    .aix-tool-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 999;
    }
}
