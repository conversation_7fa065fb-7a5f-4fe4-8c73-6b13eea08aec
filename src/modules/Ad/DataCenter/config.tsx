import {
    IconAdDeliveryCircleSolid,
    IconBullseyeHitSolid,
    IconCheckDesktopSolid,
    IconLandingPageSolid,
    IconReportSettingsSolid,
    IconReportSolid,
    IconUserLabelSolid,
    IconFileStarSolid,
    IconReportCheckSolid,
} from 'dls-icons-react';
import {PLATFORM_ENUM} from '@/dicts';
import {PageType} from '@/dicts/pageType';
import {isFeedBusinessPointReportUser} from '@/utils/getFeedFlag';
import {
    isFcLiveUser, isLawIndustryUser, isLiteProjectUser,
    isFcNativeDqaUser, isFcNativeBJUser, isFcCplUser, isFcNativeTuWenUser,
} from '@/utils/getFlag';
import {fetchTopCustomReports} from '@/api/getReportData';
import {MenuItemConfig} from '@/components/Header/types';

/**
 * 获取数据中心菜单配置（新版MenuItemConfig格式）
 * @param aixProduct 产品线
 * @returns 符合MenuItemConfig结构的菜单配置
 */
export function getDataCenterMenuConfig({aixProduct}: {aixProduct?: PLATFORM_ENUM}): MenuItemConfig[] {
    return [
        {
            key: 'wanhuatong',
            title: '自定义报告',
            icon: <IconReportSettingsSolid className="header-group-icon" />,
            children: [
                {
                    key: PageType.ReportWanHuaTong,
                    title: '我的报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.ReportWanHuaTong,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '自定义报告',
                            '定制报告',
                            '我的报告',
                            '自定义数据',
                            '定制数据',
                            '我的数据',
                        ],
                        description: '自定义数据/使用模板查看投放数据',
                    },
                },
            ],
            childrenLoader: {
                load: async () => {
                    const reports = await fetchTopCustomReports({weirwoodIgnored: true});
                    return reports.map((item, index) => ({
                        key: `${PageType.ReportWanHuaTong}${index + 1}`,
                        title: item.reportName,
                        action: {
                            type: 'internal' as const,
                            navkey: `${PageType.ReportWanHuaTong}${index + 1}`,
                            query: {
                                reportId: item.reportId,
                            },
                        },
                    }));
                },
            },
        },
        {
            key: 'promotionReport',
            title: '推广报告',
            icon: <IconCheckDesktopSolid className="header-group-icon" />,
            children: [
                {
                    key: PageType.AccountReport,
                    title: '账户报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.AccountReport,
                        query: {
                            globalProduct: aixProduct,
                            accountReportType: `${aixProduct === PLATFORM_ENUM.FC ? 'fc' : 'feed'}AccountReport`,
                        },
                    },
                    flashLinkConfig: {
                        keywords: [
                            '账户数据',
                            '账户消费数据',
                            '数据报告',
                            '账户点击数据',
                            '账户展现数据',
                            '账户转化数据',
                        ],
                        order: 12,
                        description: '查看账户整体投放效果数据',
                    },
                },
                {
                    key: PageType.ProjectReport,
                    title: '项目报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.ProjectReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '项目数据',
                            '项目消费数据',
                            '项目点击数据',
                            '项目展现数据',
                            '项目转化数据',
                            '转化量',
                            '深度转化量',
                            '转化率',
                        ],
                        order: 15,
                        description: '查看项目层级投放效果数据',
                    },
                },
                {
                    key: PageType.CampaignReport,
                    title: '方案报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.CampaignReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '计划报告',
                            '计划数据',
                            '计划消费数据',
                            '计划点击数据',
                            '计划展现数据',
                            '计划转化数据',
                            '计划转化量',
                            '计划转化率',
                        ],
                        order: 14,
                        description: '查看方案层级投放效果数据',
                    },
                },
                {
                    key: PageType.AdgroupReport,
                    title: '单元报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.AdgroupReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '单元报告',
                            '单元数据',
                            '单元消费数据',
                            '单元点击数据',
                            '单元展现数据',
                            '单元转化数据',
                            '单元转化量',
                            '单元转化率',
                        ],
                        description: '查看单元层级投放效果数据',
                    },
                },
            ],
        },
        {
            key: 'targetingReport',
            title: '定向报告',
            icon: <IconBullseyeHitSolid className="header-group-icon" />,
            children: [
                {
                    key: PageType.KeywordReport,
                    title: '关键词报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.KeywordReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '关键词报告',
                            '关键词数据',
                            '关键词消费数据',
                            '关键词点击数据',
                            '关键词展现数据',
                            '关键词转化数据',
                            '关键词点击价格',
                        ],
                        order: 13,
                        description: '查看关键词粒度投放效果数据',
                    },
                },
                {
                    key: PageType.QueryWordReport,
                    title: '搜索词报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.QueryWordReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '搜索词报告',
                            '搜索词数据',
                            '触发模式',
                            '搜索词点击数据',
                            '搜索词展现数据',
                            '搜索词点击率',
                            '搜索词点击价格',
                        ],
                        order: 11,
                        description: '查看搜索词投放数据',
                    },
                },
                {
                    key: PageType.MarketingPointReport,
                    title: '营销要点报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.MarketingPointReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '营销方案',
                            '营销要点',
                            '营销要点数据',
                            '营销方案数据',
                        ],
                        description: '营销要点投放效果数据',
                    },
                },
                {
                    key: PageType.AcgGoodsReport,
                    title: '爱采购商品报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.AcgGoodsReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '爱采购商品报告',
                            '爱采购商品数据',
                        ],
                        description: '爱采购商品投放效果数据',
                    },
                },
                {
                    key: PageType.SplitRegionReport,
                    title: '分地域报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.SplitRegionReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '分地域报告',
                            '地域数据',
                            '省份数据',
                            '城市数据',
                            '地区数据',
                            '省份',
                            '城市',
                            '地区',
                            '地域',
                        ],
                        description: '分地域/地区/城市投放效果数据',
                    },
                },
                {
                    key: PageType.TargetReport,
                    title: '定向分析报告',
                    platforms: [PLATFORM_ENUM.FEED],
                    action: {
                        type: 'internal',
                        navkey: PageType.TargetReport,
                    },
                },
                {
                    key: PageType.CrowdReport,
                    title: '人群报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.CrowdReport,
                    },
                    flashLinkConfig: {
                        description: '查看投放人群对应展现/点击/消费/转化等效果数据',
                    },
                },
                ...(
                    isFcNativeBJUser()
                        ? [{
                            key: PageType.NoteReport,
                            title: '笔记报告',
                            platforms: [PLATFORM_ENUM.FC],
                            action: {
                                type: 'internal' as const,
                                navkey: PageType.NoteReport,
                            },
                            flashLinkConfig: {
                                keywords: [
                                    '笔记报告',
                                    '笔记数据',
                                    '笔记',
                                    '百家号笔记',
                                ],
                                description: '原生互动笔记投放效果数据',
                            },
                        }]
                        : []
                ),
                ...(isFcNativeDqaUser() ? [{
                    key: PageType.QuestionKnowledgeReport,
                    title: '问答报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal' as const,
                        navkey: PageType.QuestionKnowledgeReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '问答报告',
                            '问答数据',
                            '问答',
                        ],
                        description: '问答场景/问答投放效果数据',
                    },
                }] : []),
                ...(isFcNativeTuWenUser() ? [{
                    key: PageType.ImageTextReport,
                    title: '图文报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal' as const,
                        navkey: PageType.ImageTextReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '图文报告',
                            '图文数据',
                            '图文',
                            '百家号图文',
                        ],
                        description: '查看原生互动图文场景的投放效果数据',
                    },
                }] : []),
                {
                    key: PageType.TargetAudienceReport,
                    title: '定向受众报告',
                    platforms: [PLATFORM_ENUM.FEED],
                    action: {
                        type: 'internal',
                        navkey: PageType.TargetAudienceReport,
                    },
                },
            ],
        },
        {
            key: 'creativeAnalysis',
            title: '创意报告',
            icon: <IconFileStarSolid className="header-group-icon" />,
            children: [
                {
                    key: PageType.CreativeTextReport,
                    title: '创意报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.CreativeTextReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '创意文案报告',
                            '创意数据',
                            '创意消费',
                            '创意点击',
                            '点击率',
                            '创意点击率',
                            '创意展现',
                            '创意标题',
                            '创意描述',
                        ],
                        description: '查看创意投放效果数据',
                    },
                },
                {
                    key: PageType.ProgramCreativeReport,
                    title: '程序化创意报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.ProgramCreativeReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '程序化创意报告',
                            '创意报告',
                            '创意数据',
                            '创意消费',
                            '创意点击',
                            '创意点击率',
                        ],
                        description: '查看程序化创意投放效果数据',
                    },
                },
                {
                    key: PageType.AdvanceCreativeReport,
                    title: '高级样式报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.AdvanceCreativeReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '高级样式数据',
                            '样式报告',
                            '创意样式报告',
                        ],
                        description: '查看高级样式投放效果数据',
                    },
                },
                {
                    key: PageType.CreativeComponentReport,
                    title: '组件报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.CreativeComponentReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '组件报告',
                            '创意组件数据',
                            '组件报告',
                            '组件消费',
                            '组件点击',
                            '组件点击率',
                        ],
                        description: '查看创意组件投放效果数据',
                    },
                },
                {
                    key: PageType.CreativeVideoReport,
                    title: '视频报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.CreativeVideoReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '视频报告',
                            '视频数据',
                            '视频消费',
                            '视频点击',
                            '视频点击率',
                            '视频展现',
                        ],
                        description: '查看创意中视频投放效果数据',
                    },
                },
                {
                    key: PageType.VideoInsightReport,
                    title: '视频洞察报告',
                    platforms: [PLATFORM_ENUM.FEED],
                    action: {
                        type: 'internal',
                        navkey: PageType.VideoInsightReport,
                    },
                },
                {
                    key: PageType.CreativeImageReport,
                    title: '图片报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.CreativeImageReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '图片报告',
                            '图片数据',
                            '图片消费',
                            '图片点击',
                            '图片点击率',
                            '图片展现',
                        ],
                        description: '查看创意中图片投放效果数据',
                    },
                },
                {
                    key: PageType.CreativeTitleReport,
                    title: '标题报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.CreativeTitleReport,
                    },
                },
                {
                    key: PageType.CreativeDescReport,
                    title: '描述报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.CreativeDescReport,
                    },
                },
            ],
        },
        {
            key: 'resultAnalysis',
            title: '效果报告',
            icon: <IconLandingPageSolid className="header-group-icon" />,
            children: [
                {
                    key: PageType.LandPageReport,
                    title: '落地页报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.LandPageReport,
                    },
                    flashLinkConfig: {
                        order: 16,
                        description: '查看落地页展示、点击、消费等数据',
                    },
                },
                {
                    key: PageType.InvalidClickReport,
                    title: '无效点击报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.InvalidClickReport,
                    },
                    flashLinkConfig: {
                        order: 18,
                        description: '查看投放过程中产生的无效点击数据',
                    },
                },
                {
                    key: PageType.InvalidClueReport,
                    title: '无效线索报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.InvalidClueReport,
                    },
                    flashLinkConfig: {
                        description: '查看无效线索量/成本等数据',
                    },
                },
                {
                    key: PageType.LiftBudgetReport,
                    title: '起量报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.LiftBudgetReport,
                    },
                    flashLinkConfig: {
                        description: '查看项目/方案使用一键起量功能，起量期间的展现/点击/消费/转化等效果数据',
                    },
                },
                {
                    key: PageType.OcpxCampaignReport,
                    title: '计划转化出价报告',
                    platforms: [PLATFORM_ENUM.FEED],
                    action: {
                        type: 'internal',
                        navkey: PageType.OcpxCampaignReport,
                    },
                },
                {
                    key: PageType.AftereffectMeasureReport,
                    title: '转化效果度量报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.AftereffectMeasureReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '后效数据',
                            '人群画像数据',
                        ],
                        description: '查看转化人群投放效果和特征数据',
                    },
                },
                {
                    key: PageType.VisitDetailReport,
                    title: '访客明细',
                    action: {
                        type: 'internal',
                        navkey: PageType.VisitDetailReport,
                    },
                    flashLinkConfig: {
                        description: '查看访客的访问详情数据',
                        keywords: ['访客明细报告'],
                    },
                },
            ],
        },
        {
            key: 'promotionSpecial',
            title: '投放专项',
            icon: <IconAdDeliveryCircleSolid className="header-group-icon" />,
            children: [
                {
                    key: PageType.InsightReport,
                    title: '数据简报',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.InsightReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '人群画像数据',
                            '分析路径',
                            'TOP搜索词',
                            '触发模式分类占比',
                        ],
                        description: '快捷查看整体重点投放效果数据',
                    },
                },
                {
                    key: PageType.RealtimeReport,
                    title: '实时报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.RealtimeReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '实时数据',
                            '实时消费',
                            '实时点击',
                            '实时展现',
                            '实时点击率',
                        ],
                        order: 17,
                        description: '实时查看方案/关键词展示、点击、消费等数据',
                    },
                },
                ...(isFcLiveUser() ? [{
                    key: PageType.LiveRoomReport,
                    title: '直播间报告',
                    action: {
                        type: 'internal' as const,
                        navkey: PageType.LiveRoomReport,
                    },
                }] : []),
                {
                    key: PageType.LeagueSpecialReport,
                    title: '百青藤报告',
                    platforms: [PLATFORM_ENUM.FEED],
                    action: {
                        type: 'internal',
                        navkey: PageType.LeagueSpecialReport,
                    },
                },
                {
                    key: PageType.CrowdBidReport,
                    title: '分人群出价报告',
                    platforms: [PLATFORM_ENUM.FEED],
                    action: {
                        type: 'internal',
                        navkey: PageType.CrowdBidReport,
                    },
                },
                {
                    key: PageType.CplCampaignReport,
                    title: '服务直达报告',
                    platforms: [PLATFORM_ENUM.FC],
                    visible: () => isFcCplUser(),
                    action: {
                        type: 'internal',
                        navkey: PageType.CplCampaignReport,
                    },
                    flashLinkConfig: {
                        description: '查看服务直达方案线索量/成本等业务数据',
                    },
                },
                {
                    key: PageType.ProgramReport,
                    title: '小程序报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.ProgramReport,
                    },
                    flashLinkConfig: {
                        description: '查看投放小程序对应展现/点击/消费/转化等效果数据',
                    },
                },
                {
                    key: PageType.StoreReport,
                    title: '本地店铺报告',
                    platforms: [PLATFORM_ENUM.FC],
                    action: {
                        type: 'internal',
                        navkey: PageType.StoreReport,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '店铺数据',
                            '门店数据',
                            '门店报告',
                        ],
                        description: '查看投放本地店铺对应展现/点击/消费/转化等效果数据',
                    },
                },
                {
                    key: PageType.ShopReport,
                    title: '门店报告',
                    platforms: [PLATFORM_ENUM.FEED],
                    action: {
                        type: 'internal',
                        navkey: PageType.ShopReport,
                    },
                },
            ],
        },
        {
            key: 'structuredProduct',
            title: '产品报告',
            icon: <IconReportCheckSolid className="header-group-icon" />,
            children: [
                ...(isLawIndustryUser() || isFeedBusinessPointReportUser() ? [{
                    key: PageType.BusinessPoint,
                    title: '业务类目报告',
                    // 这个报告需要根据不同平台检查不同的权限，所以保留 visible 函数
                    visible: () => {
                        return (aixProduct === PLATFORM_ENUM.FEED && isFeedBusinessPointReportUser())
                        || (aixProduct === PLATFORM_ENUM.FC && isLawIndustryUser());
                    },
                    action: {
                        type: 'internal' as const,
                        navkey: PageType.BusinessPoint,
                    },
                    flashLinkConfig: {
                        keywords: [
                            '业务类目数据',
                            '关联产品',
                            '关联产品数据',
                        ],
                        description: '业务类目投放效果数据',
                    },
                }] : []),
                {
                    key: PageType.GoodsReport,
                    title: '商品报告',
                    action: {
                        type: 'internal',
                        navkey: PageType.GoodsReport,
                    },
                    flashLinkConfig: {
                        description: '查看投放商品对应展现/点击/消费/转化等效果数据',
                    },
                },
            ],
        },
        ...(isLiteProjectUser() ? [
            {
                key: 'minmal',
                title: '极简投报告',
                icon: <IconReportSolid className="header-group-icon" />,
                platforms: [PLATFORM_ENUM.FC],
                children: [
                    {
                        key: PageType.MinmalProjectReport,
                        title: '项目报告',
                        action: {
                            type: 'internal' as const,
                            navkey: PageType.MinmalProjectReport,
                        },
                    },
                    {
                        key: PageType.MinmalCreativeImageReport,
                        title: '图片报告',
                        action: {
                            type: 'internal' as const,
                            navkey: PageType.MinmalCreativeImageReport,
                        },
                    },
                    {
                        key: PageType.MinmalCreativeTitleReport,
                        title: '标题报告',
                        action: {
                            type: 'internal' as const,
                            navkey: PageType.MinmalCreativeTitleReport,
                        },
                    },
                    {
                        key: PageType.MinmalCreativeDescReport,
                        title: '描述报告',
                        action: {
                            type: 'internal' as const,
                            navkey: PageType.MinmalCreativeDescReport,
                        },
                    },
                    {
                        key: PageType.MinmalQueryWordReport,
                        title: '搜索词报告',
                        action: {
                            type: 'internal' as const,
                            navkey: PageType.MinmalQueryWordReport,
                        },
                    },
                    {
                        key: PageType.MinmalInvalidClickReport,
                        title: '无效点击报告',
                        action: {
                            type: 'internal' as const,
                            navkey: PageType.MinmalInvalidClickReport,
                        },
                    },
                    {
                        key: PageType.MinmalInvalidClueReport,
                        title: '无效线索报告',
                        action: {
                            type: 'internal' as const,
                            navkey: PageType.MinmalInvalidClueReport,
                        },
                    },
                    {
                        key: PageType.MinmalRealtimeReport,
                        title: '实时报告',
                        action: {
                            type: 'internal' as const,
                            navkey: PageType.MinmalRealtimeReport,
                        },
                    },
                ],
            },
        ] : []) as MenuItemConfig[],
    ];
}


// GUI Token 配置
export const PageType2GuiTokenConfig = {
    [PageType.AccountReport]: '我要查看账户报告',
    [PageType.ProjectReport]: '我要查看项目报告',
    [PageType.CampaignReport]: '我要查看方案报告',
    [PageType.QueryWordReport]: '我要查看搜索词报告',
    [PageType.MarketingPointReport]: '我要查看营销要点报告',
    [PageType.AcgGoodsReport]: '我要查看爱采购商品报告',
    [PageType.SplitRegionReport]: '我要查看分地域报告',
    [PageType.CreativeVideoReport]: '我要查看视频报告',
    [PageType.CreativeImageReport]: '我要查看图片报告',
    [PageType.CreativeTitleReport]: '我要查看标题报告',
    [PageType.CreativeDescReport]: '我要查看描述报告',
    [PageType.CreativeTextReport]: '我要查看创意文案报告',
    [PageType.InvalidClickReport]: '我要查看无效点击报告',
    [PageType.InvalidClueReport]: '我要查看无效线索报告',
    [PageType.RealtimeReport]: '我要查看实时报告',
    [PageType.LiftBudgetReport]: '我要查看起量报告',
};
