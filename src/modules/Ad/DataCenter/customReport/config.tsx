import {PageType} from '@/dicts/pageType';

// 单账户模板配置
export const feedTemplateConfig = {
    [PageType.AdgroupReport]: {
        name: '单元报告',
        templateId: 370,
        // 多账户模板配置
        multiAccountTemplateId: 379,
        reportType: 2330653,
    },
    [PageType.GoodsReport]: {
        name: '商品报告',
        templateId: 365,
        multiAccountTemplateId: 378,
        reportType: 2094829,
    },
    [PageType.BusinessPoint]: {
        name: '业务类目报告',
        templateId: 391,
        multiAccountTemplateId: 390,
        reportType: 2094831,
    },
    [PageType.ProgramCreativeReport]: {
        name: '程序化创意报告',
        templateId: 125,
        multiAccountTemplateId: 129,
        reportType: 2347699,
    },
    [PageType.LandPageReport]: {
        name: '落地页报告',
        templateId: 269,
        multiAccountTemplateId: 271,
        reportType: 6098145,
    },
    [PageType.VisitDetailReport]: {
        name: '访客明细报告',
        templateId: 293,
        multiAccountTemplateId: 295,
        reportType: 6759418,
    },
    [PageType.LiftBudgetReport]: {
        name: '一键起量报告',
        templateId: 360,
        multiAccountTemplateId: 361,
        reportType: 2276045,
    },
    [PageType.TargetReport]: {
        name: '定向分析报告',
        templateId: 381,
        multiAccountTemplateId: 380,
        reportType: 2324048,
    },
    [PageType.CreativeTextReport]: {
        name: '创意报告',
        templateId: 114,
        multiAccountTemplateId: 117,
        reportType: 2094816,
    },
    [PageType.InvalidClueReport]: {
        name: '无效线索报告',
        templateId: 455,
        reportType: 970752,
    },
    [PageType.LeagueSpecialReport]: {
        name: '百青藤报告',
        templateId: 303,
        multiAccountTemplateId: 299,
        reportType: 2437984,
    },
    [PageType.OcpxCampaignReport]: {
        name: '计划转化出价报告',
        templateId: 389,
        multiAccountTemplateId: 388,
        reportType: 2276108,
    },
    [PageType.CrowdBidReport]: {
        name: '分人群出价报告',
        templateId: 279,
        reportType: 2094822,
    },
};

export const fcTemplateConfig = {
    [PageType.AdgroupReport]: {
        name: '单元报告',
        templateId: 90,
        reportType: ********,
    },
    [PageType.KeywordReport]: {
        name: '关键词报告',
        templateId: 66,
        reportType: 2602783,
    },
    [PageType.GoodsReport]: {
        name: '商品报告',
        templateId: 132,
        reportType: 248654,
    },
    [PageType.CrowdReport]: {
        name: '人群报告',
        templateId: 209,
        reportType: 9718404,
    },
    [PageType.ProgramCreativeReport]: {
        name: '程序化创意报告',
        templateId: 309,
        reportType: 2665239,
    },
    [PageType.CreativeComponentReport]: {
        name: '组件报告',
        templateId: 137,
        reportType: 6438125,
    },
    [PageType.LandPageReport]: {
        name: '落地页报告',
        templateId: 162,
        reportType: 5381679,
    },
    [PageType.VisitDetailReport]: {
        name: '访客明细报告',
        templateId: 347,
        reportType: 4017932,
    },
    [PageType.LiftBudgetReport]: {
        name: '一键起量报告',
        templateId: 173,
        reportType: 2276042,
    },
    [PageType.MarketingPointReport]: {
        name: '营销要点报告',
        templateId: 366,
        reportType: 1900011,
    },
    [PageType.InvalidClueReport]: {
        name: '无效线索报告',
        templateId: 321,
        reportType: 970701,
    },
    [PageType.BusinessPoint]: {
        name: '业务类目报告',
        templateId: -1,
        reportType: 2094830,
    },
    [PageType.CreativeTextReport]: {
        name: '创意报告',
        templateId: 96,
        reportType: 2665235,
    },
    [PageType.NoteReport]: {
        name: '笔记报告',
        templateId: 459,
        reportType: 99824122,
    },
    [PageType.QuestionKnowledgeReport]: {
        name: '问答报告',
        templateId: 452,
        reportType: 99823122,
    },
    [PageType.ImageTextReport]: {
        name: '图文报告',
        templateId: 457,
        reportType: 99825122,
    },
    [PageType.MinmalProjectReport]: {
        name: '项目报告',
        templateId: 435,
        reportType: 2300002,
    },
    [PageType.MinmalCreativeDescReport]: {
        name: '描述报告',
        templateId: 441,
        reportType: 2300005,
    },
    [PageType.MinmalCreativeImageReport]: {
        name: '图片报告',
        templateId: 437,
        reportType: 2300003,
    },
    [PageType.MinmalCreativeTitleReport]: {
        name: '标题报告',
        templateId: 439,
        reportType: 2300004,
    },
    [PageType.AdvanceCreativeReport]: {
        name: '高级样式报告',
        templateId: -1,
        reportType: 1150477,
    },
    [PageType.MinmalQueryWordReport]: {
        name: '搜索词报告',
        templateId: 443,
        reportType: 2300006,
    },
    [PageType.MinmalInvalidClueReport]: {
        name: '无效线索报告',
        templateId: 449,
        reportType: 2300009,
    },
    [PageType.CplCampaignReport]: {
        name: '服务直达报告',
        templateId: 195,
        reportType: 170025,
    },
};
