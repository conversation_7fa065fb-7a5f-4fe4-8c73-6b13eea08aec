import {useState, useEffect} from 'react';
import {Radio} from '@baidu/one-ui';
import {useBoolean} from 'huse';
import {getUserId} from '@/utils';
import {formatString} from '@/utils/string';
import {appendQuery} from '@/utils/route';
import {getUrlWithQinggeQuery} from '@/utils/link';
import {PRODUCT} from '@/dicts/campaign';
import {SingletonIframeInDataCenter} from '../fcCommon/SingletonIframeInDataCenter';
import B2BQueryReport from './B2B';

const PRODUCT_DATA_SOURCE = [
    {label: '搜索', value: PRODUCT.FC},
    {label: '爱采购加油推', value: PRODUCT.B2B_PROMOTION},
];

const stage = process.env.STAGE || 'ONLINE';
const customReportConfig = [
    {
        key: PRODUCT.FC,
        name: '凤巢',
        url: ({
            ONLINE: 'https://fengchao.baidu.com/fc/datacenter/qingge/queryWordReport/user/${userId}',
            PREONLINE: 'https://fengchao.baidu-int.com/fc/datacenter/qingge/queryWordReport/user/${userId}',
            TEST: 'http://fctest.baidu.com:${port}/fc/datacenter/qingge/queryWordReport/user/${userId}',
        })[stage],
    },
];

function QueryWordReport() {
    const [tabKey, onChangeTabKey] = useState(PRODUCT.FC);
    const url = formatString(
        customReportConfig.find(i => i.key === tabKey)?.url || '',
        {userId: getUserId(), port: location.port}
    );
    const groupProps = {
        value: tabKey,
        onChange: (e: any) => {
            onChangeTabKey(e.target.value);
        },
        className: 'aix-report-radio-group',
        theme: 'light-ai',
    } as const;
    const urlWirhQuery = appendQuery(getUrlWithQinggeQuery(url), {in: 'iframe', 'host_app': 'qingge'});

    const [isHideReportHeader, {on, off}] = useBoolean(false);

    useEffect(() => {
        function eventHandle(event: any) {
            if (typeof event.data === 'object' && event.data.type === 'hideReportHeader') {
                on();
            }
            else if (typeof event.data === 'object' && event.data.type === 'showReportHeader') {
                off();
            }
        }
        window.addEventListener('message', eventHandle);
        return () => window.removeEventListener('message', eventHandle);
    }, []);

    return (
        <div className="aix-report-container">
            <div className="report-header" style={isHideReportHeader ? {display: 'none'} : {}}>
                <div className="report-title">搜索词报告</div>
                <Radio.Group {...groupProps}>
                    {PRODUCT_DATA_SOURCE.map(item => (
                        <Radio.Button value={item.value} key={item.value}>
                            {item.label}
                        </Radio.Button>
                    ))}
                </Radio.Group>
            </div>
            {
                tabKey === PRODUCT.FC && (
                    <SingletonIframeInDataCenter
                        urlWirhQuery={urlWirhQuery}
                    />
                )
            }
            {
                tabKey === PRODUCT.B2B_PROMOTION && <B2BQueryReport />
            }
        </div>
    );
}

export default QueryWordReport;
