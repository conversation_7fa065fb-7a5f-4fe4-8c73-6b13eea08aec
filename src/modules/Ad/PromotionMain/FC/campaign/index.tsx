import {useEffect} from 'react';
import {PostMessageEventType, PostMessageScene} from 'commonLibs/utils/postMessage';
import {isUndefined} from 'lodash-es';
import {getScecSmallFlowUser} from 'commonLibs/utils/getTrusteeshipUser';
import {isEshopOnlyUser} from 'commonLibs/utils/getFlag';
import {PageType} from '@/dicts/pageType';
import {useAdRoute} from '@/modules/Ad/routes';
import Header from '@/components/Header';
import {PLATFORM_ENUM} from '@/dicts';
import {PRODUCT} from '@/dicts/campaign';
import {Instructions} from '@/utils/instructions';
import {useURLQuery} from '@/hooks/tableList/query';
import {FC_MARKET_TARGET} from '@/dicts/marketingTarget';
import {getUserId} from '@/utils';
import SingletonIframe from '@/components/common/iframe/SingletonIframe';
import {destroyIframe} from '@/utils/iframeManager';
import {sendMainLog} from '../util';

function getFcCampaignIframePath(
    {marketingTargetId, projectId, bizId, bizName}: {
        marketingTargetId?: FC_MARKET_TARGET; projectId?: string; bizId?: string;
        bizName?: string;
    }
) {
    const userId = getUserId();
    const wsCampaignPath = `/fc/managecenter/new/ws_campaign/create/user/${userId}`;

    if (getScecSmallFlowUser() || isEshopOnlyUser()) {
        return `${wsCampaignPath}?marketingTargetId=${FC_MARKET_TARGET.SHOP}`;
    }

    if (projectId && !isUndefined(marketingTargetId)) {
        return `/fc/managecenter/new/campaign/create/user/${userId}/mt/${marketingTargetId}?projectId=${projectId}`;
    }
    else if (bizId) {
        // eslint-disable-next-line max-len
        return `/fc/managecenter/new/campaign/create/user/${userId}/biz/${bizId}?marketingTargetId=${FC_MARKET_TARGET.CPQL}&businessPointName=${bizName}`;
    }
    else if (marketingTargetId) {
        return `/fc/managecenter/new/campaign/create/user/${userId}/mt/${marketingTargetId}`;
    }
    return `/fc/managecenter/new/campaign/create/user/${userId}?marketingTargetId=${FC_MARKET_TARGET.CPQL}`;
}

const campaignPageQuery = {
    productLine: JSON.stringify([PLATFORM_ENUM.FC, PRODUCT.FC]),
};

function NewCampaign({instructions}: {instructions: Instructions}) {
    const {linkTo} = useAdRoute();
    const [{projectId, marketingTargetId, bizId, bizName}] = useURLQuery<{
        projectId?: string; marketingTargetId?: FC_MARKET_TARGET; projectName?: string; bizId?: string;
        bizName?: string;
    }>();

    const iframePath = getFcCampaignIframePath({marketingTargetId, projectId, bizId, bizName});

    useEffect(() => {
        const receiveMessage = (event: {
            data: {
                scene: typeof PostMessageScene;
                eventType: typeof PostMessageEventType;
                data?: {behavior: string};
            };
        }) => {
            const {scene, eventType, data} = event.data;
            if ([
                PostMessageScene.ADD_CAMPAIGN, PostMessageScene.ADD_ADGROUP, PostMessageScene.ADD_CREATIVE,
            ].includes(scene)
                && data?.behavior === PostMessageEventType.ON_GO_BACK) {
                linkTo(PageType.CampaignList, {inheritQuery: true, query: campaignPageQuery});
            }
            sendMainLog({eventType, scene});
        };
        window.addEventListener('message', receiveMessage);
        return () => {
            window.removeEventListener('message', receiveMessage);
            destroyIframe();
        };
    }, [linkTo]);

    const handleClose = () => {
        linkTo(PageType.CampaignList, {query: campaignPageQuery});
    };

    return (
        <>
            <Header
                instructions={instructions}
                onClose={handleClose}
            />
            <SingletonIframe
                iframePath={iframePath}
                style={{
                    height: 'calc(100vh - 68px)',
                }}
            />
        </>
    );
}

export default NewCampaign;
