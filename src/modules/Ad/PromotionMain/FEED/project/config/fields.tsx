/* eslint-disable complexity */
import {isNil} from 'lodash-es';
import {FormFieldConfig, UINumberInput, UIRadioGroup} from '@baidu/react-formulator';
import Tip from 'commonLibs/Tips';
import {ftyperangeFilterOptions} from '@/modules/Ad/ManageCenter/common/ftyperange/config';
import {InputWidth} from '@/components/Project/FeedEditor/config';
import {isAiAgentOpen} from '@/components/Project/FeedEditor/util';
import {
    AppSubType, AppSubTypeText, BidModeEnum, FeedProjectFormData, feedProjectModeType,
    FeedSubjectType, SaleType,
} from '@/interface/aixProject/feedProject';
import {
    isAppLiveroom, isLXTUser, isFeedBqtFastProject,
} from '@/utils/getFlag';
import {CommonDivGroup} from '@/components/common/formItems/CommonDivGroup';
import {getAiMaxFields} from '@/components/ChatCards/FeedAiMax/config';
import {
    checkIsMdeicalMustBindCatalog,
    checkIsShowStructuredProduct,
    isSupportStructuredProductUser,
} from '@/config/feed/product';
import {ProductTypeValues} from '@/config/feed/adgroup';
import RequestTip from '@/components/common/Tip/RequestTip';
import {FeedSaleTypeNameMap} from '@/config/project/feed';
import {
    BJHType,
    BJHTypeOptions,
    checkIsBjhShortPlaySimplifiedScene,
    checkIsBjhSimplifiedScene,
} from '@/components/Project/FeedEditor/BJHType/config';
import {checkFeedSubjectType} from '@/config/feed';
import BjhTypeRadio from '@/components/Project/FeedEditor/formItems/bjhTypeRadio';
import FtyperangeRadio from '@/components/Project/FeedEditor/formItems/ftyperangeRadio';
import SaleTypeRadio from '@/components/Project/FeedEditor/formItems/saleTypeRadio';
import {checkSubjectType} from '@/config/feed/campaign';
import AppTypeForLive from '@/components/Project/FeedEditor/formItems/appTypeForLive';
import {
    getFeedBjhTongtouUser, isAppLinkLive, isBJHShortPlayAIMaxUser, isFeedAppUpdateUser,
    isFeedBjhVideoUser,
    isFeedSaleLeadLive, isIAAShortPlay, isNewAppAppoint,
} from '@/utils/getFeedFlag';
import {PROJECT_PROGRAM_TYPE_OPTIONS} from '@/config/feed/miniProgram';
import SubjectSelect, {getSubjectIds} from '../subject';
import AppSubTypeSelect, {AppSubTypeUpdateCom} from '../appSubType';
import AppSelect from '../appSelect';
import FeedProjectGuiTransAssets from '../transAssets';
import ProductSelect from '../product';
import {isBjhRoi, getPayCountShowStatus, isMustBindProduct} from '../utils';
import TransTypeAttribute from '../TransTypeAttribute';
import ProjectModeType from '../projectModeType';
import TransTypePreview from '../transTypePreview';

export const Field$$Subject: FormFieldConfig<FeedProjectFormData> = {
    field: 'subject',
    label: '营销目标',
    use: [SubjectSelect],
    componentProps: ({projectFeedId}) => {
        return {
            disabled: !!projectFeedId,
            options: getSubjectIds({isNew: !projectFeedId})
        };
    },
};

export const Field$$Ftyperange: FormFieldConfig<FeedProjectFormData> = {
    field: 'ftyperange',
    label: '流量范围',
    showRequiredMark: true,
    visible: formData => checkFeedSubjectType(formData.subject).isAppSubject
        && !location.href.includes('&in=iframe&') && isFeedBqtFastProject(),
    use: [FtyperangeRadio, {
        options: ftyperangeFilterOptions,
        style: {
            width: InputWidth,
        },
        distribution: 'even',
    }],
    componentProps: formData => {
        return {
            disabled: !!formData.projectFeedId,
        };
    },
};

export const Feild$$AppSubTypeUpdate: FormFieldConfig<FeedProjectFormData> = {
    field: 'appSubType',
    label: null,
    use: [AppSubTypeUpdateCom, {
        options: [
            {
                value: AppSubType.download,
                label: AppSubTypeText[AppSubType.download],
            },
            {
                value: AppSubType.appInvoke,
                label: AppSubTypeText[AppSubType.appInvoke],
            },
            ...(isNewAppAppoint() ? [{
                value: AppSubType.appAppoint,
                label: AppSubTypeText[AppSubType.appAppoint],
            }] : []),
        ],
    }],
    componentProps: ({projectFeedId, subject}) => {
        return {
            disabled: !!projectFeedId,
            subject,
        };
    },
    visible: formData => {
        return isFeedAppUpdateUser() && (
            checkSubjectType(formData.subject).isAppSubject
            || [FeedSubjectType.appLink, FeedSubjectType.hmos].includes(formData.subject)
        );
    },
};

export const Field$$BjhType: FormFieldConfig<FeedProjectFormData> = {
    field: 'bjhType',
    label: '营销场景',
    use: [BjhTypeRadio, {
        disabled: true,
        options: BJHTypeOptions,
        style: {
            width: InputWidth,
        },
        distribution: 'even',
    }],
    componentProps: formData => {
        return {
            disabled: !!formData.projectFeedId,
            isBjhSimplifiedScene: checkIsBjhSimplifiedScene(formData),
            isBjhTongtouScene: getFeedBjhTongtouUser(),
        };
    },
    visible: formData => checkFeedSubjectType(formData.subject).isBjhSubject,
};

export const Field$$SaleType: FormFieldConfig<FeedProjectFormData> = {
    field: 'saleType',
    label: '营销场景',
    use: [SaleTypeRadio, {
        type: 'strong',
        style: {'--one-checkbox-strong-min-width': '194px'},
    }],
    visible: ({subject}) => {
        const {isSalesLeadSubject, isAppSubject, isAppLinkSubject} = checkSubjectType(subject);
        return isSalesLeadSubject && isFeedSaleLeadLive()
        || (isAppSubject || isAppLinkSubject) && isAppLiveroom();
    },
    componentProps: ({projectFeedId, subject, saleType, appSubType}) => {
        const supportAppLinkLive = subject === FeedSubjectType.appLink && isAppLinkLive();
        return {
            options: [
                {
                    label: FeedSaleTypeNameMap[SaleType.default],
                    value: SaleType.default,
                },
                {
                    label: FeedSaleTypeNameMap[SaleType.live],
                    value: SaleType.live,
                    disabled: [FeedSubjectType.hmos].includes(subject)
                    || [AppSubType.appAppoint].includes(appSubType) && !supportAppLinkLive,
                },
            ],
            disabled: !!projectFeedId,
            subject,
            saleType,
        };
    }
};

export const isShowAppSubType = (formData: FeedProjectFormData) => {
    return !isFeedAppUpdateUser() && (
        checkSubjectType(formData.subject).isAppSubject
        || [FeedSubjectType.appLink, FeedSubjectType.hmos].includes(formData.subject)
    );
};

export const Field$$appSubType: FormFieldConfig<FeedProjectFormData> = {
    field: 'appSubType',
    label: '投放场景',
    use: [AppSubTypeSelect, {
        options: [
            {
                value: AppSubType.download,
                label: AppSubTypeText[AppSubType.download],
                key: AppSubType.download
            },
            {
                value: AppSubType.appInvoke,
                label: AppSubTypeText[AppSubType.appInvoke],
                key: AppSubType.appInvoke
            },
            {
                value: AppSubType.appAppoint,
                label: AppSubTypeText[AppSubType.appAppoint],
                key: AppSubType.appAppoint,
            },
        ]
    }],
    componentProps: ({projectFeedId, subject}) => {
        return {
            disabled: !!projectFeedId,
            subject,
        };
    },
    visible: isShowAppSubType,
};

export const isShowMiniProgramType = (formData: FeedProjectFormData) => {
    return checkSubjectType(formData.subject).isProgramSubject;
};

export const Field$$MiniProgramType: FormFieldConfig<FeedProjectFormData> = {
    field: 'miniProgramType',
    label: <span>小程序类型<RequestTip helpKey="miniProgramType" /></span>,
    use: [UIRadioGroup, {
        type: 'strong',
        style: {'--one-checkbox-strong-min-width': '194px'},
    }],
    visible: isShowMiniProgramType,
    componentProps: ({projectFeedId}) => {
        return {
            disabled: !!projectFeedId,
            options: PROJECT_PROGRAM_TYPE_OPTIONS,
        };
    },
    validators: value => {
        if (!value) {
            return '请选择小程序类型';
        }
    },
};

export const isShowAppSelect = (formData: FeedProjectFormData) => {
    return [AppSubType.download, AppSubType.appAppoint].includes(formData.appSubType)
    && [FeedSubjectType.ios, FeedSubjectType.android, FeedSubjectType.hmos].includes(formData.subject);
};

export const Field$$AppSelect: FormFieldConfig<FeedProjectFormData> = {
    field: 'appInfo',
    label: '应用包',
    use: [AppSelect],
    visible: isShowAppSelect,
    componentProps: ({projectFeedId, subject, appSubType}) => {
        const isAppAppoint = appSubType === AppSubType.appAppoint;
        return {
            disabled: !!projectFeedId,
            disabledSelectAppPlatform: isAppAppoint,
            disabledSelectAppPlatformTip: isAppAppoint ? 'IOS、HarmonyOS升级中，敬请期待' : '',
            subject,
            appSubType,
            isEdit: !!projectFeedId,
        };
    },
    validators: value => {
        if (!value?.appName) {
            return '请选择应用';
        }
    },
};

export const isShowAppSelectForLive = (formData: FeedProjectFormData) => {
    const {isLiveSubject} = checkSubjectType(formData.subject);
    return isLiveSubject;
};

export const Field$$AppSelectForLive: FormFieldConfig<FeedProjectFormData> = {
    field: 'appInfoForLive',
    label: '选择应用',
    showRequiredMark: true,
    use: [AppTypeForLive, {
        style: {
            '--one-checkbox-strong-min-width': '194px',
        },
    }],
    helpBottomText: '请先到直播B端（直播伴侣PC端、百家号网页版&app、百度app）查看绑定对应的ios/andriod安装包类型，再选择此处应用类型，以保证配置正确。如果两侧配置不一致，以信息流平台配置为准。',
    visible: isShowAppSelectForLive,
    componentProps: ({projectFeedId, subject, appSubType, saleType}) => {
        return {
            disabled: !!projectFeedId,
            subject,
            appSubType,
            saleType,
        };
    },
};

export const isShowUseProduct = ({subject, appSubType, bstype, miniProgramType} = {} as FeedProjectFormData) => {
    return isSupportStructuredProductUser(subject)
    && checkIsShowStructuredProduct({subject, appSubType, bstype, miniProgramType}, true);
};

export const Field$$UseProduct: FormFieldConfig<FeedProjectFormData> = {
    field: 'useProduct',
    label: '关联产品',
    use: [UIRadioGroup, {
        type: 'strong',
        style: {'--one-checkbox-strong-min-width': '194px'},
    }],
    componentProps: (formData: FeedProjectFormData) => {
        return {
            options: [
                // 目前仅有ocpc和roi，所以就用roi控制该字段的展示，后续如果添加其他出价模式，记得修改
                {
                    label: '开启',
                    value: true
                },
                // 目标ROI目前仅有百家号观剧才有，有疑问联系wuxiangyu
                {
                    label: '不开启',
                    value: false,
                }
            ],
            disabled: (!!formData.projectFeedId && !formData.recommendCategory)
            || (
                [FeedSubjectType.salesLead].includes(formData.subject) && checkIsMdeicalMustBindCatalog()
            ),
        };
    },
    visible: isShowUseProduct,
    showRequiredMark: ({subject, transType} = {} as FeedProjectFormData) => {
        return isMustBindProduct({subject, transType});
    },
    validators: (v, formData) => {
        if (isMustBindProduct(formData) && !v) {
            return '当优化目标为付费观剧时，请关联短剧库及产品进行投放';
        }
    },
};

export const Field$$Product: FormFieldConfig<FeedProjectFormData> = {
    field: 'product',
    label: '',
    use: [ProductSelect],
    visible: ({useProduct}) => {
        return useProduct;
    },
    validators: (value = {}, formData) => {
        // 当前校验逻辑仅为法律产品，其他产品暂不通过此处校验
        const {productType, productId} = value;
        if ((isLXTUser() && productType === ProductTypeValues.law
        || productType === ProductTypeValues.franchise) && !productId) {
            return '请选择产品';
        }
        if (isMustBindProduct(formData) && !productId) {
            return '当优化目标为付费观剧时，请关联短剧库及产品进行投放';
        }
    },
    componentProps: (formData: FeedProjectFormData) => {
        return {
            formData,
        };
    },
};

export const Field$$BidMode: FormFieldConfig<FeedProjectFormData> = {
    field: 'bidMode',
    label: '竞价策略',
    use: [UIRadioGroup, {
        type: 'strong',
        style: {'--one-checkbox-strong-min-width': '194px'},
    }],
    componentProps: (formData: FeedProjectFormData) => {
        const isBjhTongtouScene = getFeedBjhTongtouUser();
        const isHideRoiOption = [
            FeedSubjectType.android, FeedSubjectType.ios,
            FeedSubjectType.androidLive, FeedSubjectType.iosLive,
            FeedSubjectType.hmos, FeedSubjectType.appLink,
            FeedSubjectType.salesLead,
            FeedSubjectType.program,
        ].includes(formData.subject) || isBjhTongtouScene;
        return {
            options: [
                // 目前仅有ocpc和roi，所以就用roi控制该字段的展示，后续如果添加其他出价模式，记得修改
                {
                    label: '目标转化成本',
                    value: BidModeEnum.TRANS_COST
                },
                // 目标ROI目前仅有百家号观剧才有，有疑问联系wuxiangyu
                ...(
                    isHideRoiOption
                        ? []
                        : [
                            {
                                label: '目标ROI',
                                value: BidModeEnum.ROI,
                                disabled: !([FeedSubjectType.bjh].includes(formData.subject)
                                && formData.bjhType === BJHType.shortPlay),
                            },
                        ]
                ),
            ],
            disabled: !!formData.projectFeedId,
        };
    },
    visible: ({subject, bjhType, simpleType, projectFeedId}: FeedProjectFormData) => {
        if (checkIsBjhShortPlaySimplifiedScene({subject, bjhType, simpleType, projectFeedId})) {
            return false;
        }
        return isIAAShortPlay() && [FeedSubjectType.bjh].includes(subject)
        || [
            FeedSubjectType.salesLead,
            FeedSubjectType.android,
            FeedSubjectType.ios,
            FeedSubjectType.hmos,
            FeedSubjectType.appLink,
            FeedSubjectType.program,
        ].includes(subject);
    },
};

export const Field$$PorjectGuiAppTransId: FormFieldConfig<FeedProjectFormData> = {
    field: 'appTransId',
    label: '转化资产/转化追踪',
    rules: [['required', '请选择转化资产/追踪']],
    use: [
        FeedProjectGuiTransAssets,
        {
            placeholder: '请选择转化资产/追踪',
            width: InputWidth,
        },
    ],
    componentProps: formData => {
        return {
            projectFeedId: formData.projectFeedId,
            subject: formData.subject,
            saleType: formData.saleType,
            appSubType: formData.appSubType,
            appInfo: formData.appInfo,
            eshopType: formData.eshopType,
            miniProgramType: formData.miniProgramType,
            bjhType: formData.bjhType,
            productType: formData.product?.productType,
        };
    },
    visible: formData => !isBjhRoi(formData) && !checkIsBjhShortPlaySimplifiedScene(formData),
};


export const Field$$ProjectModeType: FormFieldConfig<FeedProjectFormData> = {
    field: 'projectModeType',
    label: '投放模式',
    use: [ProjectModeType, {width: InputWidth}],
    validators: (v, formData) => {
        if (v === feedProjectModeType.AIMAX) {
            if (!isAiAgentOpen(formData)) {
                return '您还未配置AIMax任务清单，请至少开启一项AIMax任务';
            }
        }
        return '';
    },
    componentProps: ({subject}) => {
        return {subject};
    },
};

export const Group$$AiMax = {
    group: 'FeedAiMax',
    use: [CommonDivGroup, {
        className: 'feed-aimax-group',
        title: (
            <>
                {
                    isBJHShortPlayAIMaxUser() ? null : (
                        <span className="feed-aimax-group-title">AIMax设置</span>
                    )
                }
            </>
        )
    }],
    fields: getAiMaxFields({
        disabled: false,
        changeAiMaxTradeVersionTypeNeedValidate: false,
    }),
    visible: ({projectModeType, subject, bjhType} = {} as FeedProjectFormData) => {
        // 百家号短剧场景下，AIMax设置只展示在短剧场景下,实际值还是透传
        return projectModeType === feedProjectModeType.AIMAX && (
            ![FeedSubjectType.bjh].includes(subject)
            || (bjhType === BJHType.shortPlay || isFeedBjhVideoUser() && bjhType === BJHType.video)
        );
    },
};

export const Field$$TransTypePreview: FormFieldConfig<FeedProjectFormData> = {
    field: 'transTypePreview',
    label: '优化目标',
    use: [TransTypePreview, {
        width: InputWidth,
    }],
    rules: [['required', '请选择优化目标']],
    componentProps: ({projectFeedId, transType, subject, bjhType, simpleType}) => {
        return {
            transType,
            disabled: !!projectFeedId,
            isUseCardType: checkIsBjhShortPlaySimplifiedScene({subject, bjhType, simpleType, projectFeedId}),
            isBjhTongtouScene: getFeedBjhTongtouUser(),
        };
    },
    visible: (formData: FeedProjectFormData) => isBjhRoi(formData) || checkIsBjhShortPlaySimplifiedScene(formData),
};

export const Field$$TransTypeAttribute: FormFieldConfig<FeedProjectFormData> = {
    field: 'transTypeAttribute',
    label: '',
    use: [TransTypeAttribute],
    componentProps: ({projectFeedId}) => {
        return {
            disabled: !!projectFeedId,
        };
    },
    visible: (formData: FeedProjectFormData) => {
        return getPayCountShowStatus(formData);
    },
};

// 这个是浅层ct的roiRatio
export const Fedls$$RoiRatio: FormFieldConfig<FeedProjectFormData> = {
    field: 'roiRatio',
    label: (
        <>目标ROI系数<Tip keyName="ltvRoiRatio" /></>
    ),
    showRequiredMark: true,
    use: [UINumberInput, {
        width: InputWidth,
        placeholder: '请输入ROI系数',
        fixed: 2,
    }],
    validators: (value, formData) => {
        if (!formData.roiType) {
            return;
        }
        const [min, max] = [0.1, 10];
        if (isNil(value) || value < min || value > max) {
            return `ROI系数范围为${min}～${max}`;
        }
    },
    visible: formData => isBjhRoi(formData) && !checkIsBjhShortPlaySimplifiedScene(formData),
};

export const Fields$$RoiRatioForBjhShortPlaySimplifiedScene = {
    ...Fedls$$RoiRatio,
    label: (
        <>ROI系数<Tip keyName="ltvRoiRatio" /></>
    ),
    visible: formData => isBjhRoi(formData) && checkIsBjhShortPlaySimplifiedScene(formData),
};
