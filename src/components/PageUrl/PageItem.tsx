import classnames from 'classnames';
import {Checkbox} from '@baidu/one-ui';
import defaultAgentPreviewImgSrc from '@/styles/assets/bot_preview.png';
import defaultStorePreviewImgSrc from '@/styles/assets/store_preview.png';
import {LandingPageCard} from '@/api/landpagePureConfig';
import {AgentListItem} from '@/api/getAgentList';

interface Props {
    page: LandingPageCard | AgentListItem;
    isSelected: boolean;
    onClick: () => void;
    disabled?: boolean;
    isAgentPage: boolean;
}

export default function PageItem({
    page, onClick, isSelected, disabled, isAgentPage,
}: Props) {
    const {pageId, storeId, pageName, thumbnail} = page || {};

    const backgroundImage = isAgentPage ? defaultAgentPreviewImgSrc
        : thumbnail ?? defaultStorePreviewImgSrc;

    return (
        <div
            className={classnames({
                'single-page-item': true,
                'active': isSelected,
                'disabled': disabled,
            })}
            onClick={onClick}
        >
            <div
                className="single-page-item-img-box"
                style={{
                    backgroundImage: `url(${backgroundImage})`,
                }}
            >
                <Checkbox
                    value={pageId}
                    checked={isSelected}
                    className="single-page-item-checkbox"
                    disabled={disabled}
                />
            </div>
            <div className="single-page-item-info">
                <div className="single-page-item-name">
                    {pageName}
                </div>
                <div className="single-page-item-sub-info">
                    <div className="b2b-pages-id">
                        ID: {storeId ?? pageId}
                    </div>
                </div>
            </div>
        </div>
    );
}
