/**
 * @abstract 落地页预览选择
 * @date 2025-08-28
 * <AUTHOR>
*/
import {useState} from 'react';
import {useResource, CacheProvider, Boundary} from 'react-suspense-boundary';
import {Pagination, Empty, Loading, Input, Dropdown, SearchBox} from '@baidu/one-ui';
import {showTypeKeyMap} from 'commonLibs/components/jimuyuSelect/config';
import {giveMeShortcuts} from 'commonLibs/utils/handleOptions';
import {getAllPageList} from '@/api/fcThreeLevel/getPageList';
import {toOneUIPaginationProps, useTablePagination} from '@/hooks/pagination';
import {LandingPageCard} from '@/api/landpagePureConfig';
import {AgentListItem} from '@/api/getAgentList';
import PageItem from './PageItem';
import './index.global.less';

enum SearchFieldEnum {
    pageName = 'pageName',
    id = 'id',
}
const [searchFields, {getLabelByKey: getLabelByField}] = giveMeShortcuts([
    {value: SearchFieldEnum.pageName, label: '名称'},
    {value: SearchFieldEnum.id, label: 'ID'},
]);

interface Filter {
    filterType: string;
    filterValue: string;
}

function PageUrlFilter(props) {
    const {onSearch} = props;
    const [filterType, setFilterType] = useState(SearchFieldEnum.pageName);
    const [filterValue, setFilterValue] = useState('');
    return (
        <div className="page-url-filter">
            <Input.Group>
                <Dropdown.Button
                    width={100}
                    options={searchFields}
                    title={getLabelByField(filterType)}
                    onHandleMenuClick={e => setFilterType(e.key)}
                />
                <SearchBox
                    width={508}
                    value={filterValue}
                    onChange={e => setFilterValue(e.target.value)}
                    placeholder="请输入落地页名称/ID"
                    onSearch={e => {
                        setFilterValue(e.target.value);
                        onSearch({
                            filterType,
                            filterValue: e.target.value,
                        });
                    }}
                    onClearClick={() => {
                        setFilterValue('');
                        onSearch({
                            filterType,
                            filterValue: '',
                        });
                    }}
                />
            </Input.Group>
        </div>
    );
}

function PageUrlList(props) {
    const {
        marketingTargetId,
        showType,
        onChange,
        value,
        pagination,
        paginationMethods,
        filterValue,
        filterType,
        selectedPageId,
        setPageId,
    } = props;

    const [[jmyPageList = [], agentList = []]] = useResource(getAllPageList, {
        marketingTargetId,
        showType: showTypeKeyMap[showType],
        filter: {
            value: filterValue,
            type: filterType,
        },
    });

    const totalCount = jmyPageList.length + agentList.length;
    const isEmpty = totalCount === 0;

    const paginationProps = toOneUIPaginationProps(
        {...pagination, ...paginationMethods},
        totalCount,
        {showSizeChange: false, showPageJumper: false, showTotal: true},
    );

    const {pageNo, pageSize} = pagination;
    const pagedDataList = [...jmyPageList, ...agentList].slice((pageNo - 1) * pageSize, pageNo * pageSize);

    const getPageList = (list: Array<LandingPageCard | AgentListItem>) => {
        return list.map(item => {
            const isSelected = selectedPageId
                ? item.pageId === selectedPageId
                : item.onlineUrl === value;
            const onClick = () => {
                if (isSelected) { // 被选过取消选中
                    onChange('');
                    setPageId('');
                } else { // 选中
                    onChange(item.onlineUrl);
                    setPageId(item.pageId);
                }
            };
            return (
                <PageItem
                    key={item.pageId}
                    page={item}
                    isAgentPage={!item.prompt}
                    isSelected={isSelected}
                    onClick={onClick}
                />
            );
        });
    };

    return (
        <>
            <div className="page-url-list-container">
                {
                    isEmpty ? (
                        <Empty className="content-empty-tip" />
                    ) : (
                        <div className="page-url-lists">
                            {getPageList(pagedDataList)}
                        </div>
                    )
                }
            </div>
            {!isEmpty && <Pagination {...paginationProps} />}
        </>
    );
}

export default function PageUrl(props) {

    const [pagination, paginationMethods] = useTablePagination();

    const [{filterValue, filterType}, setFilterValue] = useState<Filter>({
        filterType: SearchFieldEnum.pageName,
        filterValue: '',
    });

    const [selectedPageId, setPageId] = useState('');

    const onSearch = (value: Filter) => {
        setFilterValue(value);
        paginationMethods.resetPageNo();
    };

    const pageUrlListProps = {
        ...props,
        filterValue,
        filterType,
        pagination,
        paginationMethods,
        selectedPageId,
        setPageId,
    };

    return (
        <>
            <div className="page-url-select-container">
                <PageUrlFilter
                    onSearch={onSearch}
                />
                <CacheProvider>
                    <Boundary pendingFallback={<Loading tip="加载中..." className="content-empty-tip" />}>
                        <PageUrlList {...pageUrlListProps} />
                    </Boundary>
                </CacheProvider>
            </div>
        </>
    );
}
