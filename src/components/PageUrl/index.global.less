.page-url-select-drawer {
    .with-drawer-input-drawer-content {
        height: 100%;
    }
}

.page-url-select-container {
    .page-url-filter {
        margin-bottom: 16px;
    }

    .page-url-list-container {
        height: calc(100vh - 240px);
        display: flex;
        width: 100%;

        .page-url-lists {
            overflow-y: auto;
            display: inline-flex;
            flex-wrap: wrap;
            gap: 16px;

            .single-page-item {
                height: 305px;
                display: flex;
                flex-direction: column;
                width: 138px;
                border: 1px solid #ebedf5;
                border-radius: 6px;
                overflow: hidden;
                cursor: pointer;
                transition: all 0.2s ease;

                .single-page-item-img-box {
                    position: relative;
                    user-select: none;
                    height: 245px;
                    background-size: cover;
                    background-position: center;
                }

                .single-page-item-checkbox {
                    position: absolute;
                    top: 8px;
                    left: 8px;
                    line-height: 1; // 用于取消默认高度，不然间距不对
                }

                .single-page-item-info {
                    padding: 8px 12px;
                    box-sizing: border-box;
                    line-height: 22px;
                    font-size: 12px;
                    font-weight: 400;
                }

                .single-page-item-name {
                    color: #191b1e;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .single-page-item-sub-info {
                    color: #848b99;
                }

                &.disabled {
                    .single-page-item-img-box,
                    .single-page-item-name,
                    .single-page-item-sub-info {
                        cursor: not-allowed;
                        opacity: 0.5;
                    }
                }

                &.active,
                &:hover {
                    border: 1px solid #3a5bfd;

                    &.disabled {
                        .single-page-item-sub-info {
                            cursor: auto;
                            opacity: 1;
                        }
                    }
                }

                .b2b-pages-id {
                    display: flex;
                    align-items: center;
                    padding: 4px 0;
                }
            }
        }
    }

    .content-empty-tip {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
