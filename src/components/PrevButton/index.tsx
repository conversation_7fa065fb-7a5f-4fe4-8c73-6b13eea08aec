/**
 * @file 智能搭建/翻新支持 返回上一步按钮
 * <AUTHOR>
 * @date 2025-06-11
*/

import {Button} from '@baidu/one-ui';


interface PrevButtonProps {
    allowPreviousStep?: boolean;
    previousStepField?: string;
    disabled: boolean;
    field: string;
    aiChat: {pushMessage: (...args: any) => void};
}
export default function PrevButton(props: PrevButtonProps) {
    const {
        allowPreviousStep,
        previousStepField,
        disabled,
        field,
        aiChat,
    } = props;
    const handlePreviousStep = async () => {
        await aiChat.pushMessage(
            'user',
            '返回上一步',
            {
                custom: {
                    currentField: field,
                    currentFieldValue: [{previousStepField}],
                },
                trigger: 'card',
            }
        );
    };
    if (!allowPreviousStep) {
        return null;
    }
    return (
        <Button
            variant="normal"
            disabled={disabled}
            onClick={handlePreviousStep}
            style={{marginLeft: 8}}
        >
            返回上一步
        </Button>
    );
}
