/* eslint-disable complexity */
/* eslint-disable max-lines */
import dayjs from 'dayjs';
import {isNumber, intersection} from 'lodash-es';
import {roiValidConditions, roiOfflineConditions, DEET_TYPE_OPTIMIZE_OPTION} from 'commonLibs/config/ocpc';
import {formatOcpcBidRatioResponse, formateRequestParamsForm} from 'commonLibs/components/ocpcBidRatio';
import {
    cvSourcesUnlimitedValue, cvSourcesOptions,
    COMPREHENSIVE_CLUES, EFFECTIVE_READING_CLUES,
} from 'commonLibs/config/ocpc';
import {
    FC_MARKET_TARGET,
    FC_SUB_MARKET_TARGET,
    isBjhNoteSubMarket,
    isBjhImageTextSubMarket,
} from '@/dicts/marketingTarget';
import {DeepTransTypeModeEnum, FcProject, FcProjectFormData, RotatingCycItem} from '@/interface/aixProject/fcProject';
import {
    AiMaxVersion, SmartControlEnum, SmartLiftBudgetEnum, SmartTargetingEnum,
} from '@/components/List/ProjectList/AiAgent/config';
import {
    getIndustry,
    getIndustryType,
    AIMAX_TYPE,
    LATEST_AIMAX_VERSION,
} from '@/components/List/ProjectList/AiAgent/util';
import {FcTransAssetOptionsEnum, TransManagerModeType, FcOcpcBidTypeEnum, FcBidTypeEnum} from '@/dicts/ocpc';
import {
    ProjectBudgetType,
    UseProjectBudgetEnum,
    PROJECT_BUDGET_SELECT_TYPE,
    FcProjectModeType,
    INDUSTRY_SOLUTION,
} from '@/dicts/project';
import {getDefaultProductCategoryType} from '@/components/ChatCards/Product/config';
import {PRODUCT} from '@/dicts/campaign';
import {
    isFcLiveUser,
    isIntelligentInvestmentUser,
    isAIMaxBasedScene,
    isFcNativeBJUser,
    isFcNativeTuWenUser,
} from '@/utils/getFlag';
import {liftBudgetModeRadioMap} from '@/components/ChatCards/FcAiMax/config';
import {liftBudgetModeEnum} from '@/components/ChatCards/LiftBudget';
import {LiftBudgetStatus} from '@/interface/campaign';
import {UseLiftBudgetEnum, SmartAigcEnum} from '@/interface/project';
import {OcpcBidRatioTypeEnum} from '@/dicts/ocpcBidRatio';
import {sendMonitor} from '@/utils/logger';
import {AppPlatform} from '@/dicts/appInfo';
import {appSceneTypeMap} from '@/modules/Ad/PromotionMain/FC/project/appProjectScene';
import {AutoOptimizationEnabledMt, IndustryAimaxEnabledMt} from '@/components/ChatCards/FcAiMax/commonConfig';
import {OPTIMIZE_TRANS_TARGETS, TRANS_TARGET_MAP} from './NativeTransEditor/config';

export const weekValueMap = {
    monday: '1',
    tuesday: '2',
    wednesday: '3',
    thursday: '4',
    friday: '5',
    saturday: '6',
    sunday: '7',
};

const formatLiftFormValue2Api = ({liftBudgetMode, liftBudgetWeekday, liftBudgetTime}: {
    liftBudgetMode: liftBudgetModeEnum;
    liftBudgetTime?: number;
    liftBudgetWeekday?: number[];
}) => {
    if (liftBudgetMode === liftBudgetModeEnum.immediate) {
        return {};
    }
    else if (liftBudgetMode === liftBudgetModeEnum.appoint) {
        return {
            liftBudgetSchedule: [{
                weekDay: 0,
                hour: liftBudgetTime,
            }],
        };
    }
    else if (liftBudgetMode === liftBudgetModeEnum.appointWeekly) {
        return {
            liftBudgetSchedule: liftBudgetWeekday.map(weekDay => ({
                weekDay: +weekDay,
                hour: +(dayjs.unix(liftBudgetTime).hour()),
            })),
        };
    }
};


interface formatAllLiftBudget2ApiProps {
    liftBudgetSwitch: boolean;
    useImmediateLiftBudget: boolean;
    liftBudgetModeRadio: liftBudgetModeEnum;
    smartLiftBudget: SmartLiftBudgetEnum;
    liftBudgetMode: liftBudgetModeEnum;
    liftBudget: number;
    liftBudgetWeekday: number[];
    liftBudgetTime: number;
    liftBudgetStatus: LiftBudgetStatus;
    bgtTaskId?: number;
}

// eslint-disable-next-line complexity
const formatAllLiftBudget2Api = ({
    liftBudgetSwitch, useImmediateLiftBudget, liftBudgetModeRadio,
    smartLiftBudget, liftBudgetMode, liftBudget, liftBudgetWeekday, liftBudgetTime,
    liftBudgetStatus, bgtTaskId,
}: formatAllLiftBudget2ApiProps) => {
    if (liftBudgetStatus === LiftBudgetStatus.RUNNING) {
        return {
            liftBudgetStatus,
            smartLiftBudget: liftBudgetSwitch ? smartLiftBudget : SmartLiftBudgetEnum.STOP,
        };
    }
    // 取消起量
    if (!liftBudgetSwitch && !useImmediateLiftBudget) {
        return {
            smartLiftBudget: SmartLiftBudgetEnum.STOP,
            useLiftBudget: UseLiftBudgetEnum.NO_USE,
            ...(bgtTaskId ? {bgtTaskId} : {}),
        };
    }
    return {
        liftBudgetStatus,
        ...(
            liftBudgetSwitch
                && liftBudgetModeRadio === liftBudgetModeRadioMap.autoOptimization
                && !useImmediateLiftBudget
                ? {useLiftBudget: UseLiftBudgetEnum.NO_USE}
                : {
                    useLiftBudget: +(liftBudgetSwitch || useImmediateLiftBudget),
                }
        ),
        ...(
            liftBudgetSwitch && liftBudgetModeRadio === liftBudgetModeRadioMap.autoOptimization
                ? {
                    smartLiftBudget,
                }
                : {}
        ),
        ...(
            liftBudgetSwitch && liftBudgetModeRadio === liftBudgetModeRadioMap.customRules
                ? {
                    smartLiftBudget: SmartLiftBudgetEnum.STOP,
                    liftBudgetMode,
                    liftBudget,
                    ...formatLiftFormValue2Api({liftBudgetMode, liftBudgetWeekday, liftBudgetTime}),
                }
                : {}
        ),
        ...(
            useImmediateLiftBudget
                ? {
                    liftBudget,
                    liftBudgetMode: liftBudgetModeEnum.immediate,
                }
                : {}
        ),
    };
};

const formatProjectLiftBudgetToFormValue = ({liftBudget, liftBudgetMode, liftBudgetSchedule}: {
    liftBudget: number;
    liftBudgetMode: liftBudgetModeEnum;
    liftBudgetSchedule?: Array<{hour: number, weekDay: number}>;
}) => {
    if (!liftBudget) {
        return {
            liftBudgetMode,
            liftBudgetTime: +dayjs().add(1, 'hour').startOf('hour').unix(),
            liftBudgetWeekday: [],
        };
    }
    else if (liftBudgetMode === liftBudgetModeEnum.appointWeekly) {
        const [firstDay] = liftBudgetSchedule || [];
        return {
            liftBudgetMode,
            liftBudgetTime: +dayjs().hour(firstDay?.hour).minute(0).unix(),
            liftBudgetWeekday: liftBudgetSchedule.map(item => item.weekDay),
        };
    }
    else if (liftBudgetMode === liftBudgetModeEnum.appoint) {
        const [firstDay] = liftBudgetSchedule || [];
        return {
            liftBudgetMode,
            liftBudgetTime: +dayjs.unix(firstDay?.hour).unix(),
            liftBudgetWeekday: [],
        };
    }
    return {
        liftBudgetMode,
        liftBudgetTime: +dayjs().add(1, 'hour').startOf('hour').unix(),
        liftBudgetWeekday: [],
    };
};

interface AgentRotate {
    projectRotatingCyc: RotatingCycItem[];
}

const formatTimeValue = value => {
    return value < 10 ? `0${value}` : value <= 23 ? `${value}` : '00';
};

// 将[{timeId: 101, ratio: 1}, {timeId: 102, ratio: 1}]的格式转换为[01, 02]
const formatAgentRotateToFormValue = ({
    projectRotatingCyc,
}: AgentRotate) => {
    const industryType = getIndustry();
    let defaultTime = ['', ''];
    if (industryType === AIMAX_TYPE.MEDICAL) {
        defaultTime = ['21', '08'];
    }
    // 新建时
    if (projectRotatingCyc.length === 0) {
        return {
            rotatingTimeInterval: defaultTime,
        };
    }

    const sortedTimeIds = projectRotatingCyc
        .map(item => String(item.timeId).slice(1))
        .sort((a, b) => +a - +b);

    // 判断一下是否存在次日的情况：某个时间间隔会大于1，则代表存在次日的情况
    for (let i = 0; i < sortedTimeIds.length - 1; i += 1) {
        if (+sortedTimeIds[i + 1] - +sortedTimeIds[i] > 1) {
            return {
                rotatingTimeInterval: [sortedTimeIds[i + 1], formatTimeValue(+sortedTimeIds[i] + 1)],
            };
        }
    }
    // 非次日：时间依次+1排列，直接取第一项与最后一项的时间
    const startTime = sortedTimeIds[0] || defaultTime[0];
    const endTime = sortedTimeIds[sortedTimeIds.length - 1] || defaultTime[1];

    return {
        rotatingTimeInterval: [startTime, formatTimeValue(+endTime + 1)],
    };
};

interface formatAgentRotateToAPIProps {
    rotatingTimeInterval: string[];
}
// 将[01, 02]的格式转换为[{timeId: 101, ratio: 1}, {timeId: 102, ratio: 1
const formatAgentRotateToAPI = ({
    rotatingTimeInterval,
}: formatAgentRotateToAPIProps) => {
    const weekDayNumbers = Object.values(weekValueMap);
    const [startTime, endTime] = rotatingTimeInterval;
    const timeIds = [];
    if (startTime < endTime) {
        for (let i = 0; i < endTime - startTime; i += 1) {
            const time = +startTime + i;
            timeIds.push(formatTimeValue(time));
        }
    }
    else {
        for (let i = 0; i < +endTime; i += 1) {
            timeIds.push(formatTimeValue(i));
        }
        for (let i = +startTime; i <= 23; i += 1) {
            timeIds.push(formatTimeValue(i));
        }
    }
    let projectRotatingCyc = [];
    weekDayNumbers.forEach(weekDay => {
        projectRotatingCyc = projectRotatingCyc.concat(timeIds.map(timeId => ({
            timeId: +`${weekDay}${timeId}`,
            ratio: 1,
        })));
    });
    return {
        projectRotatingCyc,
    };
};

// 目前营销场景的展示情况
// 1. CPQL 直播名单下
// 2. Native 笔记名单下
// 3. Native 图文名单下
// todo: 后续摘要场景下需加上摘要场景的判断
const getDefaultSubMarketingTargetId = ({marketingTargetId}) => {
    if (isFcLiveUser() && marketingTargetId === FC_MARKET_TARGET.CPQL) {
        return FC_SUB_MARKET_TARGET.NORMAL;
    }
    else if (isFcNativeBJUser() && marketingTargetId === FC_MARKET_TARGET.NATIVE) {
        return FC_SUB_MARKET_TARGET.NOTE;
    }
    else if (isFcNativeTuWenUser() && marketingTargetId === FC_MARKET_TARGET.NATIVE) {
        return FC_SUB_MARKET_TARGET.IMAGE_TEXT;
    }
    return FC_SUB_MARKET_TARGET.NORMAL;
};

// 原生互动笔记和图文下，对外漏的优化目标根据transtypes字段进行选中处理
export const formatTransTargetToFormValue = ({transTypes, projectId, subMarketingTargetId}) => {
    if (isBjhNoteSubMarket(subMarketingTargetId)) {
        if (projectId) {
            if (intersection(transTypes, OPTIMIZE_TRANS_TARGETS).length) {
                return transTypes[0];
            }
            return TRANS_TARGET_MAP.MORE;
        }
        return COMPREHENSIVE_CLUES;
    }
    if (isBjhImageTextSubMarket(subMarketingTargetId)) {
        return EFFECTIVE_READING_CLUES;
    }
    return '';
};


// eslint-disable-next-line complexity
export function formatApi2FcProjectData({fcProjectType}: {fcProjectType: Partial<FcProject>}) { // todo 换一下接口定义成凤巢的
    // 如果是新建，表单初始值赋予通过给projectType设置默认值来实现
    const {
        projectName = '',
        projectId,
        marketingTargetId,
        subMarketingTargetId = getDefaultSubMarketingTargetId({marketingTargetId}),
        projectModeType = 0,
        sharedBudget,
        bindIds = [],
        sharedBudgetType = ProjectBudgetType.DAY,
        useSharedBudget = UseProjectBudgetEnum.ON,
        projectSource = PRODUCT.FC,
        smartLiftBudget = SmartLiftBudgetEnum.STOP,
        aiMaxPromotion,
        // 新建默认值取决于名单，优先展示大健康。编辑默认值是false
        useIndustryAIMaxPromotion = projectId ? false : Boolean(getIndustry()),
        ocpcBid,
        ocpcDeepBid,
        suggestDeepBid,
        // 默认仅支持ocpc
        ocpcBidType = FcOcpcBidTypeEnum.OCPC,
        deepTransTypeMode = 1,
        transTypes = [],
        assistTransTypes = [],
        cvSources = [],
        transManagerMode = marketingTargetId !== FC_MARKET_TARGET.APP
            ? TransManagerModeType.unlimited
            : TransManagerModeType.cvSource,
        transAssetInfo,
        transAsset = FcTransAssetOptionsEnum.BY_ASSET_ID,
        assetType = [],
        useLiftBudget = 0,
        liftBudgetMode,
        liftBudgetSchedule = [],
        liftBudget,
        liftBudgetStatus,
        structuredContentIds,
        structuredProductIds = [],
        structuredContentIdStrs,
        structuredProductIdStrs = [],
        catalogId,
        productCategoryType = getDefaultProductCategoryType(),
        aiMaxTargetingTaskTime,
        aiMaxAigcTaskTime,
        autoOrientStatus,
        autoCreativeStatus,
        bgtTaskId,
        transId,
        targetRoiRatio,
        deepRoiStatus = false,
        deepTransTypeStatus,
        ocpcPayTimesStatus = false,
        transTypeMultiBidStatus = false,
        transTypesOcpcBidRatio = {},
        multiTransTypeStatus = false,
        projectAgentUrl = '',
        projectRotatingCyc = [],
        expandPlanCyc = false,
        adgroupRotatingShieldIds = [],
        planRotatingShieldIds = [],
        smartControl = SmartControlEnum.STOP,
        industrySolution = INDUSTRY_SOLUTION.NORMAL,
        projectApplicationSid,
        projectApplicationType,
        smartBidCostControl = 0,
        smartTargetingCostControl = 0,
        smartInvalidClueControl = 0,
        smartTargeting = SmartTargetingEnum.CLOSED,
        smartAigc = SmartAigcEnum.CLOSE,
        smartBaseAdjustSwitch = 0,
        smartBaseControlCostSwitch = 0,
        smartLiftBid = 0,
    } = fcProjectType;
    const {
        isUseOcpcBidRatio,
        ocpcBidRatioType,
        ageRangeConfig = [],
        crowdCoefConfigs = {},
        cycOcpcBidRatio = {},
        regionConfig = {},
    } = formatOcpcBidRatioResponse(fcProjectType);
    const {
        liftBudgetTime,
        liftBudgetWeekday,
    } = formatProjectLiftBudgetToFormValue({liftBudget, liftBudgetMode, liftBudgetSchedule});

    const {rotatingTimeInterval} = formatAgentRotateToFormValue({
        projectRotatingCyc,
    });
    const transTarget = formatTransTargetToFormValue({transTypes, projectId, subMarketingTargetId});

    const liftBudgetSwitch = smartLiftBudget !== SmartLiftBudgetEnum.STOP
    || (liftBudgetMode === liftBudgetModeEnum.appoint
        || liftBudgetMode === liftBudgetModeEnum.appointWeekly);

    const formatData = {
        // 基础信息
        projectId,
        projectName: projectName,
        projectSource,
        marketingTargetId: marketingTargetId ? marketingTargetId : FC_MARKET_TARGET.CPQL,
        subMarketingTargetId,
        bindIds,
        projectModeType,

        // 转化信息
        transManagerMode,
        ocpcBidType,
        bidType: (ocpcBidType === FcOcpcBidTypeEnum.CPC || ocpcBidType === FcOcpcBidTypeEnum.ECPC)
            ? FcBidTypeEnum.CPC : FcBidTypeEnum.OCPC,
        ocpcBid,
        deepTransTypeMode,
        ocpcDeepBid: ocpcDeepBid === 0 ? undefined : ocpcDeepBid,
        suggestDeepBid,
        transTypes: transTarget && transTarget !== TRANS_TARGET_MAP.MORE ? [transTarget] : transTypes,
        deepTransTypes: assistTransTypes,
        deepTransTypeStatus,
        ocpcPayTimesStatus,
        transAsset: {
            option: transAsset,
            assetType,
            transAssetId: transAssetInfo?.transAssetId ? [transAssetInfo.assetType, transAssetInfo?.transAssetId] : [],
        },
        cvSources: cvSources.filter(item => item !== cvSourcesUnlimitedValue),
        sharedBudget,
        budgetType: sharedBudgetType,
        budgetSelectType: sharedBudget
            ? PROJECT_BUDGET_SELECT_TYPE.DAY
            : PROJECT_BUDGET_SELECT_TYPE.NOLIMIT,
        useProjectBudget: !!useSharedBudget,
        // AI投手版本
        useIndustryAIMaxPromotion,
        stockSmartLift: aiMaxPromotion?.stockSmartLift,
        aiMaxMarketingLevel: aiMaxPromotion?.level,
        // aiamx行业版版本，新建时默认为最新版
        aiMaxIndustryVersion: aiMaxPromotion?.version || LATEST_AIMAX_VERSION[getIndustry()],
        // 新建时若在aimax行业内则优先展示行业版
        aiMaxModeSwitch: useIndustryAIMaxPromotion && IndustryAimaxEnabledMt.includes(marketingTargetId)
            ? AiMaxVersion.INDUSTRY
            : AiMaxVersion.NORMAL,
        // 起量
        smartLiftBudget,
        liftBudget,
        liftBudgetSwitch, // aiMax起量开关
        liftBudgetModeRadio: (smartLiftBudget === SmartLiftBudgetEnum.STOP
            && liftBudgetStatus !== LiftBudgetStatus.RUNNING
            && !!liftBudgetMode)
            || (!AutoOptimizationEnabledMt.includes(marketingTargetId as FC_MARKET_TARGET))
            ? liftBudgetModeRadioMap.customRules : liftBudgetModeRadioMap.autoOptimization, // aiMax起量模式
        // 预约起量模式 生效时间
        liftBudgetMode,
        liftBudgetTime,
        liftBudgetStatus,
        liftBudgetWeekday,
        useImmediateLiftBudget: +(
            liftBudgetMode === liftBudgetModeEnum.immediate
            && liftBudgetStatus !== LiftBudgetStatus.FINISHED
        ), // 立即起量开关
        // 精细化出价
        ocpcBidRatioType,
        ageRangeConfig,
        crowdCoefConfigs,
        cycOcpcBidRatio,
        regionConfig,
        isUseOcpcBidRatio,
        ocpcBidRatioMode: 2, // 写死 只支持自定义规则
        structuredContentIds,
        structuredProductIds,
        structuredContentIdStrs,
        structuredProductIdStrs,
        catalogId,
        productCategoryType,
        autoOrientStatus: isAIMaxBasedScene() ? !!smartTargeting : autoOrientStatus,
        autoCreativeStatus: isAIMaxBasedScene() ? !!smartAigc : autoCreativeStatus,
        aiMaxTargetingTaskTime,
        aiMaxAigcTaskTime,
        bgtTaskId,
        transTypeMultiBidStatus,
        transId: (
            ocpcBidType === FcOcpcBidTypeEnum.ROI
                ? (
                    transManagerMode === TransManagerModeType.asset
                        ? transAssetInfo?.transAssetId
                        : transId
                )
                : undefined
        ),
        targetRoiRatio,
        deepRoiStatus,
        deepTargetRoiRatio: deepRoiStatus && deepTransTypeMode === DeepTransTypeModeEnum.ROI
            ? targetRoiRatio : undefined, // deepRoiStatus 为 true 表示之前设置了「优化转化价值-ROI系数」，为 false 说明没设置不赋值
        transTypesOcpcBidRatio,
        multiTransTypeStatus,
        projectAgentUrl,
        isUseAgentRotate: projectRotatingCyc.length > 0,
        expandPlanCyc: projectId && projectRotatingCyc.length > 0 ? expandPlanCyc : true,
        projectRotatingCyc,
        rotatingTimeInterval,
        rotatingShieldIds: {
            adgroupRotatingShieldIds,
            planRotatingShieldIds,
        },
        smartControl,
        smartControlSwitch: smartControl !== SmartControlEnum.STOP,
        industrySolution,
        appScene: {
            appSceneType: projectApplicationType,
            appSceneSid: projectApplicationSid,
            platform: AppPlatform.ANDROID,
        },
        autoProductContent: undefined,
        transTarget,
        ...(isAIMaxBasedScene() ? {
            aiMaxDivGroupShow: 0,
            smartTargeting,
            smartBidCostControl: !!smartBidCostControl,
            smartTargetingCostControl: !!smartTargetingCostControl,
            smartInvalidClueControl: !!smartInvalidClueControl,
            smartBaseAdjustSwitch: !!smartBaseAdjustSwitch,
            smartBaseControlCostSwitch: !!smartBaseControlCostSwitch,
            smartLiftBid,
            smartLiftSwitch: smartLiftBid || liftBudgetSwitch,
            aiMaxTargetingTaskTimeReload: false,
            aiMaxAigcTaskTimeReload: false,
        } : {}),
    };
    return {
        initialData: formatData,
        ...formatData,
    };
}

// eslint-disable-next-line complexity
export function formatFcProjectData2Api({formData}: {formData: FcProjectFormData}) {
    const {
        projectId,
        projectName,
        marketingTargetId,
        subMarketingTargetId,
        bindIds,
        projectModeType,
        sharedBudget,
        budgetType,
        transManagerMode,
        ocpcBidType,
        ocpcBid,
        ocpcDeepBid,
        transTypes,
        deepTransTypes,
        deepTransTypeMode,
        cvSources,
        transAsset,
        useProjectBudget,
        liftBudgetMode,
        liftBudgetWeekday,
        liftBudgetTime,
        liftBudget,
        ocpcBidRatioType,
        crowdCoefConfigs,
        regionConfig,
        ageRangeConfig,
        cycOcpcBidRatio,
        aiMaxModeSwitch,
        aiMaxMarketingLevel,
        aiMaxIndustryVersion,
        smartLiftBudget,
        liftBudgetSwitch,
        liftBudgetModeRadio,
        useImmediateLiftBudget,
        isUseOcpcBidRatio,
        liftBudgetStatus,
        bgtTaskId,
        initialData,
        autoOrientStatus,
        autoCreativeStatus,
        transId,
        targetRoiRatio,
        deepTargetRoiRatio,
        deepTransTypeStatus,
        ocpcPayTimesStatus,
        transTypeMultiBidStatus,
        transTypesOcpcBidRatio,
        multiTransTypeStatus,
        projectAgentUrl,
        isUseAgentRotate,
        rotatingTimeInterval,
        rotatingShieldIds,
        expandPlanCyc,
        smartControlSwitch,
        smartControl,
        appScene,
        structuredProductIds,
        structuredProductIdStrs,
        catalogId,
        productCategoryType,
        autoProductContent,
        smartBidCostControl,
        smartTargetingCostControl,
        smartInvalidClueControl,
        smartBaseAdjustSwitch,
        smartBaseControlCostSwitch,
        smartLiftBid,
        smartTargeting,
        aiMaxTargetingTaskTimeReload,
        aiMaxAigcTaskTimeReload,
        aiMaxAigcTaskTime,
        aiMaxTargetingTaskTime,
    } = formData;
    const {option, assetType, transAssetId} = transAsset;
    const [curAssetType, assetId] = transAssetId;
    // 使用最后点确定的时间
    const curTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
    const isAdClick = ocpcBidType === FcOcpcBidTypeEnum.CPC || ocpcBidType === FcOcpcBidTypeEnum.ECPC;
    const isNoOcpcBid = isAdClick || ocpcBidType === FcOcpcBidTypeEnum.ROI || ocpcBidType === FcOcpcBidTypeEnum.CVMAX;

    let assistTransTypes = [];
    if (ocpcBidType === FcOcpcBidTypeEnum.ROI || ocpcBidType === FcOcpcBidTypeEnum.ECPC) {
        assistTransTypes = [];
    }
    else if (deepTransTypeMode === DEET_TYPE_OPTIMIZE_OPTION.ROI) {
        // 如果深度优化方式是优化转化价值
        assistTransTypes = transTypes;
    }
    else {
        assistTransTypes = deepTransTypes;
    }

    const {projectRotatingCyc} = formatAgentRotateToAPI({rotatingTimeInterval});
    const result = {
        ...(
            projectId
                ? {projectId}
                : {}
        ),
        projectName,
        marketingTargetId,
        ...(
            subMarketingTargetId
                ? {subMarketingTargetId}
                : {}
        ),
        bindIds,
        smartControl: smartControlSwitch ? smartControl : SmartControlEnum.STOP,
        ...(
            useProjectBudget
                ? {sharedBudget, sharedBudgetType: budgetType}
                : {}
        ),
        useSharedBudget: Number(useProjectBudget),
        unbindSharedBudgetInfo: getUnBindSharedBudgetInfo({
            preUseSharedBudget: !!projectId && initialData.useProjectBudget,
            useSharedBudget: Number(useProjectBudget),
            initialBindIds: initialData.bindIds ?? [],
            bindIds: bindIds ?? [],
            sharedBudget,
        }),
        transManagerMode,
        projectModeType,
        deepTransTypeMode,
        ocpcBidType,
        ...(
            transManagerMode === TransManagerModeType.unlimited
                ? {transAsset: FcTransAssetOptionsEnum.UNLIMITED}
                : {}
        ),
        ...(
            transManagerMode === TransManagerModeType.cvSource
                ? {cvSources}
                : {}
        ),
        ...(
            !isAdClick && transManagerMode === TransManagerModeType.asset
                ? {
                    transAsset: option,
                    assetType,
                    transAssetId: assetId,
                    transAssetInfo: {assetType: curAssetType, transAssetId: assetId},
                }
                : {}
        ),
        transTypes: ocpcBidType === FcOcpcBidTypeEnum.CPC ? [] : transTypes,
        assistTransTypes,
        deepTransTypeStatus,
        ocpcPayTimesStatus,
        ...(isNoOcpcBid ? {} : {ocpcBid}),
        ...(
            deepTransTypes.length && deepTransTypeMode === DeepTransTypeModeEnum.TRANS_TYPE
                ? {ocpcDeepBid: ocpcDeepBid || 0}
                : {}
        ),
        ...(
            deepTargetRoiRatio && deepTransTypeMode === DeepTransTypeModeEnum.ROI
                ? {
                    targetRoiRatio: deepTargetRoiRatio,
                    ...(projectId ? {deepRoiStatus: true} : {}),
                }
                : {}
        ),
        ...(
            projectId && deepTransTypeMode === DeepTransTypeModeEnum.ROI && !deepTargetRoiRatio
                ? {deepRoiStatus: false}
                : {}
        ),
        ...(
            ocpcBidType === FcOcpcBidTypeEnum.ROI && transManagerMode === TransManagerModeType.cvSource
                ? {cvSources: [cvSourcesOptions.TRANSFILTER], transId, targetRoiRatio}
                : {}
        ),
        ...(
            ocpcBidType === FcOcpcBidTypeEnum.ROI && transManagerMode === TransManagerModeType.asset
                ? {transId: assetId, targetRoiRatio, transAsset: FcTransAssetOptionsEnum.BY_ASSET_ID}
                : {}
        ),
        ...(
            isNumber(aiMaxMarketingLevel) && projectModeType === FcProjectModeType.AIMAX
                ? {
                    useIndustryAIMaxPromotion: true,
                    aiMaxPromotion: {
                        type: getIndustryType(),
                        level: aiMaxMarketingLevel,
                        version: aiMaxIndustryVersion,
                    },
                }
                : {
                    useIndustryAIMaxPromotion: false,
                }
        ),
        // 起量相关
        ...(
            formatAllLiftBudget2Api({
                liftBudgetSwitch,
                useImmediateLiftBudget,
                liftBudgetModeRadio,
                smartLiftBudget,
                liftBudgetMode,
                liftBudget,
                liftBudgetWeekday,
                liftBudgetTime,
                liftBudgetStatus,
                bgtTaskId,
            })
        ),
        ...(
            isUseOcpcBidRatio
                ? {
                    ...formateRequestParamsForm({
                        ocpcBidRatioType,
                        crowdCoefConfigs,
                        regionConfig,
                        ageRangeConfig,
                        cycOcpcBidRatio,
                    }),
                }
                : {ocpcBidRatioType: OcpcBidRatioTypeEnum.noUse}
        ),
        ...(
            projectId
                ? {}
                : {newAixProjectStatus: true}
        ),
        ...(
            !isAIMaxBasedScene() && autoOrientStatus
                ? {
                    aiMaxTargetingTaskTime: curTime,
                }
                : {}
        ),
        ...(
            marketingTargetId && marketingTargetId === FC_MARKET_TARGET.STORE
                ? {promotionScene: 1}
                : {}
        ),
        ...(
            autoCreativeStatus
                ? {
                    ...(isAIMaxBasedScene() ? {
                        smartAigc: SmartAigcEnum.OPEN,
                        ...(aiMaxAigcTaskTimeReload || !aiMaxAigcTaskTime ? {aiMaxAigcTaskTime: curTime} : {}),
                    } : {aiMaxAigcTaskTime: curTime}),
                }
                : {...(isAIMaxBasedScene() ? {smartAigc: SmartAigcEnum.CLOSE} : {})}
        ),
        ...(
            (isIntelligentInvestmentUser() && projectModeType === FcProjectModeType.AIMAX)
                ? {optimalCostStatus: true}
                : {}
        ),
        transTypeMultiBidStatus,
        transTypesOcpcBidRatio,
        multiTransTypeStatus,
        ...(projectAgentUrl ? {projectAgentUrl} : {}),
        ...(appScene && marketingTargetId === FC_MARKET_TARGET.APP
            ? {
                projectApplicationType: appScene.appSceneType,
                ...(
                    appScene.appSceneType === appSceneTypeMap.newGame
                        ? {projectApplicationSid: appScene.appSceneSid}
                        : {}
                ),
            }
            : {}
        ),
        ...(isUseAgentRotate ? {
            projectRotatingCyc,
            ...rotatingShieldIds,
            expandPlanCyc,
        } : {
            projectRotatingCyc: [],
            expandPlanCyc: false,
        }),
        ...(structuredProductIds.length > 0 ? {
            structuredProductIds,
            catalogId,
            productCategoryType,
        } : {}),
        ...(!structuredProductIds.length && autoProductContent?.fieldComplete ? {
            generateUrlProduct: 1,
        } : {}),
        ...(
            structuredProductIdStrs?.length > 0
                ? {
                    structuredProductIdStrs,
                    productCategoryType,
                }
                : {}
        ),
        ...(isAIMaxBasedScene() ? {
            smartBidCostControl: smartBidCostControl ? 1 : 0,
            smartTargetingCostControl: smartTargetingCostControl ? 1 : 0,
            smartInvalidClueControl: smartInvalidClueControl ? 1 : 0,
            smartBaseAdjustSwitch: smartBaseAdjustSwitch ? 1 : 0,
            smartBaseControlCostSwitch: smartBaseControlCostSwitch ? 1 : 0,
            smartLiftBid,
            smartTargeting,
            ...(smartTargeting === SmartTargetingEnum.STEADY
                && (aiMaxTargetingTaskTimeReload || !aiMaxTargetingTaskTime)
                ? {aiMaxTargetingTaskTime: curTime} : {}),
        } : {}),
    };
    return result;
}

// 判断是否存在某些营销目标的计划
export const getMtidFlagBySelectedCampaign = ({
    campaignList,
    selectedCampaignPlanList,
    targetMarketingId,
    allMatched = false,
}: {
    campaignList: any[];
    selectedCampaignPlanList: any[];
    targetMarketingId: number[];
    allMatched: boolean;
}) => {
    if (allMatched) {
        return selectedCampaignPlanList.every(item => {
            return targetMarketingId.includes(campaignList.find(i => i.planId === +item)?.marketingTargetId);
        });
    }

    return campaignList.some((planInfo: any = {}) => {
        const {planId, marketingTargetId} = planInfo;
        return selectedCampaignPlanList.map(
            item => +item).includes(planId) && targetMarketingId.includes(marketingTargetId);
    });
};

function getUnBindSharedBudgetInfo({
    initialBindIds, bindIds, sharedBudget,
    preUseSharedBudget, useSharedBudget,
}: {
    initialBindIds: number[];
    bindIds: number[];
    sharedBudget: number;
    preUseSharedBudget: boolean;
    useSharedBudget: UseProjectBudgetEnum;
}) {
    if (!useSharedBudget) {
        if (preUseSharedBudget) {
            return bindIds.map(id => ({
                campaignId: id,
                budget: sharedBudget,
            }));
        }
        return [];
    }
    const deleteIds = initialBindIds.filter(id => !bindIds.includes(id));
    return deleteIds.map(id => ({
        campaignId: id,
        budget: sharedBudget,
    }));
}

export const sendMonitorWithAiMax = (formData: FcProjectFormData) => {
    const {isUseOcpcBidRatio, aiMaxMarketingLevel, budgetType, liftBudgetSwitch} = formData;
    sendMonitor('project', {event: 'ocpcBidRatio', 'extra_params': !!isUseOcpcBidRatio});
    sendMonitor('project', {event: 'weekBudget', 'extra_params': budgetType === ProjectBudgetType.WEEK});
    sendMonitor('project', {event: 'liftBudget', 'extra_params': !!liftBudgetSwitch});
    sendMonitor('project', {event: 'aiMaxMarketingLevel', 'extra_params': aiMaxMarketingLevel !== undefined});
};

export function canUseAIMAX(ocpcBidType: FcOcpcBidTypeEnum) {
    return ocpcBidType === FcOcpcBidTypeEnum.OCPC;
}


export function isDeepRoiValidByCvSourceAndTransAssets(formData: FcProjectFormData, transInfo: any) {
    const {transManagerMode, transAsset, transTypes, cvSources} = formData;
    if (transManagerMode === TransManagerModeType.asset) {
        // 选择转化价值资产时，判断是否需要开启优化转化价值
        const {transAssetId: selectedTransAssets, option} = transAsset;
        if (option === FcTransAssetOptionsEnum.BY_ASSET_ID && transTypes.length === 1 && selectedTransAssets.length) {
            const [assetId, transAssetId] = selectedTransAssets;
            const {
                cvSource = [],
                traceFlag = false,
                matchFullChainTransTypes = [],
            } = transInfo.normalizedTransAssets.assetsInfo[+assetId]?.filter(
                item => item.transAssetId === +transAssetId)[0] || {};
            const transTypsArr = Object.entries(roiValidConditions).reduce((
                result, [cvSourceValue, transTypesValue]
            ) => {
                if (cvSource.includes(+cvSourceValue)) {
                    return [
                        ...result,
                        ...transTypesValue,
                    ];
                }
                return result;
            }, []);
            // 使用全链路资产的转化类型，不允许使用roi优化价值
            if (traceFlag && matchFullChainTransTypes.some(
                (item = []) => (item || []).includes(`${transTypes[0]}`))) {
                return false;
            }
            return transTypsArr.includes(transTypes[0]);
        }
    }
    else if (transManagerMode === TransManagerModeType.cvSource) {
        // 选中指定数据来源时，判断是否需要开启优化转化价值
        const isSingleSelected = transTypes.length === 1 && cvSources.length === 1;
        const invalidRoiTransTypes = roiValidConditions[cvSources[0]];
        // 当前仅支持 数据来源 + 转化类型 1+1
        if (!isSingleSelected || !invalidRoiTransTypes) {
            return false;
        }
        return invalidRoiTransTypes.includes(transTypes[0]);
    }
    return false;
}

export const getRoiValidTransTypes = () => Object.keys(roiValidConditions)
    .reduce((cur, key) => [...new Set([...cur, ...roiValidConditions[key]])], [])
    .filter(item => !roiOfflineConditions.includes(item));

const DynamicEchoValue = 4;
export function isDeepRoiValidByCvSourceTransAssetsAndTransTrack(formData: FcProjectFormData, transInfo: any) {
    const {transManagerMode, transAsset, transTypes, cvSources} = formData;
    if (transManagerMode === TransManagerModeType.asset) {
        const {transAssetId: selectedTransAssets, assetType = [], option} = transAsset;
        if (option === FcTransAssetOptionsEnum.BY_ASSET_ID && transTypes.length === 1 && selectedTransAssets.length) {
            const [assetId, transAssetId] = selectedTransAssets;
            const {
                traceTypeConfig = {},
            } = transInfo.normalizedTransAssets.assetsInfo[+assetId]?.filter(
                item => item.transAssetId === +transAssetId)[0] || {};
            const {transValType} = traceTypeConfig[transTypes[0]] || {};
            return transValType === DynamicEchoValue;
        }
    }
    else if (transManagerMode === TransManagerModeType.cvSource) {
        if (cvSources.length !== 1 || transTypes.length !== 1) {
            return false;
        }
        const invalidInfo = transInfo.deepTransModeRoiSelect.find(item => item.cvSource === cvSources[0]) || [];
        return invalidInfo.transTypes?.includes(transTypes[0]);
    }
    return false;
}

