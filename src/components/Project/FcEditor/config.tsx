/* eslint-disable complexity */
/* eslint-disable max-lines */
import {Radio, Tooltip} from '@baidu/one-ui';
import {FormConfig, UIInput, UINumberInput, FormFieldConfig, WatchConfig} from '@baidu/react-formulator';
import {IconQuestionCircleSolid} from 'dls-icons-react';
import {intersection, entries, uniq, flatten, values, isNumber, set, cloneDeep} from 'lodash-es';
import {getValidDeepTransTypes} from 'commonLibs/TransEditor/util';
import {getFormateValuesFromBackend, getDefaultPriceFactorsFromSchedule} from 'manageCenter/schedule/utils';
import {
    transType as TRANS_TYPE,
    getBizTypeName,
    CLUE_TRANS_MULTI_BID,
    EFFECTIVE_READING_CLUES,
    deepTypeStatOptionsWithReason,
} from 'commonLibs/config/ocpc';
import SelectCard from 'commonLibs/components/selectCard';
import {productCategoryTypeEnum} from 'commonLibs/config/enumEntry';
import {
    FC_MARKET_TARGET,
    fcMarketTargetNameMapByValue,
    FC_SUB_MARKET_TARGET,
    isBjhNoteSubMarket,
    isBjhImageTextSubMarket,
} from '@/dicts/marketingTarget';
import {DeepTransTypeModeEnum, FcProjectFormData} from '@/interface/aixProject/fcProject';
import {fetchProjectSuggestBizTypes} from '@/api/ocpc';
import {schedueTypeConfig} from '@/modules/Ad/PromotionMain/FC/project/schedule/config';
import {
    TransManagerModeType,
    TransSourceNameMapByValue,
    FcOcpcBidTypeEnum,
    FcTransAssetOptionsEnum,
    TransManagerModeOptionsIfROI,
    minTargetRoiRatio,
    maxTargetRoiRatio,
    minDeepTargetRoiRatio,
    maxDeepTargetRoiRatio,
    FcBidTypeEnum,
} from '@/dicts/ocpc';
import {withBorderAnimation} from '@/components/common/formItems/withBorderAnimation';
import {ProjectBudgetType, ProjectBudgetLimit, FcProjectModeType, INDUSTRY_SOLUTION} from '@/dicts/project';
import {ReadOnlyText} from '@/components/common/formItems/text';
import {EditorGroup} from '@/components/common/formGroups/editorGroup';
import {Instructions} from '@/utils/instructions';
import {getOcpcBidRatioTypeInitialValue} from '@/components/List/ProjectList/AiAgent/ocpcBidRatio/util';
import {OcpcBidRatioTypeEnum} from '@/dicts/ocpcBidRatio';
import {isFcOcpcDeepRoiUser, isAIMaxBasedScene, isAiMaxSmartControlUser, isAIMaxOvercostScene} from '@/utils/getFlag';
import {BUILD_TYPE} from '@/dicts/level';
import {liftBudgetModeRadioMap, isSupportAgent} from '@/components/ChatCards/FcAiMax/config';
import {
    AiMaxMarketingLevelEnum,
    SmartLiftBudgetEnum,
    AiMaxVersion,
    SmartControlEnum,
} from '@/components/List/ProjectList/AiAgent/config';
import {LiftBudgetStatus} from '@/interface/campaign';
import {Tip} from '@/components/common/Tip';
import HoverTip from '@/components/common/Tip/HoverTip';
import {
    isFcLiveUser,
    isDoublePriceIILevelUser,
} from '@/utils/getFlag';
import {AiMaxEnabledByMT, AiMaxOptionEnum} from '@/components/ChatCards/FcAiMax/commonConfig';
import RadioGroupWithDisabledTip from '@/components/common/formItems/RadioGroupWithDisabledTip';
import {AutoOptimizationEnabledMt, isAimaxDisabled} from '@/components/ChatCards/FcAiMax/commonConfig';
import {isLxtConnectedProduct} from '@/components/List/ProjectList/AiAgent/util';
import {ProjectBudgetSetting} from '../../List/ProjectList/editor/ProjectBudgetSetting';
import {EditorGroupFillConfig, getGroupFilledStatus} from '../common/util';
import {supportMultipleTransTypes} from './transEditor/utils';
import TransManagerMode from './TransManagerMode';
import DeepTransTypeMode, {FcDeepTransTypeModeDivGroup} from './DeepTransTypeMode';
import CvSources from './CvSources';
import TransAsset from './TransAsset';
import {TransTypeField} from './transEditor/TransTypes';
import NativeTransTypes from './NativeTransEditor/TransTypes';
import {getOptimizeTransTargetOptions, TRANS_TARGET_MAP, nativeTransTypeList} from './NativeTransEditor/config';
import ROITransNameField from './ROITransNameField';
import DeepTransTypes from './transEditor/DeepTransTypes';
import PreserveRate from './transEditor/PreserveRate';
import AiMax from './AiMax';
import BindCampaigns from './bindCampaigns';
import ProjectAgentUrl from './AgentUrl';
import ProjectModeType from './projectModeType';
import {OcpcBidTypeField} from './OcpcBidType';
import {OcpcBid} from './OcpcBid';
import {OcpcDeepBid} from './OcpcDeepBid';
import SubMarketTarget from './SubMarketTarget';
import NativeSubMarketTarget from './NativeSubMarketTarget';
import BidType from './BidType';
import DeepTargetRoiRatio from './DeepTargetRoiRatio';
import SharedBudget from './SharedBudget';
import {
    getTransTypeList,
    getShowComprehensiveClue,
    getShowComprehensiveClueDeep,
    getIsPreserveRataType,
} from './transEditor/utils';
import {isDeepRoiValidByCvSourceTransAssetsAndTransTrack, formatTransTargetToFormValue} from './utils';

const InputWidth = '388px';

const ImmediateLiftBudgetRadio = ({
    value, onChange, liftBudgetMode, liftBudgetSwitch, liftBudgetStatus, liftBudgetModeRadio,
}) => {
    let disabledTip = '';
    if (liftBudgetStatus === LiftBudgetStatus.RUNNING) {
        disabledTip = '当前项目正在起量中';
    }
    else if (!!liftBudgetMode && liftBudgetSwitch && liftBudgetModeRadio === liftBudgetModeRadioMap.customRules) {
        disabledTip = '您已在AI MAX中规划了起量任务，暂时无法启用一键起量能力';
    }
    const immediateLiftBudgetOptions = [
        {value: 0, label: '不启用', disabled: liftBudgetStatus === LiftBudgetStatus.RUNNING},
        {
            value: 1,
            label: '启用',
            disabled: !!disabledTip,
            disabledTip,
        },
    ];

    return (
        <Radio.Group value={value} onChange={e => onChange(+e.target.value)}>
            {immediateLiftBudgetOptions.map(({value, label, disabled, disabledTip}) => (
                <Tooltip key={value} title={disabled ? disabledTip : null}>
                    <Radio.Button key={value} value={value} disabled={disabled}>
                        {label}
                    </Radio.Button>
                </Tooltip>
            ))}
        </Radio.Group>
    );
};

const BudgetTypeRadio = ({value, onChange}) => {
    const options = [
        {
            value: ProjectBudgetType.DAY,
            label: '日预算',
        },
        {
            value: ProjectBudgetType.WEEK,
            label: '七日预算',
        },
    ];
    return (
        <div className="budget-type-radio">
            <Radio.Group value={value} onChange={e => onChange(+e.target.value)}>
                {options.map(({value, label}) => (
                    <Tooltip key={value}>
                        <Radio.Button key={value} value={value}>
                            {label}
                        </Radio.Button>
                    </Tooltip>
                ))}
            </Radio.Group>
        </div>
    );
};


export enum FcGroupField {
    product = 'product',
    aiMax = 'aiMax',
    basicGroup = 'basicGroup',
    bidGroup = 'bidGroup',
}


/**
 * 这里列出
 */
const FC_GROUP_FILL_CONFIG: EditorGroupFillConfig<FcGroupField, FcProjectFormData> = {
    [FcGroupField.aiMax]: [
        // 这里不用加， 因为推出的卡片 里 带了 aiMax aimax 的值不影响推出，在表单上也没有进度条
    ],
    [FcGroupField.product]: [

    ],
    [FcGroupField.basicGroup]: [
        'projectName', 'marketingTargetId',
    ],
    [FcGroupField.bidGroup]: [
        {
            field: 'cvSources',
            getIsNecessary: ({transManagerMode}) => transManagerMode === TransManagerModeType.cvSource,
            getIsFilled: ({cvSources}) => cvSources.length > 0,
        },
        {
            field: 'transAsset',
            getIsNecessary: ({transManagerMode}) => transManagerMode === TransManagerModeType.asset,
            getIsFilled: ({transAsset}) => {
                const {assetType, transAssetId} = transAsset;
                return assetType.length > 0 || !!transAssetId;
            },
        },
        {
            field: 'transTypes',
            getIsFilled: ({transTypes}) => transTypes.length > 0,
        },
        {
            field: 'ocpcBid',
            getIsNecessary: ({ocpcBidType}) => (
                ocpcBidType === FcOcpcBidTypeEnum.OCPC
                || ocpcBidType === FcOcpcBidTypeEnum.OPTIMAL_COST
            ),
            getIsFilled: ({ocpcBid}) => !!ocpcBid,
        },
        {
            field: 'ocpcDeepBid',
            getIsNecessary: ({deepTransTypes}) => deepTransTypes?.length > 0,
            getIsFilled: ({ocpcDeepBid}) => ocpcDeepBid > 0,
        },
        {
            field: 'sharedBudget',
            getIsNecessary: ({useProjectBudget}) => !!useProjectBudget,
            getIsFilled: ({sharedBudget}) => sharedBudget > 0,
        },
        {
            field: 'liftBudget',
            getIsNecessary: ({useImmediateLiftBudget}) => !!useImmediateLiftBudget,
            getIsFilled: ({liftBudget}) => liftBudget > 0,
        },
    ],
};

export function getIsAlFcProjectFieldsFilled(formData: FcProjectFormData) {
    return Object.keys(FC_GROUP_FILL_CONFIG).every(groupField => {
        return getGroupFilledStatus(FC_GROUP_FILL_CONFIG, groupField as FcGroupField, formData).isComplete;
    });
}

export const groupProps = {
    groupConfig: FC_GROUP_FILL_CONFIG,
};

export function createProjectNameField<T extends FcProjectFormData>(
    config?: Partial<FormFieldConfig<T>>,
    fieldConfig?: {width?: string}
) {
    return {
        field: 'projectName',
        label: '项目名称',
        rules: [['textRange', [1, 100]]],
        use: [UIInput, {
            placeholder: '请输入项目名称',
            width: fieldConfig?.width || InputWidth,
            maxLen: 100,
        }],
        ...config,
    } as FormFieldConfig<T>;
}
export const Field$$ProjectName = createProjectNameField();

export const Field$$SubMarketingTargetId: FormFieldConfig<FcProjectFormData> = {
    field: 'subMarketingTargetId',
    label: '营销场景',
    use: [withBorderAnimation('subMarketingTargetId', SubMarketTarget), {
        width: InputWidth,
    }],
    componentProps: ({projectId}) => {
        return {isEdit: !!projectId};
    },
    visible: formData => isFcLiveUser() && formData.marketingTargetId === FC_MARKET_TARGET.CPQL,
};

export const Field$$NativeSubMarketingTargetId: FormFieldConfig<FcProjectFormData> = {
    field: 'subMarketingTargetId',
    label: '营销场景',
    use: [withBorderAnimation('subMarketingTargetId', NativeSubMarketTarget), {
        width: InputWidth,
    }],
    componentProps: ({projectId}) => {
        return {isEdit: !!projectId};
    },
    visible: formData => (isBjhNoteSubMarket(formData.subMarketingTargetId)
    || isBjhImageTextSubMarket(formData.subMarketingTargetId)),
};

function projectModeTypeValidators({
    autoOrientStatus, autoCreativeStatus, aiMaxMarketingLevel,
    isUseOcpcBidRatio, liftBudgetSwitch, smartControlSwitch, isUseAgentRotate,
}: FcProjectFormData) {
    return !isNumber(aiMaxMarketingLevel) && !autoOrientStatus && !autoCreativeStatus
    && !isUseOcpcBidRatio
    && !liftBudgetSwitch
    && !smartControlSwitch
    && !isUseAgentRotate;
}


export const Field$$ProjectModeType: FormFieldConfig<FcProjectFormData> = {
    field: 'projectModeType',
    label: '投放模式',
    use: [ProjectModeType, {width: InputWidth}],
    validators: (v, formData) => {
        const {
            autoOrientStatus, autoCreativeStatus, aiMaxMarketingLevel,
            budgetType, isUseOcpcBidRatio, liftBudgetSwitch, smartControlSwitch, isUseAgentRotate,
            smartBidCostControl, smartTargetingCostControl, smartInvalidClueControl,
            smartBaseAdjustSwitch, smartBaseControlCostSwitch, smartLiftBid,
        } = formData;
        if (v === FcProjectModeType.AIMAX) {
            const isValidator = projectModeTypeValidators({
                autoOrientStatus, autoCreativeStatus, aiMaxMarketingLevel,
                isUseOcpcBidRatio, liftBudgetSwitch, smartControlSwitch, isUseAgentRotate,
            } as FcProjectFormData);

            if (isAIMaxBasedScene() && isValidator
                && !smartBidCostControl
                && !smartTargetingCostControl
                && !smartInvalidClueControl
                && !smartBaseAdjustSwitch
                && !smartBaseControlCostSwitch
                && !smartLiftBid
            ) {
                return '您还未配置AIMax任务清单，请至少开启一项AIMax任务';
            }
            else if (!isAIMaxBasedScene() && isValidator && budgetType === ProjectBudgetType.DAY) {
                return '您还未配置AIMax任务清单，请至少开启一项AIMax任务';
            }
        }
        return '';
    },
    componentProps: ({
        ocpcBidType,
        marketingTargetId,
        transTypeMultiBidStatus,
        productCategoryType,
        structuredProductIds,
    }) => {
        const isLxtAimaxDisabled = isLxtConnectedProduct({productCategoryType, structuredProductIds});
        return {
            aixDisabled: isAimaxDisabled({marketingTargetId, ocpcBidType, transTypeMultiBidStatus}),
            // 针对旅校通：当关联产品后，行业版aimaxAI MAX必须开启，不支持切换到自定义
            customDisabled: isLxtAimaxDisabled,
            aixDisabledText: transTypeMultiBidStatus ? '分目标精细化出价项目不支持AIMax能力' : '此竞价策略下暂不支持AIMax能力',
            customDisabledText: isLxtAimaxDisabled ? '该项目已关联产品，不支持关闭旅游AI Max专属版，放量控本效果更佳，可切换档位' : '',
        };
    },
};

export const Field$$BindIds: FormFieldConfig<FcProjectFormData> = {
    field: 'bindIds',
    label: '包含方案',
    use: [withBorderAnimation('bindIds', BindCampaigns), {width: InputWidth}],
};

export function createBidTypeField<T extends FcProjectFormData>(config?: Partial<FormFieldConfig<T>>) {
    return {
        field: 'bidType',
        label: '出价方式',
        use: [BidType],
        componentProps: ({projectId, initialData, bindIds}) => {
            return {
                projectId,
                initialData,
                bindIds,
            };
        },
        visible: ({industrySolution}) => industrySolution !== INDUSTRY_SOLUTION.KXT,
        ...config,
    } as FormFieldConfig<T>;
}
export const Field$$BidType = createBidTypeField();

export function createOcpcBidTypeField<T extends FcProjectFormData>(config?: Partial<FormFieldConfig<T>>) {
    return {
        field: 'ocpcBidType',
        label: '竞价策略',
        use: [OcpcBidTypeField],
        helpBottomText: '切换竞价策略可能会影响您当前的AIMax任务设置，如需切换请关注AIMax任务清单变化',
        componentProps: ({
            bidType,
            transTypeMultiBidStatus,
            projectId,
            ocpcPayTimesStatus,
            subMarketingTargetId,
            initialData,
        }) => {
            return {
                bidType,
                // 多出价项目、开启了优化付费次数，编辑时，不能修改竞价策略
                disabled: (transTypeMultiBidStatus || ocpcPayTimesStatus) && !!projectId,
                projectId,
                subMarketingTargetId,
                initialData,
            };
        },
        visible: ({industrySolution}) => industrySolution !== INDUSTRY_SOLUTION.KXT,
        ...config,
    } as FormFieldConfig<T>;
}
export const Field$$OcpcBidType = createOcpcBidTypeField();

export const Field$$TransManagerMode: FormFieldConfig<FcProjectFormData> = {
    field: 'transManagerMode',
    label: '转化管理方式',
    use: [TransManagerMode],
    componentProps: ({
        marketingTargetId,
        subMarketingTargetId,
        projectId,
        bindIds,
        ocpcBidType,
    }) => {
        return {
            marketingTargetId,
            subMarketingTargetId,
            projectId,
            bindIds,
            ...(
                ocpcBidType === FcOcpcBidTypeEnum.ROI
                    ? {options: TransManagerModeOptionsIfROI}
                    : {}
            ),
        };
    },
    visible: formData => {
        return formData.ocpcBidType !== FcOcpcBidTypeEnum.CPC
            && formData.industrySolution !== INDUSTRY_SOLUTION.KXT
            && !isBjhNoteSubMarket(formData.subMarketingTargetId)
            && !isBjhImageTextSubMarket(formData.subMarketingTargetId);
    },
};

export const Field$$CvSources: FormFieldConfig<FcProjectFormData> = {
    field: 'cvSources',
    label: '数据来源',
    use: [CvSources],
    visible: formData => {
        return formData.transManagerMode === TransManagerModeType.cvSource
            && formData.ocpcBidType !== FcOcpcBidTypeEnum.ROI
            && formData.ocpcBidType !== FcOcpcBidTypeEnum.CPC;
    },
    validators: [
        {
            validator: async (v, {
                marketingTargetId,
                projectId,
                multiTransTypeStatus,
                transManagerMode,
                bindIds,
                ocpcBidType,
            }) => {
                if (!supportMultipleTransTypes({
                    marketingTargetId,
                    isEdit: !!projectId,
                    multiTransTypeStatus,
                    ocpcBidType,
                    transManagerMode,
                })) {
                    return '';
                }
                const {suggestBizTypes} = await fetchProjectSuggestBizTypes(bindIds);
                const suggestCvSources = uniq(
                    flatten(values(suggestBizTypes).map(v => v.cvSources)));
                if (suggestCvSources.some((item: number) => !v.includes(item))) {
                    return `数据来源必须包含${suggestCvSources.map(
                        i => TransSourceNameMapByValue[i]).join('、')}`;
                }
                return '';
            },
            triggers: ['onChange', '@final'],
        },
    ],
    componentProps: ({marketingTargetId}) => {
        return {
            marketingTargetId,
            level: BUILD_TYPE.PROJECT,
        };
    },
};

export const Field$$TransAsset: FormFieldConfig<FcProjectFormData> = {
    field: 'transAsset',
    label: '转化资产',
    use: [TransAsset],
    visible: formData => {
        return formData.ocpcBidType !== FcOcpcBidTypeEnum.CPC
            && formData.transManagerMode === TransManagerModeType.asset;
    },
    componentProps: ({
        marketingTargetId,
        subMarketingTargetId,
        bindIds,
        ocpcBidType,
        transAsset,
    }) => {
        return {
            // 当只修改 transAsset.transAssetId 组件不更新， 这里监听一下
            // ⬇️ 这几个是用于触发重新更新
            transAssetId: transAsset?.transAssetId,
            assetType: transAsset?.assetType,
            option: transAsset?.option,
            // ⬆️ 这几个是用于触发重新更新

            marketingTargetId,
            subMarketingTargetId,
            bindIds,
            supportAssetTypes: ocpcBidType !== FcOcpcBidTypeEnum.ROI,
        };
    },
};

export const Field$$TransId: FormFieldConfig<FcProjectFormData> = {
    // ! 目标ROI模式下，使用转化追踪时使用
    field: 'transId',
    label: '转化名称',
    use: [ROITransNameField],
    visible: formData => {
        return formData.ocpcBidType === FcOcpcBidTypeEnum.ROI
            && formData.transManagerMode === TransManagerModeType.cvSource;
    },
    validators: v => {
        if (!v) {
            return '请选择转化名称';
        }
        return '';
    },
};

export function createTransTypesField<T extends FcProjectFormData>(config?: Partial<FormFieldConfig<T>>) {
    return {
        field: 'transTypes',
        label: '优化目标',
        use: [withBorderAnimation('transTypes', TransTypeField)],
        componentProps: ({
            projectId,
            marketingTargetId,
            subMarketingTargetId,
            transAsset,
            transManagerMode,
            cvSources,
            ocpcBidType,
            transId,
            bindIds,
            multiTransTypeStatus,
            transTypeMultiBidStatus,
            projectModeType,
            initialData,
            industrySolution,
            ocpcPayTimesStatus,
        }) => {
            return {
                isEdit: !!projectId,
                projectId,
                marketingTargetId,
                subMarketingTargetId,
                transManagerMode,
                transAsset,
                cvSources,
                bindIds,
                ocpcBidType,
                transId,
                multiTransTypeStatus,
                transTypeMultiBidStatus,
                projectModeType,
                initialData,
                isFcForm: true,
                disabled: ocpcPayTimesStatus || industrySolution === INDUSTRY_SOLUTION.KXT,
            };
        },
        validators: [
            (v, {projectId, initialData}) => {
                if (projectId && initialData.transTypes
                    && initialData.bidType === FcBidTypeEnum.OCPC
                    && intersection(v, initialData.transTypes).length === 0) {
                    return `已设置的目标转化仅允许做局部调整，如果您需要投放完全不同的转化目标，请重新设置。已设置的目标转化为：${
                        initialData.transTypes.map(type => TRANS_TYPE[type]).join('、')}。`;
                }
                if (!v.length) {
                    return '请选择优化目标';
                }
                return '';
            },
            {
                validator: async (newValue, {
                    marketingTargetId,
                    projectId,
                    multiTransTypeStatus,
                    transManagerMode,
                    bindIds,
                    ocpcBidType,
                }) => {
                    if (!supportMultipleTransTypes({
                        marketingTargetId,
                        isEdit: !!projectId,
                        multiTransTypeStatus,
                        ocpcBidType,
                        transManagerMode,
                    })) {
                        return '';
                    }
                    const {suggestBizTypes} = await fetchProjectSuggestBizTypes(bindIds);
                    const suggestBizTypeNames: string[] = [];
                    if (intersection(CLUE_TRANS_MULTI_BID, newValue).length > 0) {
                        entries(suggestBizTypes).forEach(
                            ([bizType, {transTypes = []}]) => {
                                if (newValue.length && !transTypes.some(
                                    item => newValue.includes(item))
                                ) {
                                    const bizTypeName = getBizTypeName(bizType);
                                    suggestBizTypeNames.push(bizTypeName);
                                }
                            }
                        );
                        if (!suggestBizTypeNames.length) {
                            return '';
                        }
                        const bizTypeNameText = suggestBizTypeNames.join('、');
                        return `系统检测到您投放的基木鱼落地页上挂载了${bizTypeNameText}组件，至少选择一个${bizTypeNameText}类转化目标`;
                    }
                    return '';
                },
                triggers: ['onChange', '@final'],
            },
        ],
        visible: formData => {
            return formData.ocpcBidType !== FcOcpcBidTypeEnum.CPC
                && !isBjhNoteSubMarket(formData.subMarketingTargetId)
                && !isBjhImageTextSubMarket(formData.subMarketingTargetId);
        },
        ...config,
    } as FormFieldConfig<T>;
}


export const Field$$OptimizeTransTarget: FormFieldConfig<FcProjectFormData> = {
    field: 'transTarget',
    label: '优化目标',
    use: [withBorderAnimation('transTarget', SelectCard)],
    componentProps: ({
        ocpcPayTimesStatus,
        subMarketingTargetId,
    }) => {
        return {
            disabled: ocpcPayTimesStatus,
            options: getOptimizeTransTargetOptions(subMarketingTargetId),
        };
    },
    visible: formData => {
        return formData.ocpcBidType !== FcOcpcBidTypeEnum.CPC
            && (isBjhNoteSubMarket(formData.subMarketingTargetId)
            || isBjhImageTextSubMarket(formData.subMarketingTargetId));
    },
    validators: [
        v => {
            if (!v) {
                return '请选择优化目标';
            }
            return '';
        },
    ],
};

export const Field$$NativeTransTypes: FormFieldConfig<FcProjectFormData> = {
    field: 'transTypes',
    use: [withBorderAnimation('transTypes', NativeTransTypes)],
    componentProps: ({
        projectId,
        marketingTargetId,
        subMarketingTargetId,
        transAsset,
        transManagerMode,
        initialData,
        ocpcPayTimesStatus,
    }) => {
        return {
            isEdit: !!projectId,
            projectId,
            marketingTargetId,
            subMarketingTargetId,
            transManagerMode,
            transAsset,
            initialData,
            disabled: ocpcPayTimesStatus,
            transTypeList: nativeTransTypeList,
        };
    },
    validators: [
        (v, {projectId, initialData}) => {
            if (projectId && initialData.transTypes
                && initialData.bidType === FcBidTypeEnum.OCPC
                && intersection(v, initialData.transTypes).length === 0) {
                return `已设置的目标转化仅允许做局部调整，如果您需要投放完全不同的转化目标，请重新设置。已设置的目标转化为：${
                    initialData.transTypes.map(type => TRANS_TYPE[type]).join('、')}。`;
            }
            if (!v.length) {
                return '请选择优化目标';
            }
            return '';
        },
    ],
    visible: formData => {
        return formData.ocpcBidType !== FcOcpcBidTypeEnum.CPC
            && (isBjhNoteSubMarket(formData.subMarketingTargetId)
            || isBjhImageTextSubMarket(formData.subMarketingTargetId))
            && formData.transTarget === TRANS_TARGET_MAP.MORE;
    },
};

export const Field$$TransTypes = createTransTypesField();

export const Field$$OcpcPayTimesStatus: FormFieldConfig<FcProjectFormData> = {
    field: 'ocpcPayTimesStatus',
    label: '优化付费次数',
    use: [withBorderAnimation('ocpcPayTimesStatus', RadioGroupWithDisabledTip), {
        options: [
            {label: '不开启', value: false},
            {label: '开启', value: true},
        ],
        className: 'campaign-paytimes',
        width: InputWidth,
    }],
    visible: ({transTypes, ocpcBidType}) => {
        return ocpcBidType !== FcOcpcBidTypeEnum.CVMAX
            && transTypes.length === 1
            && transTypes[0] === 26;
    },
    componentProps: ({projectId}) => {
        return {
            disabled: !!projectId,
        };
    },
};

export const Field$$TargetRoiRatio: FormFieldConfig<FcProjectFormData> = {
    // ! 目标ROI模式下，需要目标ROI系数
    field: 'targetRoiRatio',
    label: (
        <span>
            目标ROI系数
            <Tip text="目标ROI系数" iconOnly />：
        </span>
    ),
    use: [withBorderAnimation('targetRoiRatio', UINumberInput), {
        width: InputWidth,
        step: 1,
        min: minTargetRoiRatio,
        max: maxTargetRoiRatio,
        placeholder: '请输入目标ROI系数',
    }],
    rules: [['numberRange', [minTargetRoiRatio, maxTargetRoiRatio]]],
    visible: formData => {
        return formData.ocpcBidType === FcOcpcBidTypeEnum.ROI;
    },
};

export function createOcpcBidField<T extends FcProjectFormData>(config?: Partial<FormFieldConfig<T>>) {
    return {
        field: 'ocpcBid',
        label: '目标转化出价',
        use: [withBorderAnimation('ocpcBid', OcpcBid), {
            placeholder: '请输入目标转化出价',
            fixed: 2,
            width: '330px',
            tailLabel: '元/转化',
        }],
        rules: [['numberRange', [0.5, 9999], '请保证目标转化出价在[0.5, 9999]区间']],
        validators: (v, formData) => {
            if (!v) {
                return '请填写目标转化出价';
            }
            if (+formData.ocpcDeepBid && v >= +formData?.ocpcDeepBid) {
                return '目标转化出价不能高于深度目标转化出价';
            }
            return '';
        },
        visible: formData => {
            return formData.industrySolution !== INDUSTRY_SOLUTION.KXT
                && (formData.ocpcBidType === FcOcpcBidTypeEnum.OCPC
                    || formData.ocpcBidType === FcOcpcBidTypeEnum.OPTIMAL_COST);
        },
        componentProps: ({
            projectId,
            transTypes,
            deepTransTypes,
            transTypeMultiBidStatus,
            isUseOcpcBidRatio,
            projectModeType,
            subMarketingTargetId,
        }) => {
            return {
                transTypes,
                deepTransTypes,
                transTypeMultiBidStatus,
                isEdit: !!projectId,
                isUseOcpcBidRatio,
                projectModeType,
                subMarketingTargetId,
            };
        },
        ...config,
    } as FormFieldConfig<T>;
}
export const Field$$OcpcBid = createOcpcBidField();

export const wrapField$$DeepTransTypeMode = (transInfo: any): FormFieldConfig<FcProjectFormData> => ({
    field: 'deepTransTypeMode',
    label: '深度优化方式',
    use: [DeepTransTypeMode],
    validators: (v, formData) => {
        const isRoiValid = isDeepRoiValidByCvSourceTransAssetsAndTransTrack(formData, transInfo);
        if (!isRoiValid && v === DeepTransTypeModeEnum.ROI) {
            return '当前选择的优化目标未设置转化价值，无法为您进行转化价值优化，建议前往转化追踪/转化资产设置';
        }
        return '';
    },
    componentProps: ({
        projectId,
        subMarketingTargetId,
        transManagerMode,
        transAsset,
        transTypes,
        cvSources,
        deepRoiStatus,
    }) => {
        return {
            isEdit: !!projectId,
            subMarketingTargetId,
            transManagerMode,
            transAsset,
            transTypes,
            transInfo,
            cvSources,
            deepRoiStatus,
        };
    },
});

export const Field$$DeepTransTypes: FormFieldConfig<FcProjectFormData> = {
    field: 'deepTransTypes',
    label: (
        <span>
            深度优化目标(选填)
            <HoverTip
                content={`系统将根据对您的深度转化数据的学习程度，
                分阶段为您优化浅层目标转化和深层目标转化，
                逐步过渡至基于您设置的深度目标转化出价为您最大化深层目标转化。
                系统亦将尝试为您推荐适宜的深度目标转化类型。
                如果您希望优化“次日留存/关键页面浏览”，系统将为您优选深度转化率高的流量投放，
                在保障目标转化成本的同时，优化实际投放目标深度转化率。
            `}
            />
        </span>
    ),
    use: [withBorderAnimation('deepTransTypes', DeepTransTypes)],
    componentProps: ({
        marketingTargetId, subMarketingTargetId, transAsset,
        transManagerMode, cvSources, transTypes,
    }) => {
        return {
            transTypes,
            marketingTargetId,
            subMarketingTargetId,
            transManagerMode,
            transAsset,
            cvSources,
        };
    },
    visible: formData => {
        return !isBjhNoteSubMarket(formData.subMarketingTargetId)
        && !isBjhImageTextSubMarket(formData.subMarketingTargetId)
        && formData.ocpcBidType === FcOcpcBidTypeEnum.OCPC
        && !formData.transTypeMultiBidStatus
        && formData.deepTransTypeMode === DeepTransTypeModeEnum.TRANS_TYPE
        && !formData.ocpcPayTimesStatus;
    },
};

export const Field$$NativeDeepTransTypes: FormFieldConfig<FcProjectFormData> = {
    ...Field$$DeepTransTypes,
    use: [DeepTransTypes, {tipPosition: 'right'}],
    componentProps: ({
        marketingTargetId, transAsset, transManagerMode,
        cvSources, transTypes, subMarketingTargetId, transTarget,
    }) => {
        return {
            transTypes: transTarget === TRANS_TARGET_MAP.MORE ? transTypes : [transTarget],
            marketingTargetId,
            subMarketingTargetId,
            transManagerMode,
            transAsset,
            cvSources,
        };
    },
    visible: formData => {
        return (isBjhNoteSubMarket(formData.subMarketingTargetId)
            || isBjhImageTextSubMarket(formData.subMarketingTargetId))
            && formData.ocpcBidType === FcOcpcBidTypeEnum.OCPC
            && formData.deepTransTypeMode === DeepTransTypeModeEnum.TRANS_TYPE;
    },
};

export const getField$$OcpcDeepBid = (
    isEdit?: boolean,
    deepTransTypeStatus?: any,
    initialData?: FcProjectFormData,
): FormFieldConfig<FcProjectFormData> => ({
    field: 'ocpcDeepBid',
    label: (
        <span>
            深度目标转化出价
            {
                (isDoublePriceIILevelUser() && isEdit && [
                    deepTypeStatOptionsWithReason.DEEP_STAT_VALID,
                    deepTypeStatOptionsWithReason.DEEP_ROI_VALID,
                ].includes(deepTransTypeStatus)) || !isDoublePriceIILevelUser() ? ''
                    : '(选填)'
            }
            <HoverTip
                content={`系统将根据您设定的深度转化出价优化，争取获得更多的深度转化量并保证转化成本相对稳定。
            `}
            />
        </span>
    ),
    use: [withBorderAnimation('ocpcDeepBid', OcpcDeepBid), {
        placeholder: '请输入深度目标转化出价',
        fixed: 2,
        width: '280px',
        tailLabel: '元/转化',
    }],
    // rules: [['numberRange', [0.5, 9999], '请保证深度目标转化出价在[0.5, 9999]区间']],
    validators: (v, formData) => {
        if ((isDoublePriceIILevelUser() && isEdit && [
            deepTypeStatOptionsWithReason.DEEP_STAT_VALID,
            deepTypeStatOptionsWithReason.DEEP_ROI_VALID,
        ].includes(deepTransTypeStatus)) || !isDoublePriceIILevelUser()) {
            if (!v) {
                return '请填写深度目标转化出价';
            }
        }

        if (+v && (+v < 0.5 || +v > 9999)) {
            return '请保证深度目标转化出价在[0.5, 9999]区间';
        }
        if (+v && +v <= +formData?.ocpcBid) {
            return '深度目标转化出价不能低于目标转化出价';
        }

        return '';
    },
    visible: formData => {
        return !!formData.deepTransTypes?.length
            && formData.ocpcBidType === FcOcpcBidTypeEnum.OCPC
            && !formData.transTypeMultiBidStatus
            && formData.deepTransTypeMode === DeepTransTypeModeEnum.TRANS_TYPE;
    },
    componentProps: ({suggestDeepBid}) => {
        return {
            suggestDeepBid,
            initialValue: initialData?.ocpcDeepBid,
        };
    },
});

export const wrapField$$DeepTargetRoiRatio = (isLGUI: boolean = false): FormFieldConfig<FcProjectFormData> => ({
    field: 'deepTargetRoiRatio',
    label: (
        <span>
            ROI系数(选填)
            <HoverTip
                content={
                    `建议目标ROI系数设置不过高，过高可能影响流量获取；系统根据您设置的目标ROI系数进行出价，
                    目标ROI系数=广告投放带来的转化价值/广告消费；建议降低修改目标ROI系数的频次和幅度，
                    保证优化目标的稳定性，稳定获量且效果达成。`
                }
            />
        </span>
    ),
    use: [withBorderAnimation('deepTargetRoiRatio', DeepTargetRoiRatio)],
    validators: (v, formData) => {
        if (v && !(+v > 0 && +v <= maxDeepTargetRoiRatio)) {
            return 'ROI系数介于0到100之间';
        }
        if (!v && !!formData.projectId
            && formData.deepTransTypeStatus === deepTypeStatOptionsWithReason.DEEP_ROI_VALID
        ) {
            return '项目处在【优化深度ROI】阶段，不允许清空ROI系数';
        }
        return '';
    },
    rules: [['numberRange', [minDeepTargetRoiRatio, maxDeepTargetRoiRatio]]],
    visible: formData => {
        const showDeepRoiRatio = isFcOcpcDeepRoiUser() && formData.deepTransTypeMode === DeepTransTypeModeEnum.ROI;
        return isLGUI
            ? !!formData.projectId && showDeepRoiRatio // 对话式创编只在编辑下支持
            : showDeepRoiRatio;
    },
});

export const Group$$FcDeepTransTypeMode: FormFieldConfig<FcProjectFormData> = {
    group: 'FcDeepTransTypeMode',
    use: [FcDeepTransTypeModeDivGroup],
    visible: formData => {
        return formData.ocpcBidType === FcOcpcBidTypeEnum.OCPC
            && !formData.transTypeMultiBidStatus
            && formData.industrySolution !== INDUSTRY_SOLUTION.KXT
            && !formData.ocpcPayTimesStatus;
    },
    fields: [],
};

export const Field$$PreserveRate: FormFieldConfig<FcProjectFormData> = {
    field: 'preserveRate',
    label: '目标次日留存率',
    use: [PreserveRate],
    visible: formData => {
        return getIsPreserveRataType(
            formData.transTypes, formData.deepTransTypes) && formData.ocpcDeepBid > 0;
    },
    componentProps: ({ocpcBid, ocpcDeepBid}) => {
        return {
            ocpcBid,
            ocpcDeepBid,
        };
    },
};

export const Field$$UseProjectBudget: FormFieldConfig<FcProjectFormData> = {
    field: 'useProjectBudget',
    label: '预算',
    use: [ProjectBudgetSetting],
    componentProps: ({
        projectId,
        ocpcBidRatioType,
        budgetType,
        smartLiftBudget,
        initialData,
        useImmediateLiftBudget,
        liftBudgetSwitch,
        isUseOcpcBidRatio,
        aiMaxMarketingLevel,
        liftBudgetMode,
        ocpcBidType,
        industrySolution,
    }) => {
        return {
            projectId,
            ocpcBidType,
            smartLiftBudget: liftBudgetSwitch ? smartLiftBudget : SmartLiftBudgetEnum.STOP,
            budgetType,
            aiMaxMarketingLevel,
            ocpcBidRatioType: isUseOcpcBidRatio ? ocpcBidRatioType : OcpcBidRatioTypeEnum.noUse,
            preBudget: initialData.sharedBudget,
            preBudgetType: initialData.budgetType,
            preUseProjectBudget: initialData.useProjectBudget,
            useImmediateLiftBudget,
            ...(
                liftBudgetSwitch ? {liftBudgetMode} : {}
            ),
            industrySolution,
        };
    },
};

export function createSharedBudgetField<T extends FcProjectFormData>(
    config?: Partial<FormFieldConfig<T>>,
    fieldConfig?: {width?: string}
) {
    return {
        field: 'sharedBudget',
        label: (
            <span>
                预算金额
                <Tooltip
                    title="当前项目下的所有营销方案将共同使用该项目预算"
                    placement="top"
                >
                    <IconQuestionCircleSolid style={{marginLeft: '4px', color: 'rgba(60, 83, 133, 0.3)'}} />
                </Tooltip>
            </span>
        ),
        use: [withBorderAnimation('sharedBudget', SharedBudget)],
        visible: formData => formData.useProjectBudget,
        validators(v_, formData) {
            const v = Number(v_);
            const {ocpcBid, ocpcDeepBid, budgetType, liftBudget, bidType} = formData || {};
            if (bidType === FcBidTypeEnum.CPC) {
                return '';
            }
            const [min, max] = ProjectBudgetLimit[budgetType];
            if (!v) {
                return '请填写预算';
            }
            if (v < min || v > max) {
                return `范围：${min} ～ ${max}`;
            }
            if (v !== 0) {
                if (liftBudget && v < liftBudget) {
                    return '预算不能小于起量预算';
                }
                if (ocpcDeepBid && v <= ocpcDeepBid) {
                    return '预算要大于深度目标转化成本';
                }
                if (ocpcBid && v <= ocpcBid) {
                    return '预算要大于目标转化成本';
                }
            }
            return '';
        },
        componentProps: ({budgetType, ocpcBidType, transTypes, projectId}) => {
            return {
                budgetType,
                ocpcBidType,
                transTypes,
                projectId,
                width: fieldConfig?.width,
            };
        },
        ...config,
    } as FormFieldConfig<T>;
}

export const Field$$SharedBudget = createSharedBudgetField();

export const Field$$ImmediateLiftBudget: FormFieldConfig<FcProjectFormData> = {
    field: 'useImmediateLiftBudget',
    label: '一键起量',
    use: [ImmediateLiftBudgetRadio],
    componentProps: ({liftBudgetMode, liftBudgetSwitch, liftBudgetStatus, liftBudgetModeRadio}) => {
        return {
            liftBudgetMode,
            liftBudgetSwitch,
            liftBudgetStatus,
            liftBudgetModeRadio,
        };
    },
    visible: formData => formData.ocpcBidType === FcOcpcBidTypeEnum.OCPC
        && formData.industrySolution !== INDUSTRY_SOLUTION.KXT,
};

export const Field$$BudgetTypeRadio: FormFieldConfig<FcProjectFormData> = {
    field: 'budgetType',
    label: '预算类型',
    use: [BudgetTypeRadio],
    visible: formData => {
        return formData.useProjectBudget
            && AiMaxEnabledByMT[AiMaxOptionEnum.WEEK_BUDGET].includes(formData.marketingTargetId)
            && formData.industrySolution !== INDUSTRY_SOLUTION.KXT
            && [FcOcpcBidTypeEnum.OCPC, FcOcpcBidTypeEnum.ROI].includes(formData.ocpcBidType);
    },
};

export const Field$$LiftBudget: FormFieldConfig<FcProjectFormData> = {
    field: 'liftBudget',
    label: '起量预算',
    use: [withBorderAnimation('liftBudget', UINumberInput), {
        width: '330px',
        placeholder: '请输入起量预算',
        fixed: 2,
    }],
    visible: formData => {
        return formData.ocpcBidType === FcOcpcBidTypeEnum.OCPC
            && !!formData.useImmediateLiftBudget
            && formData.industrySolution !== INDUSTRY_SOLUTION.KXT;
    },
    rules: [
        ['required', '请填写起量预算'],
    ],
    validators(v, formData) {
        const {sharedBudget} = formData || {};
        if (v > sharedBudget) {
            return '起量预算不得大于预算值';
        }
        return '';
    },
    componentProps: ({liftBudgetStatus}) => {
        return {
            disabled: liftBudgetStatus === LiftBudgetStatus.RUNNING,
        };
    },
};

export const getWatchConfig$$FcProject = (transInfo: any): WatchConfig<FcProjectFormData> => ({
    deepTransTypeMode: (value, formData) => {
        if (value === DeepTransTypeModeEnum.TRANS_TYPE) {
            formData.deepTransTypes = [];
            formData.ocpcDeepBid = 0;
        }
        if (value === DeepTransTypeModeEnum.ROI) {
            formData.deepTargetRoiRatio = undefined;
        }
    },
    projectModeType: (value, formData) => {
        if (value === FcProjectModeType.CUSTOM) { // AIMAX转为自定义时置为关
            formData.isUseOcpcBidRatio = false;
            formData.budgetType = ProjectBudgetType.DAY;
            formData.liftBudgetSwitch = false;
            formData.autoOrientStatus = false;
            formData.autoCreativeStatus = false;
            formData.aiMaxMarketingLevel = false;
            formData.isUseAgentRotate = false;
        }
        else {
            // AI投放模式不支持多目标精细化出价
            formData.transTypeMultiBidStatus = false;
            formData.transTypesOcpcBidRatio = {};
        }
    },
    useProjectBudget: (value, formData) => {
        if (value) {
            formData.budgetType = formData.budgetType || ProjectBudgetType.DAY;
        }
    },
    bidType: (value, formData) => {
        if (value === FcBidTypeEnum.CPC) {
            formData.ocpcBidType = FcOcpcBidTypeEnum.ECPC;
            formData.useImmediateLiftBudget = 0;
            if ([
                FC_MARKET_TARGET.SHOP,
                FC_MARKET_TARGET.STORE,
                FC_MARKET_TARGET.COMMODITY,
            ].includes(formData.marketingTargetId)) {
                formData.projectModeType = FcProjectModeType.CUSTOM;
            }
        }
        if (value === FcBidTypeEnum.OCPC) {
            formData.ocpcBidType = FcOcpcBidTypeEnum.OCPC;
            formData.deepTransTypeMode = DeepTransTypeModeEnum.TRANS_TYPE;
        }
        if (formData.subMarketingTargetId === FC_SUB_MARKET_TARGET.DQA) {
            formData.schedule = {
                type: schedueTypeConfig.all,
                scheduleValue: getFormateValuesFromBackend(
                    getDefaultPriceFactorsFromSchedule([]), value === FcBidTypeEnum.OCPC),
            };
        }
    },
    transManagerMode: (value, formData) => {
        formData.transTypes = [];
        formData.deepTransTypes = [];
        formData.ocpcPayTimesStatus = false;

        if (value === TransManagerModeType.asset
            && formData.transAsset.option === FcTransAssetOptionsEnum.UNLIMITED
        ) {
            formData.transAsset.option = FcTransAssetOptionsEnum.BY_ASSET_ID;
        }

        formData.transId = undefined;
    },
    cvSources: (value, formData) => {
        const {marketingTargetId, transManagerMode, transAsset, transTypes, deepTransTypes} = formData;
        const transTypeList = getTransTypeList({
            ...transInfo,
            marketingTargetId,
            transManagerMode,
            transAsset,
            cvSources: value,
        });
        const newTransTypes = intersection(transTypeList, transTypes);
        formData.transTypes = newTransTypes;
        const validDeepTransTypes = getValidDeepTransTypes(
            newTransTypes,
            deepTransTypes,
            {
                isShowComprehensiveClueInDeepTrans: getShowComprehensiveClueDeep({
                    transManagerMode,
                    transTypes,
                }),
            }
        );
        const newDeepTransTypes = intersection(validDeepTransTypes, deepTransTypes);
        formData.deepTransTypes = newDeepTransTypes;
    },
    transAsset: (value, formData) => {
        const {
            marketingTargetId, subMarketingTargetId, transManagerMode, transTypes, deepTransTypes, cvSources,
            ocpcBidType,
        } = formData;

        if (ocpcBidType === FcOcpcBidTypeEnum.ROI) {
            formData.transTypes = [];
            formData.deepTransTypes = [];
            return;
        }

        const transTypeList = getTransTypeList({
            ...transInfo,
            marketingTargetId,
            transManagerMode,
            transAsset: value,
            cvSources,
        });
        const newTransTypes = intersection(transTypeList, transTypes);
        formData.transTypes = newTransTypes;
        const validDeepTransTypes = getValidDeepTransTypes(
            newTransTypes,
            deepTransTypes,
            {
                isShowComprehensiveClueInDeepTrans: getShowComprehensiveClue({
                    marketingTargetId,
                    subMarketingTargetId,
                    transManagerMode,
                    transAsset: value,
                }),
            }
        );
        const newDeepTransTypes = intersection(validDeepTransTypes, deepTransTypes);
        formData.deepTransTypes = newDeepTransTypes;
    },
    transTypes: (value, formData) => {
        const validDeepTransTypes = getValidDeepTransTypes(
            value,
            formData.deepTransTypes,
            {
                isShowComprehensiveClueInDeepTrans: getShowComprehensiveClueDeep({
                    transManagerMode: formData.transManagerMode,
                    transTypes: formData.transTypes,
                    transAsset: formData.transAsset,
                }),
            }
        );
        formData.deepTransTypes = intersection(formData.deepTransTypes, validDeepTransTypes);
        // 如果是编辑场景，不允许关闭多目标精细化出价。其余情况当选择的转化目标小于等于1个时，关闭多目标精细化出价。
        if (
            value && value.length <= 1
            && !(formData.projectId && formData.transTypeMultiBidStatus)
        ) {
            formData.transTypeMultiBidStatus = false;
            formData.transTypesOcpcBidRatio = {};
        }
        else if (formData.transTypeMultiBidStatus) {
            // 当转化目标发生取消选择时，也要取消多目标精细化设置里的选择
            const newOcpcBidRatio = cloneDeep(formData.transTypesOcpcBidRatio);
            const {
                transTypes: {
                    type: transTypes = [],
                } = {},
                assistTransTypes: {
                    type: assistTransTypes = [],
                } = {},
            } = newOcpcBidRatio || {};
            set(newOcpcBidRatio, 'transTypes.type',
                transTypes.filter(v => value.includes(v)));
            set(newOcpcBidRatio, 'assistTransTypes.type',
                assistTransTypes.filter(v => value.includes(v)));
            formData.transTypesOcpcBidRatio = newOcpcBidRatio;
        }
    },
    deepTransTypes: (value, formData) => {
        formData.transTypeMultiBidStatus = false;
        formData.transTypesOcpcBidRatio = {};
    },
    isUseOcpcBidRatio: (value, formData) => {
        if (value && formData.ocpcBidRatioType === OcpcBidRatioTypeEnum.noUse) {
            formData.ocpcBidRatioMode = liftBudgetModeRadioMap.customRules;
            const fieldsInitialObj = getOcpcBidRatioTypeInitialValue() as Record<string, any>;
            Object.keys(fieldsInitialObj).forEach(key => {
                formData[key] = fieldsInitialObj[key];
            });
        }
        if (value) {
            formData.useProjectBudget = true;
        }
    },
    liftBudgetModeRadio: (value, formData) => {
        if (value === liftBudgetModeRadioMap.autoOptimization
            && formData.smartLiftBudget === SmartLiftBudgetEnum.STOP) {
            formData.smartLiftBudget = SmartLiftBudgetEnum.NORMAL;
        }
    },
    useImmediateLiftBudget: (value, formData) => {
        // 开启立即起量需要把长周期起量关闭
        if (!!value && formData.liftBudgetModeRadio === liftBudgetModeRadioMap.customRules) {
            formData.liftBudgetModeRadio = liftBudgetModeRadioMap.autoOptimization;
            // 不支持自动优选的，直接置灰一键起量的开关
            if (!AutoOptimizationEnabledMt.includes(formData.marketingTargetId)) {
                formData.liftBudgetSwitch = false;
                formData.liftBudgetModeRadio = liftBudgetModeRadioMap.customRules;
            }
        }
        if (!!value && !formData.useProjectBudget) {
            formData.useProjectBudget = true;
        }
    },
    aiMaxMarketingLevel: (value, formData) => {
        if (value && value !== AiMaxMarketingLevelEnum.SMALL) {
            formData.useProjectBudget = true;
        }
    },
    liftBudgetSwitch: (value, formData) => {
        if (value) {
            formData.useProjectBudget = true;
        }
    },
    ocpcBidType: (value, formData) => {
        if (value === FcOcpcBidTypeEnum.ECPC) {
            formData.transTypes = [];
        }
        if (value === FcOcpcBidTypeEnum.OCPC) {
            formData.transTypes = [];
            formData.deepTransTypeMode = DeepTransTypeModeEnum.TRANS_TYPE;
        }
        else {
            // 仅目标转化成本可以使用深度优化目标
            formData.deepTransTypes = [];
            formData.deepTransTypeMode = DeepTransTypeModeEnum.NOT_ADOPT;
            formData.useImmediateLiftBudget = 0;
        }


        if (value === FcOcpcBidTypeEnum.CVMAX || value === FcOcpcBidTypeEnum.OPTIMAL_COST) {
            // 放量模式和最优成本限制必须使用项目预算
            formData.useProjectBudget = true;
            formData.ocpcPayTimesStatus = false;
        }

        // !===================aimax中的一些和竞价策略相关的限制======================================
        if (value !== FcOcpcBidTypeEnum.OCPC) {
            // 仅目标转化成本可以使用aimax行业版，其他的竞价策略只能使用aimax通用版
            formData.aiMaxModeSwitch = AiMaxVersion.NORMAL;
            formData.useIndustryAIMaxPromotion = false;
            formData.aiMaxMarketingLevel = undefined;
        }
        // 这几个营销目标只支持ocpc和roi，其他的就转换成自定义，不支持设置aimax
        if (isAimaxDisabled({marketingTargetId: formData.marketingTargetId, ocpcBidType: value})) {
            formData.projectModeType = FcProjectModeType.CUSTOM;
        }

        if (
            value === FcOcpcBidTypeEnum.CVMAX
            || value === FcOcpcBidTypeEnum.OPTIMAL_COST
        ) {
            // 放量模式、最优成本、目标ROI，限制不能使用智能起量，限制不能使用7日预算（只能日预算）
            formData.liftBudgetSwitch = false;
            formData.budgetType = ProjectBudgetType.DAY;
        }
        if (value === FcOcpcBidTypeEnum.CVMAX || value === FcOcpcBidTypeEnum.ROI) {
            // 放量模式和目标ROI，限制不能使用精细化出价
            formData.isUseOcpcBidRatio = false;
        }
        // !======================================================================================

        if (value === FcOcpcBidTypeEnum.ROI) {
            if (formData.transManagerMode === TransManagerModeType.unlimited) {
                formData.transManagerMode = TransManagerModeType.cvSource;
            }
            if (formData.transManagerMode === TransManagerModeType.asset) {
                formData.transAsset.transAssetId = formData.transAsset.transAssetId.length
                    ? formData.transAsset.transAssetId
                    : [];
                formData.transAsset.option = FcTransAssetOptionsEnum.BY_ASSET_ID;
            }
            formData.transTypes = [];
        }

        if ((isBjhNoteSubMarket(formData.subMarketingTargetId)
            || isBjhImageTextSubMarket(formData.subMarketingTargetId))
            && value !== FcOcpcBidTypeEnum.OCPC
        ) {
            formData.projectModeType = FcProjectModeType.CUSTOM;
        }
    },
    transId: (value, formData) => {
        formData.transTypes = [];
    },
    transTypeMultiBidStatus: (value, formData) => {
        formData.deepTransTypes = [];
    },
    subMarketingTargetId: (value, formData) => {
        if (value === FC_SUB_MARKET_TARGET.LIVE) {
            formData.bidType = FcBidTypeEnum.OCPC;
            formData.ocpcBidType = FcOcpcBidTypeEnum.OCPC;
            formData.transManagerMode = TransManagerModeType.asset;
            formData.transAsset.option = FcTransAssetOptionsEnum.BY_ASSET_ID;
            formData.transTypes = [];
        }
        else if (value === FC_SUB_MARKET_TARGET.NORMAL) {
            formData.transTypes = [];
        }
        if (isBjhNoteSubMarket(value)) {
            formData.transTarget = formatTransTargetToFormValue({
                transTypes: formData.transTypes,
                projectId: formData.projectId,
                subMarketingTargetId: value,
            });
        }
        if (isBjhImageTextSubMarket(value)) {
            formData.transTypes = [EFFECTIVE_READING_CLUES];
            formData.transTarget = formatTransTargetToFormValue({
                transTypes: formData.transTypes,
                projectId: formData.projectId,
                subMarketingTargetId: value,
            });
        }
    },
    transTarget: (value, formData) => {
        if (value === TRANS_TARGET_MAP.MORE) {
            formData.transTypes = [];
        }
        else {
            formData.transTypes = [value];
        }
    },
    structuredProductIds: (value, formData) => {
        // 针对旅校通场景的逻辑处理
        if (formData?.productCategoryType === productCategoryTypeEnum.LXT) {
            // 如果选择了产品，则开启旅游行业版aimax
            if (value?.length && !formData.productIdsSettedOnce) {
                formData.productIdsSettedOnce = true;
                formData.transTypeMultiBidStatus = false;
                formData.projectModeType = FcProjectModeType.AIMAX;
                formData.aiMaxModeSwitch = AiMaxVersion.INDUSTRY;
                formData.aiMaxMarketingLevel = AiMaxMarketingLevelEnum.ACTIVE;
            }
        }
    },
});

export const getFormConfig = ({transInfo, instructions}: {transInfo: any, instructions: Instructions}) => {
    const formConfig: FormConfig<FcProjectFormData> = {
        fields: [
            {
                field: 'aiMax',
                use: [AiMax, {title: 'instructions', instructions}],
                componentProps: ({
                    useIndustryAIMaxPromotion,
                    aiMaxMarketingLevel,
                    ocpcBidRatioType,
                    budgetType,
                    smartLiftBudget,
                    liftBudgetMode,
                    liftBudgetSwitch,
                    isUseOcpcBidRatio,
                    projectModeType,
                }) => {
                    return {
                        useIndustryAIMaxPromotion,
                        aiMaxMarketingLevel,
                        ocpcBidRatioType,
                        budgetType,
                        smartLiftBudget,
                        liftBudgetMode,
                        liftBudgetSwitch,
                        isUseOcpcBidRatio,
                        projectModeType,
                    };
                },
            },
            {
                group: FcGroupField.basicGroup,
                use: [EditorGroup, {title: '基本信息', ...groupProps}],
                fields: [
                    Field$$ProjectName,
                    {
                        field: 'marketingTargetId',
                        label: '营销目标',
                        use: [withBorderAnimation('marketingTargetId', ReadOnlyText), {
                            disabled: true,
                            transForm: (value: FC_MARKET_TARGET) => fcMarketTargetNameMapByValue[value],
                            width: InputWidth,
                        }],
                    },
                    Field$$SubMarketingTargetId,
                    {
                        ...Field$$ProjectModeType,
                        use: [withBorderAnimation('projectModeType', ProjectModeType), {
                            width: InputWidth,
                            instructions,
                        }],
                    },
                    {
                        ...Field$$BindIds,
                        use: [withBorderAnimation('bindIds', BindCampaigns), {instructions, width: InputWidth}],
                    },
                    {
                        field: 'projectAgentUrl',
                        label: '商家智能体',
                        use: [
                            withBorderAnimation('projectAgentUrl', ProjectAgentUrl),
                            {instructions, width: InputWidth},
                        ],
                        visible: formData => isSupportAgent(formData),
                    },
                ],
            },
            {
                group: FcGroupField.bidGroup,
                use: [EditorGroup, {title: '优化目标与成本预期', ...groupProps}],
                fields: [
                    Field$$BidType,
                    Field$$OcpcBidType,
                    Field$$TransManagerMode,
                    Field$$CvSources,
                    Field$$TransAsset,
                    Field$$TransId,
                    Field$$TransTypes,
                    Field$$OcpcPayTimesStatus,
                    Field$$TargetRoiRatio,
                    Field$$OcpcBid,
                    {
                        ...Group$$FcDeepTransTypeMode,
                        fields: [
                            wrapField$$DeepTransTypeMode(transInfo),
                            Field$$DeepTransTypes,
                            getField$$OcpcDeepBid(),
                            wrapField$$DeepTargetRoiRatio(true),
                        ],
                    },
                    Field$$PreserveRate,
                    Field$$UseProjectBudget,
                    Field$$SharedBudget,
                    Field$$ImmediateLiftBudget,
                    Field$$LiftBudget,
                ],
            },
        ],
        watch: getWatchConfig$$FcProject(transInfo),
    };
    return formConfig;
};
