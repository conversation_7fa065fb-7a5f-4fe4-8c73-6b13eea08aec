/* eslint-disable no-negated-condition */
/* eslint-disable complexity */
// @ts-ignore
import {formatOcpcBidRatioResponse, formateRequestParamsForm} from 'feedCommonLibs/AiMax';
// @ts-ignore
import {ageTypeMap} from 'feedCommonLibs/config/bid/ocpcBidRatio';
import {isMultiTransFrom} from 'feedCommonLibs/config/bid/transType';
import {merge, omit, pick} from 'lodash-es';
import dayjs from 'dayjs';
import {FeedTransAsset} from '@/api/feedProject';
import {transNameMapByValue} from '@/config/transType';
import {
    BidModeEnum,
    BsType, FeedAIMaxEduLevel, FeedAIMaxTradeVersionType, FeedProjectFormData, feedProjectModeType, FeedProjectParams,
    FeedProjectResp, FeedSubjectType, LiftRespType,
    SaleType,
    SmartControlEnum,
    TransTypeAttribute,
    UseLiftBudgetOPS,
} from '@/interface/aixProject/feedProject';
import {
    FeedUseLiftBudget, AILiftModel, FeedLiftBudgetStatus,
    LiftOption, ScheduleModel,
} from '@/config/feed/liftBudget';
import {LiftSwitchEnum, UseProjectBudgetEnum, AutoIdeaSwitchEnum, ReopenAutoIdeaSwitchEnum} from '@/dicts/project';
import {FeedOcpcBidRatioTypeEnum} from '@/dicts/ocpcBidRatio';
import {ocpcBidRatioModeRadioMap} from '@/components/ChatCards/FeedAiMax/config';
import {BS_TYPE_DICT, checkFeedSubjectType} from '@/config/feed';
import {marketTargetNameMapByValue} from '@/config/feed/marketTarget';
import {
    isFeedShareBudgetUser,
    isSoftLinkFeedProjectUser,
    isFeedAiLiftMoreStartTimeUser,
    isFeedBJHNew,
} from '@/utils/getFlag';
import {isBJHShortPlayAIMaxUser, isFeedSmartControl} from '@/utils/getFeedFlag';
import {
    getEducationIndustry, getFeedAIMaxIndustry, getMedecalIndustry,
} from '@/components/List/ProjectList/AiAgent/util';
import globalData from '@/utils/globalData';
import {spyxTrade} from '@/config/feed/trade';
import {UsePublicEnum} from '@/interface/feedEditor/creative';
import {isBjhRoi, getPayCountShowStatus} from '@/modules/Ad/PromotionMain/FEED/project/utils';
import {FeedAIMaxTradeVersionTypeConfig} from '@/components/ChatCards/FeedAiMax/edu';
import {DEEP_OPT_TYPE} from '@/config/feed/bid';
import {MiniProgramTypeEnum} from '@/config/feed/miniProgram';
import {checkIsMultiProducts, checkIsSupportMultiProducts} from '@/config/feed/multiProducts';
import {TRANS_FROM_ENUM} from '@/config/feed/campaign';
import {isFeedSpyxTrade} from '@/utils/getFeedFlag';
import {checkIsBjhShortPlaySimplifiedScene} from './BJHType/config';

export function isAiAgentOpen({
    liftSwitch,
    ocpcBidRatioType,
    aiMaxTradeVersionLevel,
    autoIdeaSwitch,
    useLiftBudget,
    smartControl,
    lift,
}: {
    liftSwitch: LiftSwitchEnum;
    ocpcBidRatioType: FeedOcpcBidRatioTypeEnum;
    aiMaxTradeVersionLevel: FeedAIMaxEduLevel;
    autoIdeaSwitch: boolean;
    useLiftBudget: boolean;
    smartControl: SmartControlEnum;
    lift: object;
}) {
    return liftSwitch
        || (ocpcBidRatioType && ocpcBidRatioType !== FeedOcpcBidRatioTypeEnum.noUse)
        || autoIdeaSwitch
        || (useLiftBudget && lift.scheduleModel !== ScheduleModel.JUST)
        || aiMaxTradeVersionLevel
        || smartControl;
}

export const NO_LIMIT_BUDGET = 0;


interface BudgetLiftFields {
    // AiMax
    aiLift: number;
    aiLiftModel: AILiftModel;
    lift: LiftOption;
    useLiftBudget: boolean;
    useImmediateLiftBudget: boolean;
    liftStatus: FeedLiftBudgetStatus;
}

export function formatBudgetLiftToApi(fields: BudgetLiftFields, initialData: any) {
    // AiMax
    const {
        aiLift,
        aiLiftModel,
        useImmediateLiftBudget,
        liftStatus,
        lift: formLift,
    } = fields;
    const useLiftBudget = fields.useLiftBudget ? FeedUseLiftBudget.ON : FeedUseLiftBudget.OFF;
    const startLiftStatus = [
        FeedLiftBudgetStatus.FINISHED,
        FeedLiftBudgetStatus.RESERVATION,
        FeedLiftBudgetStatus.RUNNING,
    ];
    const lift = merge({}, formLift, {
        eventWeek: formLift.eventWeek?.join(','),
    });


    if (useImmediateLiftBudget) {
        return {
            useLiftBudget: FeedUseLiftBudget.ON,
            lift: {
                scheduleModel: ScheduleModel.JUST,
                liftBudget: lift.liftBudget,
            },
        };
    }
    // 启用智能起量
    if (aiLift && useLiftBudget === FeedUseLiftBudget.ON) {
        return {
            aiLift,
            aiLiftModel,
        };
    }
    // 普通一键起量
    if (!aiLift && useLiftBudget === FeedUseLiftBudget.ON) {
        if (useImmediateLiftBudget) {
            lift.scheduleModel = ScheduleModel.JUST;
        }
        switch (lift.scheduleModel) {
            // 指定时间起量
            case ScheduleModel.SET: {
                const [startTime, startTime1, startTime2, startTime3] = lift.startTimeList.filter(time => !!time);
                return {
                    useLiftBudget,
                    aiLift,
                    liftStatus,
                    lift: {
                        scheduleModel: lift.scheduleModel,
                        liftBudget: lift.liftBudget,
                        startTime,
                        ...(isFeedAiLiftMoreStartTimeUser() ? {startTime1, startTime2, startTime3} : {}),
                    },
                };
            }
            // 循环起量
            case ScheduleModel.REPEAT:
                return {
                    useLiftBudget,
                    aiLift,
                    liftStatus,
                    lift: {
                        scheduleModel: lift.scheduleModel,
                        liftBudget: lift.liftBudget,
                        eventWeek: lift.eventWeek,
                        eventHour: lift.eventHour,
                    },
                };
        }
    }

    if (useLiftBudget === FeedUseLiftBudget.OFF) {
        if (initialData.aiLift) {
            // 关闭智能起量
            return {
                aiLift: 0,
            };
        }
        else if (lift.scheduleModel === ScheduleModel.JUST) {
            // 立即手动起量无法关闭
            return {};
        }
        else if (startLiftStatus.includes(liftStatus)) {
            // 关闭一键起量，起量中的时候也支持关闭
            return {
                useLiftBudget,
            };
        }
    }

    return {};
}

export const PRODUCT_LIB_KEYS = [
    'productType', 'catalogId', 'catalogSource', 'bmcUserId', 'catalogName',
    'productPlacementType', 'virtualProductSet',
];


export function getProjectProductInfo(
    projectData?: Pick<
        Partial<FeedProjectResp>,
        'productType' | 'catalogId' | 'catalogName' | 'catalogSource' | 'bmcUserId' | 'productIds'
        | 'productPlacementType' | 'virtualProductSet'
    >
) {
    return {
        ...pick(projectData, PRODUCT_LIB_KEYS),
        isRecommend: false,
        catalogId: projectData?.catalogId || '',
        productId: projectData?.productIds ? Number(projectData?.productIds) : undefined,
    };
}

export type FeedProjectProductFormData = ReturnType<typeof getProjectProductInfo>;

// 后端字段结构 -> 前端字段结构的转换
export function formatApi2FeedProjectData({feedProjectType, isPreUseProjectBudget}: {
    feedProjectType: FeedProjectResp & LiftRespType | FeedProjectResp;
    isPreUseProjectBudget?: UseProjectBudgetEnum;
}) {
    const {
        isSalesLeadSubject,
        isAppSubject,
        isAppLinkSubject,
    } = checkFeedSubjectType(feedProjectType.subject);
    const supportLiftSwitch = isSalesLeadSubject || isAppSubject || isAppLinkSubject;
    // 如果是新建，表单初始值赋予通过给projectType设置默认值来实现
    // todo 初始值应该设置哪些，具体到每个字段
    const {
        isCreate,
        projectFeedName,
        projectFeedId,
        subject,
        campaignFeedIds = [],
        simpleType,
        appInfo,
        ocpc,
        saleType,
        appSubType,
        eshopType,
        miniProgramType,
        budget,
        // 智能起量相关字段
        aiLift,
        useLiftBudget,
        aiMaxTradeVersionType: originAIMaxTradeVersionType,
        aiMaxTradeVersionLevel,
        autoIdeaSwitch,
        reopenAutoIdeaSwitch,
        lift: originFeedAiLift,
        liftStatus = FeedLiftBudgetStatus.NOT_STARTED,
        aiLiftModel = AILiftModel.aggressive,
        aiTag,
        adSource,
        liftSwitch = isBJHShortPlayAIMaxUser() ? false : (isSoftLinkFeedProjectUser() && supportLiftSwitch),
        aixCampaignItems = [],
        bmcUserId,
        bjhType,
        catalogSource,
        smartControl: smartControlValue = SmartControlEnum.noop,
        bidMode = BidModeEnum.TRANS_COST,
        useProductNew = false,
        autoIdeaSwitchOpenTime,
    } = feedProjectType;
    const {tradeId2nd} = globalData.get('feedBasicData') || {};
    // 行业版的智能基建调优和通用版的后端复用,这边需要做个转化
    const smartControl = (
        subject === FeedSubjectType.bjh && aiMaxTradeVersionLevel
    ) ? SmartControlEnum.noop : isCreate && spyxTrade.includes(+tradeId2nd) && isFeedSpyxTrade()
            ? SmartControlEnum.aggressive : smartControlValue;
    const {
        ocpcBid,
        transType,
        transTypePreview,
        transTypeAttribute,
        transFrom,
        appTransId,
        useRoi,
        roiType = UsePublicEnum.FALSE,
        enableOptimizeDeepTransForBjh, // 专门为初始化百家号简化链路准备的
        roiRatio,
        deepOcpcBid,
        deepTransType,
        isOptimizeDeepTrans,
        coefficient,
        transName: initialTransName,
        deepStayDays: initialDeepStayDays,
        ...restOcpc // ocpc在提交的时候需要整个对象提交，所以这里需要先保存一份
    } = ocpc || {};

    const {
        ocpcBidRatioType,
        ageRangeConfig,
        crowdCoefConfigs,
        cycOcpcBidRatio,
        regionConfig,
        ocpcBidRatioSubType,
    } = formatOcpcBidRatioResponse(coefficient || {});

    const [isUseProjectBudget, projectBudget] = getProjectBudgetParams(feedProjectType, isPreUseProjectBudget);

    const {
        startTime = dayjs().startOf('hour').add(1, 'hour').format('YYYY-MM-DD HH:mm:ss'),
        startTime1,
        startTime2,
        startTime3,
        eventWeek,
    } = originFeedAiLift || {};

    const feedLift = merge({
        scheduleModel: undefined,
        eventHour: '00:00',
        eventWeek: undefined,
        liftBudget: undefined,
    }, originFeedAiLift, {
        eventWeek: eventWeek?.split(',').map(numStr => +numStr),
        startTimeList: (
            isFeedAiLiftMoreStartTimeUser()
                ? [startTime, startTime1, startTime2, startTime3]
                : [startTime]
        ).filter(item => !!item),
    }) as LiftOption;

    const useImmediateLiftBudget = feedLift.scheduleModel === ScheduleModel.JUST;
    // 自定义一键起量配置
    const customLiftBudget = feedLift.scheduleModel === ScheduleModel.SET
        || feedLift.scheduleModel === ScheduleModel.REPEAT;
    const commonLiftStatus = liftStatus !== FeedLiftBudgetStatus.NOT_STARTED
        || liftStatus === FeedLiftBudgetStatus.RESERVATION;
    const feedUseLiftBudget = (typeof useLiftBudget !== 'undefined' && useLiftBudget !== FeedUseLiftBudget.NO_USE)
        ? (useLiftBudget === UseLiftBudgetOPS.Enable && !useImmediateLiftBudget)
        : (!!aiLift || (customLiftBudget && commonLiftStatus));
    const refinedBidSwitch = ocpcBidRatioType && ocpcBidRatioType !== FeedOcpcBidRatioTypeEnum.noUse;
    let aiMaxTradeVersionType = originAIMaxTradeVersionType;
    if (!isBJHShortPlayAIMaxUser()) {
        // 名单内的新建项目时默认不打开任何aiMax的功能
        if (!projectFeedId && getEducationIndustry()) {
            // 新建时默认打开教育行业版本
            aiMaxTradeVersionType = FeedAIMaxTradeVersionType.EDU;
        } else if (getEducationIndustry()
            // aimax通用版未启用任何选项，默认开启教育行业版
            && !useLiftBudget
            && !liftSwitch
            && !refinedBidSwitch
            && !autoIdeaSwitch
        ) {
            aiMaxTradeVersionType = FeedAIMaxTradeVersionType.EDU;
        } else if (!projectFeedId && getMedecalIndustry()) {
            // 新建时默认打开大健康行业版本
            aiMaxTradeVersionType = FeedAIMaxTradeVersionType.MED;
        } else if (getMedecalIndustry()
            // aimax通用版未启用任何选项，默认开启大健康行业版
            && !useLiftBudget
            && !liftSwitch
            && !refinedBidSwitch
            && !autoIdeaSwitch
        ) {
            aiMaxTradeVersionType = FeedAIMaxTradeVersionType.MED;
        }
    }
    const product = getProjectProductInfo(feedProjectType);
    const deepOptType = formatApi2DeepOptType({useRoi, roiType, enableOptimizeDeepTransForBjh});

    const formatData = {
        projectFeedId,
        projectFeedName: projectFeedName || getDefaultSubjectName(BsType.na, subject), // 项目没有商品目录，默认na
        bidMode,
        budget: projectBudget,
        isUseProjectBudget,
        adSource,
        subject,
        simpleType,
        campaignFeedIds,
        appInfo,
        saleType,
        appSubType,
        eshopType,
        ocpcBid,
        transType,
        transTypePreview: transTypePreview || transType,
        transTypeAttribute,
        transFrom,
        deepTransFrom: undefined,
        appTransId,
        useRoi,
        roiType,
        deepOptType,
        enableOptimizeDeepTrans: Number(!!deepOptType),
        roiRatio,
        deepOcpcBid,
        deepTransType,
        isOptimizeDeepTrans,
        miniProgramType,
        deepStayDays: initialDeepStayDays,
        initialDeepStayDays,
        aiLift,
        aiLiftModel,
        lift: feedLift,
        useLiftBudget: feedUseLiftBudget,
        liftStatus,
        useImmediateLiftBudget,
        recommendCategory: undefined,
        // projectModeType这个值用来判断投放模式的值，就是aimax是否开启了
        projectModeType: isAiAgentOpen({
            liftSwitch,
            ocpcBidRatioType,
            aiMaxTradeVersionLevel,
            autoIdeaSwitch,
            useLiftBudget: feedUseLiftBudget,
            smartControl,
            lift: feedLift,
        }) ? feedProjectModeType.AIMAX : feedProjectModeType.CUSTOM,
        aiTag,
        selectedTransAssets: {} as FeedTransAsset, // 先初始化一个空值，在组件里请求吧，避免影响打开的性能
        retentionRate: undefined, // 初始化一个空值，避免TS报错
        aiMax: undefined, // 初始化一个空值，避免TS报错
        // 如果是编辑，直接拿项目数据判断，如果是新建就是默认名单内打开的
        liftSwitch,
        autoIdeaSwitch,
        reopenAutoIdeaSwitch: reopenAutoIdeaSwitch || ReopenAutoIdeaSwitchEnum.OFF,
        refinedBidSwitch,
        ocpcBidRatioMode: ocpcBidRatioModeRadioMap.customRules,
        ocpcBidRatioType,
        ocpcBidRatioSubType,
        customAgeRangeConfig: ageRangeConfig,
        specifiedAgeRangeConfig: ageRangeConfig,
        crowdCoefConfigs,
        cycOcpcBidRatio,
        regionConfig,
        aixCampaignItems,
        submitLoading: false,
        initialTransName,
        restOcpc,
        product, // 虚拟增加的参数，聚合产品库以及产品信息放到表单上，方便页面上使用
        productLib: omit(product, ['productId']), // 单独给产品库项使用的
        useProduct: !!product.catalogId || useProductNew,
        aiMaxTradeVersionType,
        aiMaxTradeVersionLevel,
        smartControlSwitch: !!smartControl,
        smartControl,
        bjhType,
        campaignItems: [], // 取消绑定在项目上的方案是，编辑方案的预算，需要给后端传这个字段
        autoIdeaSwitchOpenTime,
        trigerAutoIdeaSwitch: false, // 表单上自动idea开关的打开时间
    };

    return {
        // !不要加额外的field了，直接在formData里定义。formData里不要使用拓展对象，需要定义field
        initialData: formatData,
        ...formatData,
        // !不要加额外的field了，直接在formData里定义。formData里不要使用拓展对象，需要定义field
    };
}

// * 初始化时 (isPreUseProjectBudget 为空）
// 新建的时候默认使用项目预算，并且使用不限
// ! 百家号不支持项目预算，需要设置为空
// 如果是编辑， 得有预算才打开
// * 修改时 (isPreUseProjectBudget 有值）
// 如果之前有值，就用之前的值
// 不传budget就是没使用共享预算, budget = 0 不限，budget = 具体数字 自定义
// 绑定了轻舸方案的项目不能使用项目预算
function getProjectBudgetParams(projectFormData: FeedProjectResp, isPreUseProjectBudget: any): [any, any] {
    const {subject, budget, projectFeedId, aixCampaignItems = []} = projectFormData;
    const {isProgramSubject, isEBusinessSubject, isBjhSubject} = checkFeedSubjectType(subject);
    if ((isBjhSubject && !isBJHShortPlayAIMaxUser()) || isProgramSubject || isEBusinessSubject) {
        return [UseProjectBudgetEnum.OFF, undefined];
    }

    return isPreUseProjectBudget !== undefined
        ? [
            isPreUseProjectBudget,
            budget,
        ] : (
            ((budget !== undefined || (!projectFeedId && !aixCampaignItems.length)) && isFeedShareBudgetUser())
                ? [UseProjectBudgetEnum.ON, projectFeedId ? budget : NO_LIMIT_BUDGET]
                : [UseProjectBudgetEnum.OFF, undefined]
        );
}

// 前端字段结构 -> 后端字段结构的转换
export function formatFeedProjectData2Api({formData}: {formData: FeedProjectFormData}): FeedProjectParams {

    const {
        projectFeedName,
        projectFeedId,
        subject,
        simpleType,
        bidMode,
        campaignFeedIds,
        appInfo,
        saleType,
        appSubType,
        eshopType,
        miniProgramType,
        deepStayDays,
        aiTag,

        // ocpc
        ocpcBid,
        transType,
        transTypeAttribute,
        transFrom: transFrom_,
        appTransId,
        isOptimizeDeepTrans, // 这里暂时先不用了，因为页面上没有这个字段
        deepOcpcBid,
        deepTransType,
        // useRoi, 融合为deepOptType
        roiRatio,
        // 暂时仅用来iaa浅层roi传递roiType参数，深层roiType通过deepOptType计算获得
        roiType: shallowRoiType,
        deepOptType,
        // AiMax
        liftSwitch,
        autoIdeaSwitch, // 自动创意开关
        reopenAutoIdeaSwitch, // 再次开启方案&单元自动创意能力
        aiLift,
        aiLiftModel,
        lift,
        useLiftBudget,
        useImmediateLiftBudget,
        liftStatus,
        recommendCategory,
        aiMaxTradeVersionType,
        aiMaxTradeVersionLevel,
        ocpcBidRatioType,
        ocpcBidRatioSubType,
        customAgeRangeConfig,
        specifiedAgeRangeConfig,
        crowdCoefConfigs,
        cycOcpcBidRatio,
        regionConfig,
        selectedTransAssets,
        aixCampaignItems,
        adSource,
        restOcpc,
        product,
        budget,
        isUseProjectBudget,
        initialData,
        smartControl,
        bjhType,
        deepTransFrom,
        campaignItems = [], // 取消绑定在项目上的方案是，编辑方案的预算，需要给后端传这个字段
    } = formData;

    const budgetFields = formatBudgetLiftToApi({
        aiLift,
        aiLiftModel,
        lift,
        useLiftBudget,
        useImmediateLiftBudget,
        liftStatus,
    }, initialData);

    const autoIdeaSwitchOpenTime = getAutoIdeaSwitchOpenTime(formData);
    const isShowPayCount = getPayCountShowStatus(formData);
    const {useRoi, roiType} = formatDeepOptType2Api(deepOptType);
    const isSupportMultiProducts = checkIsSupportMultiProducts({
        subject, catalogId: product?.catalogId, catalogSource: product.catalogSource, appSubType,
    });
    const isUseMultiProducts = isSupportMultiProducts && checkIsMultiProducts({subject, ...product});
    const isUseSingleProduct = !isUseMultiProducts && product?.productId;
    const transFrom = getTransFrom({transFrom: transFrom_, deepTransFrom});

    return {
        projectFeedName,
        projectFeedId,
        bidMode,
        subject,
        simpleType: checkIsBjhShortPlaySimplifiedScene({subject, bjhType, projectFeedId, simpleType}) ? 1 : 0,
        campaignFeedIds,
        appInfo: subject === FeedSubjectType.hmos ? pick(appInfo, ['appUrl']) : appInfo,
        saleType,
        appSubType,
        eshopType,
        miniProgramType,
        aiTag,
        mandatoryOperation: 1, // 跳过预算校验，写死就行了
        adSource,
        liftSwitch: liftSwitch ? LiftSwitchEnum.ON : LiftSwitchEnum.OFF,
        autoIdeaSwitch: autoIdeaSwitch ? AutoIdeaSwitchEnum.ON : AutoIdeaSwitchEnum.OFF,
        reopenAutoIdeaSwitch,
        ...budgetFields,
        // !TODO 将来可能
        aiMaxTradeVersionType: !aiMaxTradeVersionLevel
            ? FeedAIMaxTradeVersionType.OFF
            : FeedAIMaxTradeVersionTypeConfig[getFeedAIMaxIndustry({
                subject,
                bjhType,
                bidMode,
            })],
        aiMaxTradeVersionLevel: aiMaxTradeVersionLevel,
        ocpc: {
            ...restOcpc,
            ...(isBjhRoi(formData) ? {transType} : {
                // 百家号ROi没有出价相关字段，只有roiRatio
                ocpcBid,
                transType,
                transFrom, // 信息流暂时只会有一个
                appTransId,
                transTypeName: transNameMapByValue[transType],
            }),
            ...(isShowPayCount ? {transTypeAttribute: transTypeAttribute || TransTypeAttribute.NUMBER_OF_PEOPLE} : {}),
            // 这里暂时先不用了，因为页面上没有这个字段，先用deepTransType和useRoi代替
            isOptimizeDeepTrans: deepOptType !== DEEP_OPT_TYPE.USE_ROI
                && deepOptType !== DEEP_OPT_TYPE.USE_ROI_TYPE
                && !!deepTransType,
            deepOcpcBid,
            deepTransType,
            useRoi,
            roiType: isBjhRoi(formData) ? shallowRoiType : roiType,
            roiRatio,
            isNewVersion: selectedTransAssets?.isNewVersion,
            deepTransTypeName: transNameMapByValue[deepTransType],
            deepStayDays,
            coefficient: formateRequestParamsForm({
                ocpcBidRatioType,
                ocpcBidRatioSubType,
                ageRangeConfig: ocpcBidRatioSubType === ageTypeMap.specify
                    ? specifiedAgeRangeConfig
                    : customAgeRangeConfig,
                regionConfig,
                crowdCoefConfigs,
                cycOcpcBidRatio,
            }),
        },
        aixCampaignItems,
        ...pick(product, [
            'productType', 'bmcUserId', 'catalogId', 'catalogSource', 'catalogName', 'structuredProductIds'
        ]),
        ...(isUseMultiProducts ? pick(product, ['productPlacementType', 'virtualProductSet']) : {}),
        ...(
            isUseSingleProduct
                ? {productIds: String(product.productId)}
                : {}
        ),
        ...(isFeedBJHNew() ? {bjhType} : {}),
        ...(
            isUseProjectBudget === UseProjectBudgetEnum.ON
                ? {budget}
                : (projectFeedId ? {budgetOptType: 1} : {})
        ), // budgetOptType: 1, 表示删除项目预算
        // 通用版智能基建和短剧行业版的名单分开
        ...(
            isFeedSmartControl() ? {
                smartControl: smartControl,
            } : {}
        ),
        ...(
            subject === FeedSubjectType.bjh && aiMaxTradeVersionLevel
                ? {smartControl: SmartControlEnum.aggressive}
                : {}
        ),
        // 采纳了推荐关联产品时添加这个标记
        ...(recommendCategory && product?.isRecommend ? {acceptSource: 1} : {}),
        campaignItems,
        autoIdeaSwitchOpenTime,
    };
}

export function getTransFrom({transFrom, deepTransFrom}: any) {
    if (isMultiTransFrom(transFrom, deepTransFrom)) {
        return TRANS_FROM_ENUM.MULTI;
    }
    return transFrom;
}

export type FeedProjectApiData = ReturnType<typeof formatFeedProjectData2Api>;

// 目前只校验了 目标转化成本、 深度转化成本、和 预算， 都是一样的所以这里没有配置
const API_ERROR_FIELD_MAP: {
    [key: string]: keyof FeedProjectFormData;
} = {

};
// 后端报错字段 -> 前端报错字段的转换
export function formatApi2FeedProjectErrorField(field: string) {
    return API_ERROR_FIELD_MAP[field] || field;
}


export function getIsShowDeepOptions(formData: FeedProjectFormData) {

    // 编辑时由于无法修改转化目标，所以不显示深度转化类型
    if (formData.projectFeedId && !formData.deepTransType) {
        return false;
    }

    // 应用调起直播推广（直播间服务购买成功（CT131））不支持深度转化
    if (checkFeedSubjectType(formData.subject).isAppLinkSubject
        && formData.saleType === SaleType.live
    ) {
        return false;
    }

    const deppOptions = formData.selectedTransAssets?.transTypes?.find(
        i => i.transType === formData.transType
    )?.deepTransTypes || [];
    return !!deppOptions.length;
}

function getAutoIdeaSwitchOpenTime(formData: FeedProjectFormData) {
    const {trigerAutoIdeaSwitch, autoIdeaSwitch, reopenAutoIdeaSwitch} = formData;
    if (location.href.includes('createProject') && autoIdeaSwitch === AutoIdeaSwitchEnum.ON) {
        return dayjs().format('YYYY-MM-DD HH:mm:ss');
    }
    if (trigerAutoIdeaSwitch
        && autoIdeaSwitch === AutoIdeaSwitchEnum.ON
        && reopenAutoIdeaSwitch === ReopenAutoIdeaSwitchEnum.ON
    ) {
        return dayjs().format('YYYY-MM-DD HH:mm:ss');
    }
}

const charPool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
const randomString = (len: number) => {
    let result = '';
    for (let i = 0; i < len; i++) {
        const idx = parseInt(`${Math.random() * charPool.length}`, 10);
        result += charPool[idx];
    }
    return result;
};

// 默认计划名称：营销目标_计划_日期_时间
export const getDefaultName = (prefix = '营销目标_计划') => {
    const date = new Date();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    return `${prefix}${randomString(3)}_${month}_${day}_${hour}:${minute}`;
};


export function getDefaultSubjectName(bstype: BsType, subject: FeedSubjectType) {
    const subjectName = bstype === BsType.pa
        ? BS_TYPE_DICT[bstype]
        : marketTargetNameMapByValue[subject as keyof typeof marketTargetNameMapByValue].slice(0, 5);
    return getDefaultName(`${subjectName}_智投项目`);
}

export function watchProduct<T extends keyof FeedProjectFormData['product']>(
    value: FeedProjectFormData['product'][T],
    formData: Partial<FeedProjectFormData>
) {
    formData.campaignFeedIds = []; // 修改产品后清空方案
}

function formatApi2DeepOptType(params) {
    const {useRoi, roiType, enableOptimizeDeepTransForBjh} = params;
    if (useRoi) {
        return DEEP_OPT_TYPE.USE_ROI;
    }
    if (roiType || enableOptimizeDeepTransForBjh) {
        return DEEP_OPT_TYPE.USE_ROI_TYPE;
    }
}
function formatDeepOptType2Api(deepOptType) {
    switch (deepOptType) {
        case DEEP_OPT_TYPE.USE_ROI:
            return {useRoi: true, roiType: 0};
        case DEEP_OPT_TYPE.USE_ROI_TYPE:
            return {roiType: 1, useRoi: false};
    }
    return {};
}
