/**
 * @file 移动端落地页-DropdownUrlSelect（可以支持直接调起选取）
 * <AUTHOR>
 * @date 2025-05-21
*/
import classNames from 'classnames';
import {useCallback, useState, useMemo} from 'react';
import {useResource, CacheProvider, Boundary} from 'react-suspense-boundary';
import {Dropdown, Input, Button, SearchBox, Menu, Loading, Tooltip} from '@baidu/one-ui';
import {useSwitch} from '@huse/boolean';
import {useDebouncedValue} from 'huse';
import {IconChevronDown} from 'dls-icons-react';
import {showTypeKeyMap} from 'commonLibs/components/jimuyuSelect/config';
import {getAllPageList} from '@/api/fcThreeLevel/getPageList';
import './index.global.less';

// 可选落地页URL列表
function UrlList(props) {
    const {
        marketingTargetId,
        searchValue,
        showType,
        onChange,
        hide,
    } = props;

    const [[jmyPageList, agentList], {pending}] = useResource(getAllPageList, useDebouncedValue({
        marketingTargetId,
        showType: showTypeKeyMap[showType],
        filter: {
            value: searchValue,
            type: 'pageName',
        },
    }, 500));

    const pageList = useMemo(() => [...jmyPageList, ...agentList], [
        jmyPageList,
        agentList,
    ]);

    const handleMenuClick = e => {
        const pageId = e.key;
        const pageInfo = pageList.find(item => String(item.pageId) === pageId);
        const url = pageInfo?.onlineUrl;
        onChange(url);
        hide();
    };

    const menuProps = {
        className: 'url-select-overlay-page-list-menu',
        type: 'basic',
        onClick: handleMenuClick,
    };

    return (
        <div className="url-select-overlay-page-list-container">
            {
                pageList.length > 0 ? (
                    <Menu {...menuProps}>
                        {
                            pageList.map(item => {
                                return (
                                    <Menu.Item
                                        className="url-select-overlay-page-list-menu-item"
                                        key={item.pageId}
                                    >
                                        <Tooltip placement="top" title={item.pageName}>
                                            <span className="landpage-site-name">{item.pageName}</span>
                                        </Tooltip>
                                    </Menu.Item>
                                );
                            })
                        }
                    </Menu>
                ) : (
                    <div className="url-select-overlay-page-list-empty">
                        无匹配结果
                    </div>
                )
            }
        </div>
    );
}

// 点击【选择已有】展示下拉Overlay: DropdownUrlSelect
function UrlSelectOverlay(props) {
    const [searchValue, setSearchValue] = useState('');
    const searchboxProps = {
        className: 'url-select-overlay-operation-search-box',
        placeholder: '请输入搜索值',
        value: searchValue,
        onChange: e => {
            setSearchValue(e.target.value);
        },
        onClearClick: () => {
            setSearchValue('');
        },
    };


    return (
        <div className="url-select-overlay-container">
            <div className="url-select-overlay-operation-container">
                <SearchBox {...searchboxProps} />
            </div>
            <div className="url-select-overlay-content-container">
                <CacheProvider>
                    <Boundary pendingFallback={<Loading tip="加载中..." className="loading" />}>
                        <UrlList {...props} searchValue={searchValue} />
                    </Boundary>
                </CacheProvider>
            </div>
        </div>
    );
}

function DefaultDropdownBtn(props) {
    const {visible} = props;
    return (
        <Button
            {...props}
            className="url-select-btn"
            type="basic"
        >
            选择已有
            <IconChevronDown className={`url-select-btn-down-icon ${visible
                ? 'url-select-btn-down-icon-open'
                : ''}`}
            />
        </Button>
    );
}

// 自定义Dropdown触发按钮
export function CustomDropdownUrlSelect(props) {
    const {
        disabled,
        DropdownBtn = DefaultDropdownBtn,
    } = props;

    const [visible, , hide, toggle] = useSwitch(false);

    const overLayProps = {
        hide,
        ...props,
    };

    return (
        <Dropdown
            visible={visible}
            disabled={disabled}
            overlay={<UrlSelectOverlay {...overLayProps} />}
            trigger={['click']}
            placement="bottomRight"
            transparent={false}
            onVisibleChange={toggle}
            getPopupContainer={() => document.body}
            className="finial-url-dropdown"
        >
            <DropdownBtn {...props} />
        </Dropdown>
    );
}

// 落地页URL输入框 + DropdownUrlSelect
export default function FinalUrl(props) {
    const {
        value,
        onChange,
        className,
        placeholder,
        disabled,
        width,
        containerClassName = 'final-url-editor',
        inputClassName = 'final-url-editor-input',
        hasCustomDropdownBtn = false,
    } = props;

    const inputProps = {
        value,
        onChange: e => onChange(e.value),
        className: classNames(className, inputClassName),
        placeholder,
        disabled,
        width,
        showErrorMessage: false,
    };

    return (
        <div className={containerClassName}>
            <Input {...inputProps} />
            {
                hasCustomDropdownBtn ? (
                    <CustomDropdownUrlSelect
                        {...props}
                        DropdownBtn={(props: object) => (
                            <Button
                                {...props}
                                type="text-strong"
                                className="qg-form-input-text-btn"
                            >选择
                            </Button>
                        )}
                    />
                ) : <CustomDropdownUrlSelect {...props} />
            }
        </div>
    );
}
