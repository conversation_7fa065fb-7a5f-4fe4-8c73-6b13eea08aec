.final-url-editor {
    display: flex;
    align-items: center;

    .final-url-editor-input {
        margin-right: 4px;
        width: 280px;

        input {
            width: 280px;
        }
    }
}

.url-select {
    &-btn {
        position: relative;
        padding: 0 8px;

        &-down-icon {
            transition: transform 0.3s ease-in-out;
            transform: rotate(0deg);
            margin-left: 4px;
            font-size: 16px;

            &-open {
                transform: rotate(180deg);
            }
        }
    }

    &-overlay {
        &-container {
            width: 380px;
        }

        &-operation {
            &-search-box {
                margin: 0 10px;
                width: 100%;
            }

            &-container {
                display: flex;
            }
        }

        &-content-container {
            position: relative;
            max-height: 160px;
            overflow: auto;
        }

        &-page-list {
            &-menu {
                width: 100%;

                &-item {
                    & > span {
                        width: 100%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }

            &-container {
                .landPage-item {
                    display: flex;
                    justify-content: space-between;

                    .landpage-site-name {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        margin-right: 8px;
                    }
                }

                .highlight-search {
                    color: #ff9100;
                }

                .no-decoration-link:hover {
                    text-decoration: none;
                }
            }

            &-empty {
                font-size: 14px;
                color: #666;
                padding: 8px 16px;
            }
        }
    }
}

.finial-url-dropdown {
    .loading {
        width: 380px;
        margin: 14px 0;
    }
}
