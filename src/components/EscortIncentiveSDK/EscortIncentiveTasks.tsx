/* eslint-disable complexity */
/* eslint-disable max-len */
import {useEffect, useMemo} from 'react';
import classNames from 'classnames';
import {useRequest, useActionPending} from 'huse';
import {Boundary} from 'react-suspense-boundary';
import {But<PERSON>, Dialog, Toast, ConfigProvider} from '@baidu/one-ui';
import {Typography, Skeleton, Tooltip, Popover} from '@baidu/light-ai-react';
import {IconQuestionCircleSolid, IconChevronRight} from 'dls-icons-react';
import {useSlot} from '@/hooks/slot';
import {useControl} from '@/hooks/externalControl';
import {useLocalStorage} from '@/hooks/storage';
import {AixProgress} from '@/components/common/progress';
// import {useGlobalProductContext} from '@/hooks/productLine'; // product接受外部props传入，不直接依赖productLine
import {onAddTask, fetchTaskInfo, isKaUser} from './api/tasks';
import {fetchCoinsInfo, onExchangeCoupon} from './api/coins';
import {TaskStatusEnum, TaskIdEnum, LogActionType, AppIdEnum, PLATFORM_ENUM} from './config';
import {LocalTaskConfig} from './config/LocalTaskConfiguration';
import {getInvertTime, coinsToMoney} from './utils';
import {TaskBasicInfo} from './interface/task';
import HistoryTasks_ from './components/HistoryTasks';
import {TaskDetailDrawer as TaskDetailDrawer_} from './components/TaskDetail/Drawer';
import {TaskUnacceptDetailDrawer as TaskUnacceptDetailDrawer_} from './components/unacceptTaskDetail/drawer';
import {CoinsProgress} from './components/Coins';
import {ActivityRule} from './components/activityRule';
import {showGuideDialog, showRebuildGuideDialog, showOldBringNewGuideDialog} from './components/GuideDialog';
import {useEscortIncentiveConfig} from './context';
import './EscortIncentiveTasks.global.less';

export function BanfeiTaskCenter({className = '', setBanfeiTaskData, product}: {
    className?: string;
    setBanfeiTaskData?: (data: any) => void;
    product: PLATFORM_ENUM;
}) {
    const {log, userId, appId} = useEscortIncentiveConfig();

    const {data: coinsInfo, refresh: refreshCoins} = useRequest(fetchCoinsInfo, {});

    const {data: tasks = [], pending, refresh: refreshTasks} = useRequest(fetchTaskInfo, {isHistory: false});
    const tasksCount = tasks.length;

    // product：FC、FEED，暂时不写死，直接删掉弹窗代码
    // const {product} = useGlobalProductContext();

    const onAcceptTask_ = async (taskId: TaskIdEnum) => {
        try {
            await onAddTask({taskId});
            log(LogActionType.ACCEPT_TASK_SUCCESS, {taskId});
            refreshTasks();
        }
        catch (e) {
            log(LogActionType.ACCEPT_TASK_FAIL, {taskId});
            throw e;
        }
    };

    const [TaskDetailDrawer, {openTaskDetailDrawer}] = useControl(TaskDetailDrawer_);
    const [TaskUnacceptDetailDrawer, {openUnacceptTaskDetailDrawer}] = useControl(TaskUnacceptDetailDrawer_);
    const [HistoryTasks, {openHistoryTasks}] = useControl(HistoryTasks_);

    const guideDialogKeyPath: [string, string, string] = useMemo(
        () => ['banfei', `guide_dialog_${userId}_III`, 'show'],
        [userId]
    );
    const rebuildGuideDialogKeyPath: [string, string, string] = useMemo(
        () => ['banfei', `guide_dialog_${userId}_rebuildII`, 'show'],
        [userId]
    );
    const oldBringNewGuideDialogKeyPath: [string, string, string] = useMemo(
        () => ['banfei', `old_bring_new_dialog_${userId}`, 'show'],
        [userId]
    );
    const [hasGuideDialog, setHasShowGuideDialog] = useLocalStorage(guideDialogKeyPath, false);
    const [hasShowRebuildGuideDialog, setHasShowRebuildGuideDialog] = useLocalStorage(rebuildGuideDialogKeyPath, false);
    const [hasShowOldBringNewGuideDialog, setHasShowOldBringNewGuideDialog] = useLocalStorage(oldBringNewGuideDialogKeyPath, false);

    const onOpenHistoryTasks = () => {
        log(LogActionType.OPEN_HISTORY_TASKS);
        openHistoryTasks();
    };

    const openTaskRule = () => {
        log(LogActionType.OPEN_TASK_RULES);
        Dialog.alert({
            title: '任务中心通用规则',
            content: <ActivityRule />,
            width: 1000,
            okText: '知道了',
            needCloseIcon: true,
        });
    };

    useEffect(
        () => {
            if (tasksCount && setBanfeiTaskData) {
                setBanfeiTaskData(tasks);
            }
        },
        [tasksCount]
    );

    useEffect(
        () => {
            log(LogActionType.VIEW_TASK_ENTRY);
        },
        []
    );


    useEffect(() => {
        // 针对PC未领取任务，展示引导弹窗
        const unacceptedPcTask = tasks.find(task => task.taskId === TaskIdEnum.ExpansionPC3
            && task.status === TaskStatusEnum.unAccepted);
        const now = new Date();

        const isAppSupport = (
            appId === AppIdEnum.qingge
            && location.pathname === '/ad/overview'
            && now < new Date('2025-09-30T00:00:00')
            && product !== PLATFORM_ENUM.FEED
        );

        if (!hasGuideDialog
            && isAppSupport
            && unacceptedPcTask
        ) {
            showGuideDialog({
                confirm: {
                    onOk: async () => {
                        setHasShowGuideDialog(true);
                        try {
                            await onAcceptTask_(TaskIdEnum.ExpansionPC3);
                            openTaskDetailDrawer(TaskIdEnum.ExpansionPC3);
                        } catch (e) {
                            // onAcceptTask中已有错误处理
                        }
                    },
                    onClose: () => {
                        setHasShowGuideDialog(true);
                        openUnacceptTaskDetailDrawer(TaskIdEnum.ExpansionPC3);
                    },
                },
                task: unacceptedPcTask,
                log,
            });
        }
    }, [appId, hasGuideDialog, onAcceptTask_, product, tasks]);

    useEffect(() => {
        // 公共条件：仅在轻舸展示
        const isAppSupport =
            appId === AppIdEnum.qingge
            && location.pathname === '/ad/overview'
            && product !== PLATFORM_ENUM.FEED;

        const now = new Date();

        // 优先级 1: 智能翻新未领取任务
        const unacceptedRebuildTask = tasks.find(
            task =>
                task.taskId === TaskIdEnum.UpperQ3AiRebuildCoupons
                && task.status === TaskStatusEnum.unAccepted
        );

        if (
            !hasShowRebuildGuideDialog
            && isAppSupport
            && now < new Date('2025-08-31T00:00:00')
            && unacceptedRebuildTask
        ) {
            showRebuildGuideDialog({
                confirm: {
                    onOk: async () => {
                        setHasShowRebuildGuideDialog(true);
                        try {
                            await onAcceptTask_(
                                TaskIdEnum.UpperQ3AiRebuildCoupons
                            );
                        } catch (e) {
                            // onAcceptTask中已有错误处理
                        }
                    },
                    onClose: () => {
                        setHasShowRebuildGuideDialog(true);
                    },
                },
                task: unacceptedRebuildTask,
                log,
            });
            return; // 阻止执行优先级 2
        }

        // 优先级 2: 老带新未领取任务
        const unacceptedOldBringNewTask = tasks.find(
            task =>
                task.taskId === TaskIdEnum.OldBringNew
                && task.status === TaskStatusEnum.unAccepted
        );

        if (
            !hasShowOldBringNewGuideDialog
            && isAppSupport
            && now < new Date('2025-09-30T00:00:00')
            && unacceptedOldBringNewTask
        ) {
            showOldBringNewGuideDialog({
                confirm: {
                    onOk: async () => {
                        setHasShowOldBringNewGuideDialog(true);
                        try {
                            await onAcceptTask_(TaskIdEnum.OldBringNew);
                        } catch (e) {
                            // onAcceptTask中已有错误处理
                        }
                    },
                    onClose: () => {
                        setHasShowOldBringNewGuideDialog(true);
                    },
                },
                task: unacceptedOldBringNewTask,
                log,
            });
        }
    }, [
        appId,
        location.pathname,
        product,
        tasks,
        hasShowRebuildGuideDialog,
        hasShowOldBringNewGuideDialog,
    ]);


    const {almostExpireCoins = 0, totalCoins = 0} = coinsInfo || {};

    const cls = classNames('banfei-task-center-card-container', {[className]: !!className});

    return (
        <Boundary renderError={() => null}>
            <ConfigProvider theme="light-ai">
                <div className={cls}>
                    <div className="banfei-task-center-card-panel">
                        <div className="task-center-title">
                            <div className="main-title">
                                <img src="https://fc-feed.bj.bcebos.com/aix%2FsmallGoldCoin.svg" alt="勋章图标" />
                                任务中心
                            </div>
                            <div className="operator-button">
                                <Button type="text-strong" onClick={openTaskRule}>活动规则</Button>
                                <Button type="text-strong" onClick={onOpenHistoryTasks}>历史任务</Button>
                            </div>
                        </div>
                        <div className="task-center-coin">
                            金币余额{totalCoins}个
                            {almostExpireCoins ? `(${almostExpireCoins}个即将过期)` : ''}
                            <div className="coin-exchange-entry">
                                <ExchangeButton
                                    totalCoins={totalCoins}
                                    disabled={!totalCoins}
                                    afterExchange={refreshCoins}
                                />
                                <Tooltip placement="top-end" content="10金币兑换1元优惠券，金币获得后90天内有效">
                                    <IconQuestionCircleSolid className="question-mark-icon" />
                                </Tooltip>
                            </div>
                        </div>
                        {pending && <Skeleton.Paragraph active rows={3} />}
                        {
                            !pending && (
                                !tasksCount
                                    ? (
                                        <div className="task-center-empty-content">
                                            当前暂无新任务{totalCoins ? '，可去兑换金币~' : '~'}
                                        </div>
                                    )
                                    : tasks.filter(i => LocalTaskConfig[i.taskId]).map(task => {
                                        const {taskId} = task;
                                        if ([TaskStatusEnum.unAccepted, TaskStatusEnum.unIssued].includes(task.status)) {
                                            return (
                                                <UnAcceptedTaskCard
                                                    key={taskId}
                                                    {...task}
                                                    onAcceptTask={onAcceptTask_}
                                                    viewTaskDetail={openTaskDetailDrawer}
                                                    viewUnacceptTaskDetail={openUnacceptTaskDetailDrawer}
                                                />
                                            );
                                        }
                                        return (
                                            <AcceptedTaskCard key={taskId} {...task} viewTaskDetail={openTaskDetailDrawer} />
                                        );
                                    })
                            )
                        }
                    </div>
                    <HistoryTasks />
                    <TaskDetailDrawer />
                    <TaskUnacceptDetailDrawer
                        onAcceptTask={onAcceptTask_}
                    />
                </div>
            </ConfigProvider>
        </Boundary>
    );
}

const popoverStyle = {zIndex: 9999};
interface UnAcceptedTaskCardProps extends TaskBasicInfo {
    onAcceptTask: (taskId: UnAcceptedTaskCardProps['taskId']) => Promise<void>;
    viewTaskDetail: (taskId: UnAcceptedTaskCardProps['taskId']) => void;
    viewUnacceptTaskDetail: (taskId: UnAcceptedTaskCardProps['taskId']) => void;
}
function UnAcceptedTaskCard({
    taskId,
    status,
    rewardsData,
    estimateData,
    rewardLimit,
    competition,
    onAcceptTask: onAcceptTask_,
    viewTaskDetail,
    desc,
    extConfig,
    endTime,
    viewUnacceptTaskDetail,
}: UnAcceptedTaskCardProps) {
    const {log} = useEscortIncentiveConfig();

    useEffect(
        () => {
            log(LogActionType.VIEW_UNACCEPTED_TASK, {taskId});
        },
        [taskId]
    );

    const {title, slots} = LocalTaskConfig[taskId];
    const [onAcceptTask, pendingCount] = useActionPending(onAcceptTask_);
    const Slot = useSlot({
        estimateData, slots, competition, rewardLimit, taskId,
        desc, status, extConfig, endTime, rewardsData,
        pendingCount, onAcceptTask, viewTaskDetail, viewUnacceptTaskDetail,
        log,
    });

    const card = (
        <div className="unaccepted-task-center-content">
            <div className="info">
                <div className="title">
                    {title}
                </div>
                <Slot name="UnacceptedTaskCardInfo">
                    <Slot name="TaskEstimateTipOnCard" />
                    <Slot name="TaskFeaturesOnCard" />
                </Slot>
            </div>
            <Slot name="UnacceptedTaskCardOperation" className="operation" />
        </div>
    );

    return (
        <Popover
            style={popoverStyle}
            placement="top-end"
            content={desc ? <Typography.Markdown>{desc}</Typography.Markdown> : null}
        >
            {card}
        </Popover>
    );
}

interface AcceptedTaskCardProps extends TaskBasicInfo {
    viewTaskDetail: (taskId: AcceptedTaskCardProps['taskId']) => void;
}
const START_PERCENT = 20;
const PERCENT_LENGTH = 80;
function AcceptedTaskCard({
    taskId,
    desc,
    estimateData,
    reportData,
    rewards,
    rewardLimit,
    subTasks,
    userStartTime,
    taskDuration,
    endTime,
    viewTaskDetail,
    indicatorCurrent,
    taskIndicator,
    extConfig,
    rewardType,
    rewardsData,
    status,
}: AcceptedTaskCardProps) {
    const {log} = useEscortIncentiveConfig();

    useEffect(
        () => {
            log(LogActionType.VIEW_ACCEPTED_TASK, {taskId});
        },
        [taskId]
    );

    const {title, slots, viewDetailOnClickCard} = LocalTaskConfig[taskId];

    const toViewTaskDetail = () => {
        log(LogActionType.OPEN_TASK_DETAIL, {taskId});
        viewTaskDetail(taskId);
    };

    const Slot = useSlot({
        slots, reportData, rewards, rewardLimit, desc, estimateData, indicatorCurrent, taskIndicator,
        taskId, userStartTime, taskDuration, title, extConfig, endTime, rewardType, status, rewardsData,
        log, viewTaskDetail, subTasks,
    });

    return (
        <div
            className="accepted-task-center-content"
            onClick={() => viewDetailOnClickCard && toViewTaskDetail()}
        >
            <div className="info">
                <Slot name="AcceptedTaskCardTitle">
                    <div className="title">
                        {title}
                        <IconChevronRight className="arrow-icon" />
                    </div>
                </Slot>
                <Slot name="AcceptedTaskCardContent">
                    {
                        reportData && (
                            <div className="content-desc">
                                <Slot name="ReportDataDesc" />，获得 <CoinsProgress value={rewards} limit={rewardLimit} />
                            </div>
                        )
                    }
                    <TaskCardProgress
                        indicatorCurrent={indicatorCurrent}
                        taskIndicator={taskIndicator}
                        extConfig={extConfig}
                    />
                    <Slot name="TaskDetailProgressIntroduce" />
                </Slot>
                <Slot name="SubTaskProgressOnCard">
                    <div className="sub-task-progress">
                        已完成
                        <span className="highlight">
                            {subTasks.filter(item => item.status === TaskStatusEnum.completed).length}
                            /
                            {subTasks.length}
                        </span>，任务倒计时
                        <span className="highlight">{getInvertTime({userStartTime, taskDuration, endTime})}</span>天
                    </div>
                </Slot>
            </div>
            <Slot name="AcceptedTaskCardOperation" className="operation" />
        </div>
    );
}

interface ExchangeButtonProps {
    disabled?: boolean;
    totalCoins?: number;
    afterExchange?: () => void;
}
function ExchangeButton({disabled = false, totalCoins = 0, afterExchange}: ExchangeButtonProps) {
    const {log, accountLevelId} = useEscortIncentiveConfig();

    const onExchangeCoins = (coins: number) => {
        log(LogActionType.OPEN_EXCHANGE_COIN_DIALOG);
        const money = coinsToMoney(coins);
        Dialog.confirm({
            title: '金币兑换',
            content: `当前金币余额${coins}个，可兑换${isKaUser(accountLevelId) ? '' : '7折'}优惠券${money}元，优惠券兑换后即刻开始使用，有效期30天。`
                + '预计确认兑换后30分钟下发优惠券，可在【历史任务】-【金币记录】中查看优惠券。请确认是否兑换？',
            async onOk() {
                try {
                    await onExchangeCoupon({exchangeCoins: coins});
                    log(LogActionType.EXCHANGE_COIN_SUCCESS);
                    Toast.success({
                        content: `已成功兑换7折优惠券${money}元，可在历史任务中查看金币记录。`,
                        duration: 3,
                    });
                    afterExchange && afterExchange();
                }
                catch (e) {
                    log(LogActionType.EXCHANGE_COIN_FAIL);
                }
            },
            okText: '兑换',
            needCloseIcon: true,
        });
    };

    return (
        <Button
            type="text-strong"
            className={`exchange-button ${disabled ? 'disabled' : ''}`}
            disabled={disabled}
            onClick={() => onExchangeCoins(totalCoins)}
        >
            去兑换
        </Button>
    );
}

function TaskCardProgress({
    indicatorCurrent,
    extConfig,
    taskIndicator,
}: Pick<TaskBasicInfo, 'indicatorCurrent' | 'taskIndicator' | 'extConfig'>) {
    const {stages = [], rewardCurrentStock, rewardStock} = extConfig || {};

    let currentProgressPercent: number;
    if (indicatorCurrent < stages[0].indicator) {
        currentProgressPercent = Math.floor((indicatorCurrent / stages[0].indicator) * START_PERCENT);
    }
    else {
        currentProgressPercent = START_PERCENT
            + Math.floor(
                ((indicatorCurrent - stages[0].indicator) / (taskIndicator - stages[0].indicator)) * PERCENT_LENGTH
            );
    }
    currentProgressPercent = Math.min(currentProgressPercent, 100);

    // 奖池剩余为0时显示为灰点（奖池剩余比例是四舍五入）
    const dotType = (rewardCurrentStock / rewardStock) >= 0.5 * 0.01 ? 'dot' : 'gray-dot';
    return (
        <AixProgress
            className="task-card-progress"
            width="auto"
            value={[
                {
                    percent: currentProgressPercent,
                    style: {background: 'linear-gradient(90deg, #FFDD67 0%, #FF870F 100%)'},
                },
            ]}
            marks={
                stages.map(({indicator}: any) => {
                    const percent = START_PERCENT
                        + (
                            Math.floor(
                                ((indicator - stages[0].indicator) / (taskIndicator - stages[0].indicator))
                                * PERCENT_LENGTH
                            )
                        );
                    return {
                        percent,
                        type: percent > currentProgressPercent ? dotType : 'checked-dot',
                    };
                })
            }
        />
    );
}
