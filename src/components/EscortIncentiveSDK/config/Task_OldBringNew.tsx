/* eslint-disable max-len */
/*
 * @file 老带新任务
 * <AUTHOR>
 * @date 2025-07-29 15:28:28
 */

import dayjs from 'dayjs';
import {Typography} from '@baidu/light-ai-react';
import {Tooltip, Button, Toast, Table, Link, Popover} from '@baidu/one-ui';
import {IconQuestionCircle, IconLink} from 'dls-icons-react';
import classNames from 'classnames';
import {copyText} from '@/utils/text/copy';
import {LogActionType, TaskIdEnum, TaskStatusEnum} from '../config';
import {useEscortIncentiveConfig} from '../context';
import {EscortButton} from '../components/Button';
import {TaskDetailData, TaskBasicInfo} from '../interface/task';
import {EscortCoupons, SimpleCoupons} from '../components/Coupons';
import './Task_OldBringNew.global.less';

function TaskEstimateTipOnCard() {
    return (
        <Typography.Markdown className="estimate-tip">
            邀请成功最高得1500元京东卡+5000元优惠券
        </Typography.Markdown>
    );
}

type TaskFeaturesOnCardProps = Pick<TaskBasicInfo, 'taskId' | 'rewardLimit' | 'endTime' | 'estimateData'> & {
    onAcceptTask: (taskId: number) => void;
    pendingCount: number;
    viewTaskDetail: (taskId: number) => void;
};
function TaskFeaturesOnCard({
    endTime,
    taskId,
    onAcceptTask,
    pendingCount,
    viewTaskDetail,
}: TaskFeaturesOnCardProps) {
    return (
        <div>
            <CardOperation jdNum={1500} coinNum={5000} />
            <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                <div className="task-features">
                    限时优惠
                    <Tooltip title={`活动截止${dayjs(endTime).format('YYYY-MM-DD')}`}>
                        <IconQuestionCircle />
                    </Tooltip>
                </div>
                <EscortButton
                    size="small"
                    onClick={() => {
                        onAcceptTask(taskId);
                        viewTaskDetail(taskId);
                    }}
                    loading={!!pendingCount}
                >
                    参与活动
                </EscortButton>
            </div>
        </div>
    );
}

type AcceptedTaskCardContentProps = Pick<
    TaskBasicInfo,
    'rewardLimit' | 'reportData' | 'extConfig' | 'indicatorCurrent' | 'taskIndicator' | 'endTime' | 'rewards' | 'status' | 'rewardsData' | 'estimateData'
>;

function Left({jdNum}: {jdNum: number}) {
    return (
        <div className="task-old-bring-new-coupon-amount">
            <div className="task-old-bring-new-coupon-amount-value">
                <span className="unit"><span className="prefix">最高</span>￥</span>
                <span className="number">{jdNum}</span>
            </div>
            <div className="task-old-bring-new-coupon-amount-label">
                京东卡（成功立享）
            </div>
        </div>
    );
}
function Right({coinNum}: {coinNum: number}) {
    return (
        <div className="task-old-bring-new-coupon-amount">
            <div className="task-old-bring-new-coupon-amount-value">
                <span className="unit"><span className="prefix">最高</span>￥</span>
                <span className="number">{coinNum}</span>
            </div>
            <div className="task-old-bring-new-coupon-amount-label">
                优惠券(部分地区)
                <Popover content="福建省/重庆市/武汉市/济南市" placement="top">
                    <IconQuestionCircle style={{marginLeft: '3px'}} />
                </Popover>
            </div>
        </div>
    );
}

interface ECouponProp {
    jdNum: number; coinNum: number;
}
function CardOperation({jdNum = 1500, coinNum = 5000}: ECouponProp) {
    return (
        <div className="task-old-bring-new-coupon">
            <EscortCoupons
                renderLeft={<Left jdNum={jdNum} />}
                renderRight={<Right coinNum={coinNum} />}
            />
        </div>
    );
}

function AcceptedTaskCardContent({
    endTime,
    status,
    reportData,
}: AcceptedTaskCardContentProps) {
    const {inviteUsers = []} = reportData || {};
    const totalInvite = inviteUsers.length;
    const totalInviteSuccess = inviteUsers.filter(item => item.inviteStatus === 1).length;
    const totalInvitePayCount = inviteUsers.filter(item => item.hasPay === 1).length;
    const {log, userId} = useEscortIncentiveConfig();

    const formattedEndTime = dayjs(endTime).format('YYYY-MM-DD');
    // 成功邀请客户数*单张京东卡额度300元+成功邀请客户数*专属优惠券1000元（例如成功邀请1位客户，任务中状态前卡显示¥1300）
    const rewards = totalInvitePayCount * 300 + totalInvitePayCount * 1000;

    switch (status) {
        case TaskStatusEnum.accepted:
        case TaskStatusEnum.notMeetTaskLimit:
            return (
                <TaskStatusInfo
                    leftContent={
                        <>
                            <div className="invite-status">
                                已邀请{totalInvite}位客户，
                                <span className="invite-pay-count">{totalInvitePayCount}位</span>已产生消费
                            </div>
                            <div className="task-features error medium">
                                截止日期{formattedEndTime}
                            </div>
                        </>
                    }
                    rightContent={
                        <>
                            <div className="top-tips">预计可获得</div>
                            <div className="money">¥{rewards}</div>
                            <EscortButton
                                size="small"
                                className="invite-link-copy"
                                onClick={e => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onCopyLink({
                                        userId,
                                        totalInviteSuccess,
                                        log,
                                        endTime,
                                        from: 'card',
                                        isMobile: false,
                                    });
                                }}
                            >
                                复制邀请链接
                            </EscortButton>
                        </>
                    }
                />
            );

        case TaskStatusEnum.completed:
            return (
                <TaskStatusInfo
                    jdNum={1500}
                    coinNum={5000}
                    showCardOperation
                    leftContent={
                        <>
                            <div className="invite-status">预计10月31日前完成发放</div>
                            <div className="task-finished">活动结束</div>
                        </>
                    }
                    rightContent={
                        <>
                            <div className="top-tips">预计可获得</div>
                            <div className="money">¥{rewards}</div>
                            <div className="result">成功邀请:{totalInviteSuccess}/{totalInvite}</div>
                        </>
                    }
                />
            );


        default:
            return null;
    }
}

interface BaseProps {
    leftContent: React.ReactNode;
    rightContent: React.ReactNode;
}

type WithCardOperation = BaseProps & {
    showCardOperation: true;
} & ECouponProp; // 必须传 jdNum, coinNum

type WithoutCardOperation = BaseProps & {
    showCardOperation?: false; // 或不传
} & Partial<ECouponProp>; // 可以不传

type TaskStatusInfoProps = WithCardOperation | WithoutCardOperation;
// ✅ 子组件 - 公共布局
function TaskStatusInfo({
    leftContent, rightContent, showCardOperation = false,
    jdNum, coinNum,
}: TaskStatusInfoProps) {
    return (
        <>
            <div className="invite-status-container">
                <div className="left">{leftContent}</div>
                <div className="right">{rightContent}</div>
            </div>
            {showCardOperation && <CardOperation jdNum={jdNum!} coinNum={coinNum!} />}
        </>
    );
}

function SubTaskProgressOnCard() {
    return null;
}

type TaskDetailContentProps = Pick<
    TaskDetailData,
    'extConfig' | 'taskCurrentValue' | 'taskTargetValue' | 'reportData' | 'rewardLimit' | 'currentRewardsCount'
    | 'userTaskUpdateTime' | 'estimateData' | 'rewardsData' | 'endTime'
>;

const columns = [
    {
        title: '公司名称',
        dataIndex: 'username',
        key: 'username',
    },
    {
        title: '客户类型',
        dataIndex: 'type',
        key: 'type',
    },
    {
        title: '是否邀请成功',
        dataIndex: 'ifSuccess',
        key: 'ifSuccess',
    },
    {
        title: '备注',
        dataIndex: 'other',
        key: 'other',
    },
];

const Mark = Typography.Mark;

const isMobile = /Mobi|Android|iPhone/i.test(navigator.userAgent) || document.body.clientWidth <= 500;

function EnhanceLink({totalInviteSuccess, endTime}: {totalInviteSuccess: number, endTime: string}) {
    const {log, userId} = useEscortIncentiveConfig();
    return (
        <div className="enhance-link-container">
            <div className="left-text">
                限时领取先到先得，复制专属链接邀请新客户!
            </div>
            <div className="right-link">
                <Button
                    type="strong"
                    icon={<IconLink style={{color: '#3A5BFD'}} />}
                    size="medium"
                    onClick={() => onCopyLink({
                        userId,
                        totalInviteSuccess,
                        log,
                        endTime,
                        from: 'detail',
                        isMobile: false,
                    })}
                >复制专属链接邀请客户
                </Button>
            </div>
        </div>
    );
}
function TaskDetailContent({
    userTaskUpdateTime,
    reportData,
    endTime,
}: TaskDetailContentProps) {
    const inviteUsers = reportData?.inviteUsers || [];
    const totalInviteSuccess = inviteUsers.filter(item => item.inviteStatus === 1).length;
    const data = inviteUsers.map((item, index) => ({
        key: index,
        username: item.inviteCompany,
        type: item.custType === 1 ? '个人' : '企业',
        ifSuccess: item.inviteStatus === 1 ? '是' : '否',
        other: item.inviteFailReason || '',
    }));
    const totalInvite = inviteUsers.length; // 可能用得到
    const totalInvitePayCount = inviteUsers.filter(item => item.hasPay === 1).length;

    const InviteSummaryWithJD = ({totalInvite, totalInvitePayCount}: {totalInvite: number, totalInvitePayCount: number}) => {
        const jdCards = totalInvitePayCount * 300; // 京东卡总额
        const coupons = totalInvitePayCount * 1000; // 优惠券总额

        return (
            <>
                已邀请<Mark status="warning" className="bold-num">{totalInvite}</Mark>个客户，其中<Mark status="warning" className="bold-num">{totalInvitePayCount}</Mark>个客户已产生消费，预计可获得<span style={{color: '#FF5500'}}>¥{jdCards}京东卡 + ¥{coupons}优惠券</span>
            </>
        );
    };

    return (
        <div className="task-detail-content">
            {!isMobile && <EnhanceLink totalInviteSuccess={totalInviteSuccess} endTime={endTime} />}
            <div className="task-content-target">
                <div className="task-content-target-title">
                    <div className="title">邀请客户列表</div>
                    {
                        userTaskUpdateTime && (
                            <div className="task-update-time">数据更新时间：{userTaskUpdateTime}</div>
                        )
                    }
                </div>
                <div className="task-content-target-info">
                    <div className="info">
                        <div className="left">
                            <InviteSummaryWithJD totalInvite={totalInvite} totalInvitePayCount={totalInvitePayCount} />
                        </div>
                        <div className="right">限时领取先到先得</div>

                    </div>
                    <div className="table-detail">
                        <Table
                            columns={columns}
                            dataSource={data}
                            showHeader
                            pagination={false}
                        />
                    </div>
                </div>
                <Typography.Markdown className="old-bring-new-task-statement">
                    {`
1. 活动周期：2025年7月1日至9月30日，专属优惠券预计2025.10.31前完成发放
2. 可参与任务的客户：营销联络中心近90天有消费客户
3. 优惠券不可与框架优惠叠加，优惠券可以转移给同一客户的其他账户
4. 邀请1名新客户可获得1张300元京东卡；如邀请客户区域福建省/重庆市/武汉市/济南市，额外可得1000元专属优惠券；老客户最多可邀请5名新客户
5. 邀请新客户成功定义：新客在联中开户并上线，新客需要符合新开口径，近90天日均=0；客户区域以爱企查“行政区”界定
6. 优惠券类型：优惠券为5折折扣券，适用于搜索推广、信息流推广、知识营销产品线
7. 奖池有限，先到先得，京东卡待活动结束后统一线下快递邮寄发送
                    `}
                </Typography.Markdown>
            </div>
        </div>
    );
}

function HistoryTaskTip({
    status,
    taskId,
    rewardsData,
}: {
    status: TaskStatusEnum;
    taskId: TaskIdEnum;
    rewardsData: {
        coupon: number;
        isSameCid: boolean;
        rewards?: number;
    };
}) {
    const {log} = useEscortIncentiveConfig();
    const onLinkClick = () => {
        log(LogActionType.TASKS_HISTORY_VIEW_COUPONS, {taskId});
    };
    return (
        <div>
            {
                status === TaskStatusEnum.completedAndGetCoin && (
                    <div className="effect">
                        恭喜获得<SimpleCoupons value={rewardsData.rewards || 0} size="small" />
                        <span className="effect-text">5折优惠券一张</span>，
                        <span onClick={onLinkClick}>
                            <Link
                                type="strong"
                                target="_blank"
                                size="small"
                                toUrl="https://eyouhui.baidu.com/polaris-web/front-advertisers/#/home/<USER>"
                            >
                                去查看
                            </Link>
                        </span>
                    </div>
                )
            }
            {
                status === TaskStatusEnum.completed && rewardsData?.isSameCid && (
                    <div className="effect">同主体下其他账户已获得优惠券</div>
                )
            }
        </div>
    );
}

function AcceptedTaskCardTitle({title}: TaskBasicInfo) {
    return <div className="title old-bring-new-title">{title}</div>;
}

function generateLink(userId: string | number) {
    const encoded = btoa(String(userId));
    return `https://qingge.baidu.com/mobile/taskInvite?userId=${encoded}`;
}

function onCopyLink({userId, totalInviteSuccess, log, endTime, from, isMobile}: {
    userId: string | number;
    totalInviteSuccess: number;
    log: (action: LogActionType, params?: Record<string, any>) => void;
    endTime: string;
    from: 'card' | 'detail';
    isMobile: boolean;
}) {
    log(from === 'card' ? LogActionType.COPY_INVITE_LINK_CARD : isMobile ? LogActionType.COPY_INVITE_LINK_DETAIL_MOBILE : LogActionType.COPY_INVITE_LINK_DETAIL);
    // 链接失效（邀请成功的客户已达到上限5个；或者任务到期） ；endTime
    const overTime = new Date().getTime() > new Date(endTime).getTime();
    if (totalInviteSuccess >= 5 || overTime) {
        Toast.error({content: '链接失效'});
        return;
    }
    const link = generateLink(userId);
    copyText(link, {
        successMessage: '邀请链接复制成功，1秒后跳转邀请页面',
        showErrorDetail: true,
    });
    setTimeout(() => {
        window.location.href = link;
    }, 1000);
}
function TaskDetailTitle({title, daysRemaining, endTime, reportData}: {title: string, daysRemaining: number, reportData: TaskDetailData['reportData'], endTime: string}) {
    const {inviteUsers = []} = reportData || {};
    const totalInviteSuccess = inviteUsers.filter(item => item.inviteStatus === 1).length;
    const {userId, log} = useEscortIncentiveConfig();
    return (
        <div className={classNames('task-detail-container-title', {
            'task-detail-container-title-mobile': isMobile,
        })}
        >
            <div className="task-detail-name">{title}</div>
            <div className="task-detail-status">
                {isMobile ? (
                    <>
                        <div className="text">9月30日24点截止，剩余<div className="days-remaining">{daysRemaining}天</div></div>
                        <div
                            className="days copy-link"
                            onClick={() => onCopyLink({
                                userId,
                                totalInviteSuccess,
                                log,
                                endTime,
                                from: 'detail',
                                isMobile: true,
                            })}
                        >
                            复制专属链接邀请客户
                        </div>
                    </>
                ) : (
                    <>
                        <div className="days">
                            9月30日24点截止，剩余<div className="days-remaining">{daysRemaining}天</div>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
}

export const TaskOldBringNewSlots = {
    SubTaskProgressOnCard,
    AcceptedTaskCardContent,
    TaskEstimateTipOnCard,
    TaskFeaturesOnCard,
    TaskDetailContent,
    HistoryTaskTip,
    TaskDetailTitle,
    AcceptedTaskCardTitle,
};
