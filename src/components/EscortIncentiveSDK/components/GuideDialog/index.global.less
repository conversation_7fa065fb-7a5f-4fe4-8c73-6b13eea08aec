.banfei-guide-dialog {
    &-content-bg {
        background-image: var(--guide-dialog-bg-image, url(https://fc-feed.bj.bcebos.com/aix%2Fbanfei_guide_dialog.png));
        background-repeat: no-repeat;
        height: 450px;
        width: 542px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-size: cover;
    }

    // 智能翻新
    &.rebuild-variant &-content-bg {
        --guide-dialog-bg-image: url(https://fc-feed.bj.bcebos.com/aix%2Fbanfei_rebuild_guide_dialog.png);
    }

    // 老带新
    &.old-bring-new &-content-bg {
        --guide-dialog-bg-image: url(../../resource/oldInviteNew.png);
    }

    &-content-text {
        position: absolute;
        bottom: -71px;
        width: 384px;
        font-weight: 400;
        font-size: 16px;
        line-height: 22px;
        margin-left: 12px;

        .text1 {
            .text-number {
                font-weight: 500;
                color: #FF6638;
            }

            .text-union {
                font-weight: 500;
                margin: 0 2px;
            }
        }

        .text2 {
            margin-top: 72px;

            .text-number {
                font-weight: 500;
                color: #FF6638;
            }
        }
    }

    &-content-btn {
        cursor: pointer;
        background-image: url(https://fc-feed.bj.bcebos.com/aix%2Fbanfei_guide_accept.svg);
        z-index: 1;
        position: absolute;
        background-repeat: no-repeat;
        height: 48px;
        width: 478px;
        left: 50%;
        bottom: -49px;
        transform: translate(-50%, 120px);

        &:hover {
            background-image: url(https://fc-feed.bj.bcebos.com/aix%2Fbanfei_guide_accept_active.svg);
        }
    }

    &-content-image-preload {
        height: 0;
        width: 0;
        visibility: none;
        background-image: url(https://fc-feed.bj.bcebos.com/aix%2Fbanfei_guide_accept_active.svg);
    }

    .one-dialog-header {
        position: absolute;
        z-index: 1;
        right: 16px;
        top: -50px;
    }

    .one-dialog-close {
        margin-right: 0;
        position: absolute;
        color: #fff;
        right: -123px;
        top: -142px;
        height: 36px !important;
        width: 36px;
        background: #ffffff4d;
        border-radius: 30px;
    }

    .pc-custom-render {
        .banfei-guide-dialog-content-bg {
            background-image: url(../../resource/pcDialog.png);
            background-repeat: no-repeat;
            height: 450px;
            width: 542px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-size: cover;
        }

        .banfei-guide-dialog-content-text {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;

            .header-coins {
                position: absolute;
                left: 290px;
                top: 104px;
                color: #FF693B;
                font-weight: 500;
                font-size: 18px;
            }

            .total-coins {
                position: absolute;
                left: 468px;
                top: 165px;
                font-weight: 400;
                font-size: 12px;
                color: #ffd;
            }

            .process-coins {
                display: flex;
                justify-content: space-between;
                position: absolute;
                left: 95px;
                top: 237px;

                .item {
                    width: max-content;
                    font-weight: 500;
                    color: #FF6638;
                    font-size: 14px;
                }

                .item:nth-child(2) {
                    margin-left: 90px;
                }

                .item:nth-child(3) {
                    margin-left: 89px;
                }

                .item:nth-child(4) {
                    margin-left: 87px;
                }
            }
        }

        .banfei-guide-dialog-content-button {
            display: flex;
            justify-content: space-between;
            position: absolute;
            left: 0;
            bottom: 62px;
            width: 100%;

            .button-base() {
                position: relative;
                cursor: pointer;
                width: 231px;
                height: 48px;
                border-radius: 36px;
                padding: 4px 8px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 16px;
            }

            .left {
                margin-left: 32px;
                background-color: #FFF4DD;
                color: #FF6C3F;
                .button-base();
            }

            .right {
                margin-right: 32px;
                background: linear-gradient(90deg, #FF875C 0%, #FF6537 100%);
                color: #FFF;
                .button-base();
            }

            .right::before {
                content: "";
                position: absolute;
                bottom: 100%;
                left: 50%;
                transform: translateX(-12%) translateY(9px);
                width: 142px;
                height: 24px;
                background-image: url("../../resource/optimize-plan.svg");
                background-repeat: no-repeat;
                background-size: contain;  /* 保持比例缩放 */
                background-position: center;
            }


            .left:hover {
                background: #FFEBC4;
            }

            .right:hover {
                background: linear-gradient(90deg, #FF9A75 0%, #FF7952 100%);
            }
        }
    }
}


.content-btn-old-bring-new {
    background-image: url(../../resource/obn.svg);

    &:hover {
        background-image: url(../../resource/obnHover.svg);
    }
}

