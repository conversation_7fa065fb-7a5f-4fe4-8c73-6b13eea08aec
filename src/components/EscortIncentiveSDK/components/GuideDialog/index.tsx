/**
 * @file 通用任务引导弹窗
*/

import {Dialog, DialogDestroyable} from '@baidu/one-ui';
import classNames from 'classnames';
import {LogActionType} from '../../config';
import {TaskBasicInfo} from '../../interface/task';
import './index.global.less';

interface DialogToQinggeProps {
    confirm?: {
        onOk?: () => void;
        onClose?: () => void;
    };
    task: TaskBasicInfo;
    log: (action: LogActionType, params?: any) => void;
}

type Text1 = (task: TaskBasicInfo) => React.ReactNode;

type GuideDialogConfig = {
    wrapClassName?: string;
    customBtnClassName?: string;
} & (
    {
        render: (
            task: TaskBasicInfo,
            onOk: any,
            onClose: any
        ) => React.ReactNode;
    } | {
        getText1: Text1;
        getText2: (() => string) | Text1;
        render?: never;
    }
);

/**
 * 通用引导弹窗函数
 */
const guideDialogMap = new Map<string, DialogDestroyable>();

function createGuideDialog(config: GuideDialogConfig) {
    return ({confirm, task, log}: DialogToQinggeProps) => {
        const {customBtnClassName = '', render} = config;
        const taskId = task.taskId;
        // 生成缓存 key，基于配置和任务ID
        const configKey = JSON.stringify({
            wrapClassName: config.wrapClassName,
            taskId,
        });

        // 如果已有相同类型的弹窗实例，先销毁旧的再创建新的，确保回调是最新的
        if (guideDialogMap.has(configKey)) {
            const existingDialog = guideDialogMap.get(configKey);
            existingDialog?.destroy();
            guideDialogMap.delete(configKey);
        }

        log(LogActionType.VIEW_GUIDE_DIALOG, {taskId});

        const onOk = (dialog: DialogDestroyable) => {
            log(LogActionType.GUIDE_DIALOG_CONFIRM, {taskId});
            confirm?.onOk && confirm.onOk();
            dialog.destroy();
            guideDialogMap.delete(configKey); // 弹窗销毁后删除缓存
        };
        const onClose = (dialog: DialogDestroyable) => {
            confirm?.onClose && confirm.onClose();
            dialog.destroy();
        };

        const dialog = Dialog.confirm({
            title: '',
            needCloseIcon: true,
            destroyOnClose: true,
            content: render ? render(task, () => onOk(dialog), () => onClose(dialog)) : (
                <div className="banfei-guide-dialog-content">
                    <div className="banfei-guide-dialog-content-bg" />
                    <div className="banfei-guide-dialog-content-text">
                        <div className="text1">{config.getText1(task)}</div>
                        <div className="text2">{config.getText2(task)}</div>
                    </div>
                    <div
                        className={classNames('banfei-guide-dialog-content-btn', {
                            [customBtnClassName]: true,
                        })}
                        onClick={() => onOk(dialog)}
                    />
                    <div className="banfei-guide-dialog-content-image-preload"></div>
                </div>
            ),
            footer: [],
            afterClose: () => {
                confirm?.onClose && confirm.onClose();
                guideDialogMap.delete(configKey); // 弹窗销毁后删除缓存
            },
            wrapClassName: config.wrapClassName,
        });

        // 缓存实例
        guideDialogMap.set(configKey, dialog);
    };
}


/**
 * PC任务引导弹窗
 */
export const showGuideDialog = createGuideDialog({
    wrapClassName: 'banfei-guide-dialog',
    render(task, onOk, onClose) {
        // TODO: 接口
        return (
            <div className="banfei-guide-dialog-content pc-custom-render">
                <div className="banfei-guide-dialog-content-bg">
                    <div className="banfei-guide-dialog-content-text">
                        <div className="header-coins">500</div>
                        <div className="total-coins">100</div>
                        <div className="process-coins">
                            <div className="item">100</div>
                            <div className="item">200</div>
                            <div className="item">300</div>
                            <div className="item">400</div>
                        </div>
                    </div>
                    <div className="banfei-guide-dialog-content-button">
                        <div className="left" onClick={onClose}>
                            查看详情
                        </div>
                        <div className="right" onClick={onOk}>
                            领取任务
                        </div>
                    </div>
                </div>
            </div>
        );
    },
});

/**
 * 智能翻新引导弹窗
 */
export const showRebuildGuideDialog = createGuideDialog({
    wrapClassName: 'banfei-guide-dialog rebuild-variant',
    getText1: task => {
        const {rewardLimit} = task;
        return (
            <>
                完成任务最高可获得
                <span className="text-number">6000元5折优惠券</span>
            </>
        );
    },
    getText2: () => '使用「智能翻新」功能 提升账户基建水平&预算利用率',
});

/**
 * 老带新
 */
export const showOldBringNewGuideDialog = createGuideDialog({
    wrapClassName: 'banfei-guide-dialog rebuild-variant old-bring-new',
    customBtnClassName: 'content-btn-old-bring-new',
    getText1: () => {
        return (
            <>
                邀新最高得<span className="text-number">1500元京东卡</span>+<span className="text-number">5000元优惠券</span>(部分地区)
            </>
        );
    },
    getText2: () => {
        return (
            <>
                您的好友最高可得<span className="text-number">6000元优惠券</span>或<span className="text-number">开户立减1000元</span>
            </>
        );
    },
});
