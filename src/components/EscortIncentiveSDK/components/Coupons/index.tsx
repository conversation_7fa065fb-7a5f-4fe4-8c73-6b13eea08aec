/**
 * Coupons优惠券金额
*/
import classNames from 'classnames';
import {IconCouponMinusSolid} from 'dls-icons-react';
import {useSlot} from '@/hooks/slot';
import './index.global.less';

export function SimpleCoupons({value, size = 'medium'}: {value: number, size?: 'large' | 'medium' | 'small'}) {
    return (
        <div className={`coupons-value coupons-value-${size}`}>
            <span>{value >= 0 ? `￥${value}` : `${value}`}</span>
            <IconCouponMinusSolid />
        </div>
    );
}

// 丰富版优惠券
interface CouponsProps {
    charge: number;
    coupon: number;
    mode?: 'card' | 'detail';
    selected?: boolean;
    onClick?: () => void;
}

export function Coupons({
    charge,
    coupon,
    mode = 'card',
    selected = false,
    onClick,
}: CouponsProps) {
    const couponClsName = classNames('coupon-container', `coupon-${mode}`, {
        'coupon-selected': selected,
    });
    return (
        <div className={couponClsName} onClick={onClick}>
            <div className="coupon-title">
                <div className="coupon-label">充</div>
                <div className="coupon-value">{charge}</div>
                <div className="coupon-unit">元</div>
            </div>
            <div className="coupon-content">
                <div className="coupon-label">送</div>
                <div className="coupon-value">{coupon}</div>
                <div className="coupon-unit">元券</div>
            </div>
        </div>
    );
}

export function EscortCoupon({contentWidth = '50%', className = '', ...props}) {
    const Slot = useSlot(props);
    const cls = classNames('escort-coupon', className);

    return (
        // @ts-ignore
        <div className={cls} style={{'--escort-coupon-content-width': contentWidth}}>
            <div className="escort-coupon-left" style={{width: contentWidth}}>
                <Slot name="left">{props.value}</Slot>
            </div>
            <div className="escort-coupon-divider"></div>
            <div className="escort-coupon-right">
                <Slot name="right">{props.desc}</Slot>
            </div>
        </div>
    );
}


export function EscortCoupons({contentWidth = '50%', ...props}) {
    const Slot = useSlot(props);

    return (
        // @ts-ignore
        <div className="escort-coupons" style={{'--escort-coupon-content-width': contentWidth}}>
            <div className="escort-coupon-left" style={{width: contentWidth}}>
                <Slot name="left">{props.value}</Slot>
            </div>
            <div className="escort-coupon-right" style={{width: contentWidth}}>
                <Slot name="right">{props.value}</Slot>
            </div>
        </div>
    );
}
