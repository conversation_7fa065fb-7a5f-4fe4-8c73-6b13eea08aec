import {AppIdEnum, LogActionType} from '../config';

export function generateLog({send, appId}: {send: (...args: any[]) => void, appId: AppIdEnum}) {
    return function (action: LogActionType, params?: any) {
        if (
            [
                LogActionType.ACCEPT_TASK_SUCCESS,
                LogActionType.ACCEPT_TASK_FAIL,
                LogActionType.VIEW_ACCEPTED_TASK,
                LogActionType.OPEN_TASK_DETAIL,
                LogActionType.OPEN_TASK_DETAIL,
                LogActionType.VIEW_UNACCEPTED_TASK,
                LogActionType.CLICK_PAY_BUTTON,
                LogActionType.TASKS_HISTORY_VIEW_COUPONS,
                LogActionType.VIEW_GUIDE_DIALOG,
                LogActionType.GUIDE_DIALOG_CONFIRM,
                LogActionType.GUIDE_DIALOG_VIEW,
            ].includes(action)
        ) {
            send('click', {level: appId, source: 'EscortTask', target: action, info: params?.taskId});
        }
        else if ([
            LogActionType.GO_TO_OPTIMIZE_SUBTASK,
            LogActionType.VIEW_SUBTASK_IN_DETAIL,
        ].includes(action)) {
            send('click', {level: appId, source: 'EscortTask', target: action, info: params.subTaskId});
        }
        else if ([
            LogActionType.GO_TO_MATERIAL_LIST,
        ].includes(action)) {
            send('click', {level: appId, source: 'EscortTask', target: action, info: params.pageType});
        }
        else {
            send('click', {level: appId, source: 'EscortTask', target: action});
        }
    };
}
