import classNames from 'classnames';
import {UIInput} from '@baidu/react-formulator';
import {Button} from '@baidu/one-ui';
import {useHover} from 'huse';
import {useEllipsis} from '@/hooks/element/useEllipsis';
import './style.global.less';

export interface ReadOnlyTextProps {
    wrap?: boolean;
    placeholder?: string;
    width?: string | number;
    value: React.ReactNode;
    transForm?: (value: any, props: ReadOnlyTextProps) => string | React.ReactNode;
    onChatBtnClick?: (e: any, value: any) => void;
    disabled?: boolean;
    style?: any;
    chatBtnText?: string;
    className?: string;
    isEmpty?: boolean;
    children?: React.ReactNode;
    btnVisible?: boolean;
}

export function ReadOnlyText(props: ReadOnlyTextProps) {
    const {
        wrap, placeholder, width, value, transForm, disabled,
        onChatBtnClick, style, chatBtnText, className, isEmpty,
        children,
    } = props;
    const content = transForm ? transForm(value, props) : value;
    const cls = classNames(className, {
        'qg-form-input-readonly': true,
        'disabled': disabled,
        'qg-form-input-readonly-wrap': wrap,
        'clickable': !!props.onChatBtnClick,
        'empty': isEmpty,
        'btn-visible': !!props.btnVisible,
    });

    const [ellipsisRef, ellipsisWidth] = useEllipsis([content]);
    return (
        <div
            className={cls}
            style={{...style, width}}
            onClick={e => !disabled && onChatBtnClick && onChatBtnClick(e, value)}
        >
            {
                placeholder && !content && (
                    <span className="qg-form-input-readonly-text-placeholder">{placeholder}</span>
                )
            }
            {
                content && (
                    <span
                        ref={ellipsisRef}
                        className="qg-form-input-readonly-text"
                        title={typeof content === 'string' && (!!ellipsisWidth) ? content : undefined}
                    >
                        {content}
                    </span>)
            }
            {children}
            {!disabled && props.onChatBtnClick && (
                <Button
                    type="text-strong"
                    className="qg-form-input-text-btn"
                >
                    {chatBtnText || '对话修改'}
                </Button>)
            }
        </div>
    );
}

export function InputText({
    onChatBtnClick,
    width,
    chatBtnText,
    disabled,
    isDirectShow = false,
    ...inputProps
}: {
    onChatBtnClick?: () => void;
} & Parameters<typeof UIInput>[0]) {

    const [isHover, hoverCallbacks] = useHover();

    const props: Parameters<typeof UIInput>[0] = {
        ...inputProps,
        disabled,
        width: '100%',
    };
    return (
        <div style={{width}} className="qg-form-input-text" {...hoverCallbacks}>
            <UIInput
                {...props}
            />
            {
                (isDirectShow || isHover) ? (
                    <Button
                        type="text-strong"
                        onClick={onChatBtnClick}
                        className="qg-form-input-text-btn"
                        disabled={disabled}
                    >{chatBtnText || '对话修改'}
                    </Button>
                ) : null
            }
        </div>

    );
}
