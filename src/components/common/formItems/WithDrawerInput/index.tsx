import {ComponentProps, useMemo, useState} from 'react';
import {Drawer, DrawerProps} from '@baidu/one-ui';
import {FormConfig} from '@baidu/react-formulator';
import {ReadOnlyText, InputText} from '../text';
import {
    CommonFormEditor,
    CommonFormEditorDrawerFooter,
    CommonFormEditorProps,
} from '../../materialList/commonFormEditor';


interface CommonInput<T> extends Record<string, any> {
    value: T;
    onChange: (value: T) => void;
}

type WithDrawerInputProps<T extends Record<string, any>> = {
    formConfig: FormConfig<T>;
    transForm?: (value: T, props: WithDrawerInputProps<T>) => string;
    disabled?: boolean;
    drawerProps?: DrawerProps;
    readOnlyTextProps?: Partial<ComponentProps<typeof ReadOnlyText>>;
    valueFormatter?: (value: T) => T;
    extraSlots?: Record<string, React.ComponentType>;
} & CommonInput<T>;


export default function WithDrawerInput<T extends Record<string, any>>(props: WithDrawerInputProps<T>) {

    const {
        value,
        onChange,
        formConfig,
        transForm,
        drawerProps: drawerPropsFromProps = {},
        disabled,
        initialData,
        isReadOnlyText = true,
        readOnlyTextProps,
        inputTextProps,
        className,
        extraSlots,
        valueFormatter,
    } = props;
    const [visible, setVisible] = useState(false);

    const [id, setId] = useState(0);


    const editorProps: CommonFormEditorProps<T> = useMemo(() => ({
        initialData,
        closeEditor: () => setVisible(false),
        onSave: async (v: T) => {
            onChange(valueFormatter ? valueFormatter(v) : v);
        },
        formConfig,
        slots: {
            footer: CommonFormEditorDrawerFooter,
            ...extraSlots,
        },
    }), [id]);


    const textProps = {
        value,
        className,
        transForm: () => {
            return transForm ? transForm(value, props) : value;
        },
        disabled,
        chatBtnText: '修改',
        onChatBtnClick: () => {
            setId(id => id + 1);
            setVisible(true);
        },
        ...readOnlyTextProps,
    };

    const inputProps = {
        value,
        onChange: (value: any) => {
            const {field} = inputTextProps;
            if (field) {
                onChange({
                    [field]: value,
                });
            }
            else {
                onChange(value);
            }
        },
        className,
        disabled,
        onChatBtnClick: () => {
            setId(id => id + 1);
            setVisible(true);
        },
        ...inputTextProps,
    };

    const drawerProps: DrawerProps = {
        visible,
        onClose: () => setVisible(false),
        title: '修改',
        destroyOnClose: true,
        ...drawerPropsFromProps,
    };

    return (
        <>
            {isReadOnlyText ? <ReadOnlyText {...textProps} /> : <InputText {...inputProps} />}
            <Drawer {...drawerProps}>
                <div className="with-drawer-input-drawer-content">
                    <CommonFormEditor {...editorProps} />
                </div>
            </Drawer>
        </>
    );
}
