import {useActionPending} from 'huse';
import {Button, ProviderConfig} from '@baidu/one-ui';
import {useQuickForm, FormProvider, FormConfig} from '@baidu/react-formulator';
import {ReactNode, useMemo} from 'react';
import classNames from 'classnames';
import {displayErrorForRF, AIX_ERROR, ConvertFieldFunc} from '@/utils/error';
import {useSlot} from '@/hooks/slot';
import './style.global.less';

export interface CommonFormEditorProps<T extends Record<string, any>> {
    initialData: T;
    closeEditor: () => void;
    onSave: (values: T) => (Promise<void> | void);
    formConfig: FormConfig<T>;
    convertField?: ConvertFieldFunc;
    title?: ReactNode;
    isUseLabelTop?: boolean;
    isUseHorizontal?: boolean;
    slots?: {
        afterForm?: React.ComponentType;
        footer?: React.ComponentType<{
            onConfirm: () => void;
            onCancel: () => void;
            loading: boolean;
        }>;
    };
    isDynamicFormConfig?: boolean;
}

export function CommonFormEditor<T extends Record<string, any>>(
    {
        initialData, closeEditor, onSave,
        formConfig, convertField, title,
        isUseLabelTop = true,
        isUseHorizontal = false,
        slots, isDynamicFormConfig = false,
    }: CommonFormEditorProps<T>
) {
    const [Form, {validateFields, setFieldsError}] = useQuickForm();

    const config = useMemo(() => {
        return formConfig;
    }, isDynamicFormConfig ? [formConfig] : []); // !这里直接固定死，不让改成动态的

    const confirm = async () => {
        const values = await validateFields({scrollToError: true});
        try {
            await onSave(values);
        }
        catch (error) {
            displayErrorForRF({
                error: error as AIX_ERROR,
                form: {setFieldsError},
                formData: values,
                options: {convertField},
            });
            return;
        }
        closeEditor();
    };

    const [onConfirm, pendingCount] = useActionPending(confirm);

    const cls = classNames('use-rf-preset-form-ui aix-common-editor-form', {
        'use-label-top': isUseLabelTop,
        'use-horizontal': isUseHorizontal,
    });

    const slotProps = useMemo(() => ({slots}), [slots]);
    const Slot = useSlot(slotProps);

    return (
        <ProviderConfig theme="light-ai">
            <FormProvider value={{inputErrorClassName: 'one-ai-invalid'}}>
                {
                    title && (
                        <div className="aix-common-editor-form-title">{title}</div>
                    )
                }
                <Form
                    config={config}
                    data={initialData}
                    className={cls}
                    // ! 这里的onSubmit 是系统自己触发的
                    // 触发机制看这里 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/gv0WKdV3ZF/KO91dTP6dJ/AYNuPsAWAFSmXX
                    // @ts-ignore
                    onSubmit={e => {
                        e.preventDefault();
                        onConfirm();
                    }}
                />
            </FormProvider>
            <Slot name="afterForm" />
            <div className="footer">
                <Slot
                    name="footer"
                    onConfirm={onConfirm}
                    onCancel={closeEditor}
                    loading={!!pendingCount}
                >
                    <Button onClick={onConfirm} loading={!!pendingCount} type="text-strong">确认</Button>
                    <Button onClick={closeEditor} type="text-aux">取消</Button>
                </Slot>
            </div>
        </ProviderConfig>
    );
}

export function CommonFormEditorDrawerFooter({
    onConfirm,
    onCancel,
    loading,
}: {
    onConfirm: () => void;
    onCancel: () => void;
    loading: boolean;
}) {
    return (
        <div className="common-form-editor-drawer-footer">
            <Button onClick={onConfirm} loading={loading} type="primary">确认</Button>
            <Button onClick={onCancel} type="normal">取消</Button>
        </div>
    );
}
