import {useQuickForm, FormProvider} from '@baidu/react-formulator';
import {ProviderConfig, Button, Toast} from '@baidu/one-ui';
import {matchTypeEnum} from 'commonLibs/config/matchType';
import {getNewMatchTypeDataSource} from 'commonLibs/config/matchType';

const initialData = {
    matchType: matchTypeEnum.accurate.value,
};

const matchTypeDataSource = getNewMatchTypeDataSource();

const formConfig = {
    fields: [
        {
            field: 'matchType',
            use: ['RadioGroup', {
                options: matchTypeDataSource,
                style: {'--one-checkbox-strong-min-width': '87px', 'margin-top': '12px'},
                size: 'small'
            }],
            label: '修改匹配模式',
        },
    ],
};


const MatchType = ({off, onBatchMatchType, reset}: {
    off: () => void;
    onBatchMatchType: ({key}: {key: string}) => void;
    reset: () => void;
}) => {
    const [Form, {validateFields, setFieldsValue}] = useQuickForm();
    const onConfirm = async () => {
        try {
            const values = await validateFields();
            const {matchType} = values;
            onBatchMatchType({key: matchType});
            reset();
        } catch (e) {
            Toast.error({
                content: '修改失败',
                duration: 3
            });
        }
    };
    return (
        <div>
            <FormProvider
                value={{
                    inputErrorClassName: 'one-ai-invalid',
                    showFirstError: true,
                }}
            >
                <ProviderConfig theme="light-ai">
                    <Form
                        config={formConfig}
                        data={initialData}
                        className="use-rf-preset-form-ui use-horizontal use-label-top"
                    />
                </ProviderConfig>
            </FormProvider>
            <div style={{marginTop: -12}}>
                <Button
                    onClick={onConfirm}
                    type="text-strong"
                    style={{marginRight: 12}}
                    size="small"
                >确认
                </Button>
                <Button
                    onClick={off}
                    type="text-aux"
                    size="small"
                >取消
                </Button>
            </div>
        </div>
    );
};

export default MatchType;
