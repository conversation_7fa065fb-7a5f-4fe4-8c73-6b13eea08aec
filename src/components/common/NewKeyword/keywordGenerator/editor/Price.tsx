import {useQuickF<PERSON>, FormProvider} from '@baidu/react-formulator';
import {ProviderConfig, Button, NumberInput, Toast} from '@baidu/one-ui';
import './style.global.less';

const initialData = {
    price: undefined,
};

const PriceEditor = ({value, onChange, resetRecommendPrice}: {
    value: number | undefined;
    onChange: (value: number) => void;
    resetRecommendPrice: () => void;
}) => {
    const numberBoxProps = {
        value,
        size: 'small',
        width: 70,
        fixed: 2,
        step: 0.01,
        onChange: (e: any) => onChange(e.target.value),
        min: 0.01,
        max: 999.99,
        location: 'bottom'
    };
    return (
        <div className="kr-batch-price-editor">
            <NumberInput {...numberBoxProps} />
            <Button
                type="text-strong"
                onClick={resetRecommendPrice}
                className="kr-reset-price-btn"
                size="small"
            >
                恢复建议出价
            </Button>
        </div>
    );
};

const getFormConfig = ({resetRecommendPrice}: {
    resetRecommendPrice: () => void;
}) => {
    return {
        fields: [
            {
                field: 'price',
                use: [PriceEditor, {
                    resetRecommendPrice
                }],
                label: '修改点击出价',
                validators(value: string | undefined) {
                    if (!value) {
                        return '请输入出价';
                    }
                },
            },
        ],
    };
};


const Price = ({off, onBatchPrice, reset, resetRecommendPrice}: {
    off: () => void;
    onBatchPrice: (value: number) => void;
    reset: () => void;
    resetRecommendPrice: () => void;
}) => {
    const [Form, {validateFields, setFieldsValue}] = useQuickForm();
    const onConfirm = async () => {
        try {
            const values = await validateFields();
            const {price} = values;
            onBatchPrice(price);
            reset();
        } catch (e) {
            Toast.error({
                content: '修改失败',
                duration: 3
            });
        }
    };
    const formConfig = getFormConfig({resetRecommendPrice});
    return (
        <div>
            <FormProvider
                value={{
                    inputErrorClassName: 'one-ai-invalid',
                    showFirstError: true,
                }}
            >
                <ProviderConfig theme="light-ai">
                    <Form
                        config={formConfig}
                        data={initialData}
                        className="use-rf-preset-form-ui use-horizontal use-label-top kr-batch-price-form"
                    />
                </ProviderConfig>
            </FormProvider>
            <div style={{marginTop: -4}}>
                <Button
                    onClick={onConfirm}
                    type="text-strong"
                    style={{marginRight: 12}}
                    size="small"
                >确认
                </Button>
                <Button
                    onClick={off}
                    type="text-aux"
                    size="small"
                >取消
                </Button>
            </div>
        </div>
    );
};

export default Price;
