import guid from '@baidu/guid';
import {FcKeywordType, TableKeywordItem, MIX_MATCH_TYPE_ENUM} from './config';

export function getKey(item: TableKeywordItem) {
    return item.id;
}

export function generateItem({
    keyword = '',
    price,
    addFrom,
    mixMatchType,
    suggestSource,
    recommendStrategy,
    originalPrice
}: Partial<FcKeywordType> = {} as FcKeywordType): TableKeywordItem {
    return {
        id: guid(),
        keyValue: keyword,
        price,
        disabled: false,
        addFrom,
        mixMatchType: mixMatchType || MIX_MATCH_TYPE_ENUM.intelligenceCore,
        selected: false,
        suggestSource,
        recommendStrategy,
        originalPrice
    };
}

export function initialKeyword(value: FcKeywordType[] = []) {
    return value.map(item => generateItem(item));
}
