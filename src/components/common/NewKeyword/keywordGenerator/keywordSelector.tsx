import {useCallback, useEffect, useMemo, useState} from 'react';
import {But<PERSON>} from '@baidu/light-ai-react';
import {
    Button as OneButton, SelectProps,
    TextArea, Table, Select, TableProps,
    Toast, Tooltip,
} from '@baidu/one-ui';
import {IconAdjust, IconDownload, IconPlus} from 'dls-icons-react';
import {MultiLabelInput, BatchToolbar} from '@baidu/one-ui-pro';
import SearchBox from 'commonLibs/materialList/SearchBox';
import {
    RegionSelector, KeywordSetting as Setting, KeywordFooter,
    UrlAndBusiness, BusinessExpandKeyword,
} from 'toolsCenter/Kr/Keyword';
import {useShowKeywordGroupGuide} from 'toolsCenter/kr/util';
import CustomFieldsSelector from 'commonLibs/materialList/CustomFieldsSelector';
import {getLengthInBytes} from '@/utils/string';
import {getUserId} from '@/utils';
import {useRegister} from '@/hooks/tableList/register';
import {getKeywordPromptByUrl} from '@/api/fcThreeLevel/keywords';
import {MaterialList} from '@/interface/tableList';
import {sendMonitor} from '@/utils/logger';
import {toOneUIRowSelectionProps} from '@/utils/selection';
import FilterList from '../../materialList/FilterList';
import {AcceptFunnelItem, sendKeywordAcceptFunnel} from '../util';
import {
    GENERATOR_KEYWORD_TYPE,
    GENERATOR_KEYWORD_TYPE_TEXT,
    FcKeywordType,
    TableKeywordItem,
    MAX_KEYWORD_COUNT,
} from './config';
import {defaultColumns} from './keywordTableConfig';
import {getKey} from './hooks/useKeywordList';
import {generateItem} from './util';
import {BufferMethodsType} from './hooks/useKeywordBufferRows';
import {QueryInputPropsType, KeywordListCommonPropsType, KeywordListSelectorPropsType} from './hooks/useKeywordList';

const getLength = (str: string) => {
    // 1中文字符2个字节，1英文字母1个字节
    return getLengthInBytes(str, [], []);
};

const DEFAULT_PAGE_SIZE = 20;
const MAX_PROMPT_LENGTH = 1000;
const MIN_PROMPT_LENGTH = 20;
const MAX_SEED_WORD_COUNT = 10;
const MAX_SEED_WORD_LENGTH = 64;

interface IKeywordQueryInputProps extends QueryInputPropsType, KeywordListCommonPropsType {
    url?: string;
    adgroupInfo?: unknown;
}

// eslint-disable-next-line complexity, max-statements
export function KeywordQueryInput({
    querys, setQuerys, prompt, setPrompt, queryText, setQueryText, textAreaErrorMsg, setTextAreaErrorMsg,
    generatorType, setGeneratorType, resetQueryAndFilters, url, hasSuggestWords,
    getRecommendList, getRecommendListLoading, getSuggestionWords, suggestWords,
    visible, on, adgroupInfo,
    landingPageUrl, setLandingPageUrl, businessPoints, setBusinessPoints,
}: IKeywordQueryInputProps) {
    const changePrompt = useCallback((value = '') => {
        setPrompt(value);
        const l = getLengthInBytes(value);
        const msg = l < MIN_PROMPT_LENGTH
            ? '为确保关键词生成效果，请至少输入20个字符'
            : l > MAX_PROMPT_LENGTH ? '不能超过1000个字符' : '';
        setTextAreaErrorMsg(msg);
    }, []);

    // 重置
    const onGeneratorTypeChange = useCallback((value: string | number) => {
        setGeneratorType(value as GENERATOR_KEYWORD_TYPE);
        resetQueryAndFilters();
    }, [resetQueryAndFilters]);

    const selectProps: SelectProps = {
        className: 'keyword-generator-top-left',
        width: 130,
        value: generatorType,
        onChange: onGeneratorTypeChange,
    };

    const urlSelectProps = {
        adgroupInfo: adgroupInfo ?? {},
        setDispalyUrl: setLandingPageUrl,
        displayUrl: landingPageUrl,
    };
    const businessProps = {
        displayBusiness: businessPoints,
        onSaveBusiness: setBusinessPoints,
    };

    useEffect(() => {
        if (url) {
            getKeywordPromptByUrl({url}).then(res => {
                !prompt && changePrompt(res);
                res && sendMonitor('click', {
                    field: 'gen_kw_url_prompt',
                    'extra_params': res,
                });
            });
        }
    }, [url]);

    // 种子词的填写
    const onMultiLabelInputChange = useCallback(e => {
        const newLabels = e.value;
        if (e.errorMsg?.length && !e.errorMessage.includes(';')) {
            return;
        }
        setQuerys(newLabels.map((i: {label: string}) => i.label));

        // 如果是新增querys，则需要把queryText清空
        if (newLabels.length > querys.length) {
            setQueryText('');
        }

    }, [querys]);

    const onAddSeedWord = useCallback((seedWord: string) => {
        if (getLengthInBytes(seedWord) > MAX_SEED_WORD_LENGTH) {
            Toast.error({
                content: `种子词长度不能超过${MAX_SEED_WORD_LENGTH}个字符`,
            });
            return;
        }

        if (querys.length >= MAX_SEED_WORD_COUNT) {
            Toast.error({
                content: `最多添加${MAX_SEED_WORD_COUNT}个种子词`,
            });
            return;
        }

        if (querys.includes(seedWord)) {
            Toast.error({
                content: '已添加该词，请勿重复添加',
            });
            return;
        }

        setQuerys([...querys, seedWord]);
    }, [querys]);

    const labels = useMemo(() => querys.map(label => ({label})), [querys]);

    const validator = useCallback(value => {
        if (querys.length && querys.includes(value)) {
            return '已添加该词';
        }
        return false;
    }, [querys]);

    const onQueryTextChange = useCallback((e: {value: string}) => {
        setQueryText(e.value);
    }, []);


    const isGenerateBtnDisabled = (
        generatorType === GENERATOR_KEYWORD_TYPE.PROMPT
            ? (!prompt || !!textAreaErrorMsg)
            : (!querys.length && !queryText)
    ) && !landingPageUrl && !businessPoints?.length;

    return (
        <>
            <div className="keyword-generator-query-words-input">
                <Select {...selectProps}>
                    <Select.Option value={GENERATOR_KEYWORD_TYPE.SEEDWORD}>
                        {GENERATOR_KEYWORD_TYPE_TEXT[GENERATOR_KEYWORD_TYPE.SEEDWORD]}
                    </Select.Option>
                    <Select.Option value={GENERATOR_KEYWORD_TYPE.PROMPT}>
                        {GENERATOR_KEYWORD_TYPE_TEXT[GENERATOR_KEYWORD_TYPE.PROMPT]}
                    </Select.Option>
                </Select>
                <>
                    {
                        generatorType === GENERATOR_KEYWORD_TYPE.SEEDWORD ? (
                            <MultiLabelInput
                                className="seed-word-input"
                                labels={labels}
                                placeholder="请输入种子词后回车确认，多个词以；分隔"
                                withSearchBtn={false}
                                style={{
                                    height: 58,
                                    borderRadius: 5,
                                }}
                                validater={validator}
                                tagMaxLen={MAX_SEED_WORD_LENGTH}
                                getLength={getLength}
                                maxLabelNum={MAX_SEED_WORD_COUNT}
                                onChange={onMultiLabelInputChange}
                                onInputChange={onQueryTextChange}
                            />
                        ) : null
                    }
                    {
                        generatorType === GENERATOR_KEYWORD_TYPE.PROMPT ? (
                            <div className="prompt-input" style={{fontSize: 0}}>
                                <TextArea
                                    minRows={2}
                                    maxRows={2}
                                    countMode="cn"
                                    value={prompt}
                                    maxLen={MAX_PROMPT_LENGTH}
                                    minLen={MIN_PROMPT_LENGTH}
                                    errorMessage={textAreaErrorMsg}
                                    onChange={e => changePrompt(e.value)}
                                    placeholder="请描述您本次推广业务的重点信息，如：我要推广的是新能源汽车，这款汽车的类型、品牌和厂商是什么。"
                                    style={{
                                        width: '100%',
                                        resize: 'none',
                                    }}
                                    errorLocation="layer"
                                />
                            </div>
                        ) : null
                    }
                </>
                {!visible && (
                    <OneButton
                        type="text-strong"
                        size="medium"
                        icon={<IconPlus />}
                        className="kr-keyword-url-and-business"
                        onClick={on}
                    >
                        使用落地页或业务拓词
                    </OneButton>
                )}
                {
                    visible ? (
                        <div className="kr-keyword-url-and-business-expand">
                            <UrlAndBusiness {...urlSelectProps} />
                            <BusinessExpandKeyword {...businessProps} />
                        </div>
                    ) : null
                }
            </div>
            <Button
                variant="cta"
                style={{width: '100%', marginTop: 12}}
                onClick={() => {
                    sendMonitor('click', {
                        field: `gen_kw_${generatorType}`,
                        'extra_params': generatorType === GENERATOR_KEYWORD_TYPE.PROMPT ? prompt : querys.join(','),
                    });
                    getRecommendList();
                    getSuggestionWords();
                }}
                loading={getRecommendListLoading}
                disabled={isGenerateBtnDisabled}
            >
                开始{generatorType === GENERATOR_KEYWORD_TYPE.PROMPT ? '生成' : '拓词'}
            </Button>
            {
                hasSuggestWords ? (
                    <div className="seed-recommand-container">
                        相关搜索：{
                            suggestWords.map(item => {
                                return (
                                    <span
                                        className="seed-recommand-word"
                                        key={item.keyword}
                                        onClick={() => onAddSeedWord(item.keyword)}
                                    >
                                        +{item.keyword}
                                    </span>
                                );
                            })
                        }
                    </div>
                ) : null
            }
        </>
    );
}

interface IKeywordSelectorProps extends KeywordListSelectorPropsType, KeywordListCommonPropsType {
    rows: TableKeywordItem[];
    bufferMethods: BufferMethodsType;
    columnConfiguration: MaterialList.ColumnConfiguration;
    modConfiguration: MaterialList.ModConfiguration;
    tableContentHeight: number;
}

// eslint-disable-next-line complexity, max-statements
export function KeywordSelector({
    columnConfiguration,
    modConfiguration,
    rows,
    generatorType,
    bufferMethods,
    region,
    setRegionIds,
    selectedSettings,
    setSelectedSettings,
    onDownload,
    downloadLoading,
    columns,
    handleColumnAction,
    getFilterContentByField,
    onSort,
    filters,
    filterMethods,
    selection,
    selectionOperations,
    recommendList: recommendListRaw,
    getRecommendListLoading,
    transMonitorId,
    recommendType,
    recommendStrategy,
    tableContentHeight,
    addFroms,
}: IKeywordSelectorProps) {
    const {spliceRows, fetchBidAndUpdate} = bufferMethods;

    const userId = getUserId();
    const [showGuide] = useShowKeywordGroupGuide({userId});
    const {deleteFilterByIndex, changeFilterByIndex, resetFilters, changeFilterByField} = filterMethods;

    const addToBuffer = useCallback((items: Array<Partial<FcKeywordType>>) => {
        let itemsToAdd = items.filter(item => !rows.some(row => row.keyValue === item.keyword));

        if (itemsToAdd.length > MAX_KEYWORD_COUNT) {
            // 如果超限了可以回填前3000个，并做toast提示。
            Toast.error({
                content: `最多添加${MAX_KEYWORD_COUNT}个关键词`,
            });
            itemsToAdd = itemsToAdd.slice(0, MAX_KEYWORD_COUNT - (rows?.length || 0));
        }

        const newItems = itemsToAdd.map(item => {
            return generateItem({
                keyword: item.keyword,
                addFrom: addFroms[0],
                suggestSource: item.suggestSource,
                recommendStrategy,
                price: item.recommendPrice,
                originalPrice: item.recommendPrice
            });
        });

        const {changedRowIndexes} = spliceRows({
            spliceParams: [rows.length, 0, ...newItems],
        });

        sendKeywordAcceptFunnel({
            transMonitorId,
            action: AcceptFunnelItem.acceptBtnClick,
            count: newItems.length,
            recommendType,
            recommendStrategy,
            addFroms,
        });
        const fetchBidRowIndexs: number[] = [];
        const keywords: string[] = [];
        newItems.forEach((item, index) => {
            if (item.keyValue && !item.price) {
                fetchBidRowIndexs.push(changedRowIndexes[index]);
                keywords.push(item.keyValue);
            }
        });
        fetchBidAndUpdate({indexs: fetchBidRowIndexs, keywords});
    }, [
        rows,
        spliceRows,
        fetchBidAndUpdate,
        transMonitorId,
        recommendType,
        recommendStrategy,
        addFroms,
    ]);

    const registerDrawer = useCallback(() => {
        return handleColumnAction('addToBuffer', addToBuffer);
    }, [handleColumnAction, addToBuffer]);
    useRegister(registerDrawer);

    // 如果rows已有了，那么给data加上disabled字段
    const recommendList = useMemo(() => {
        return recommendListRaw.map(item => {
            return {
                ...item,
                disabled: rows.some(row => row.keyValue === item.keyword),
                generatorType,
            };
        });
    }, [recommendListRaw, rows, generatorType]);

    const [pageNo, setPageNo] = useState(1);
    const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);

    useEffect(() => {
        setPageNo(1);
    }, [recommendListRaw]);

    const onSearch = useCallback(
        (value, {operatorValue} = {}) => {
            if (!value) {
                return;
            }
            changeFilterByField('keyword', {
                values: [value],
                operator: operatorValue || 'like',
            });
        },
        [changeFilterByField]
    );

    const searchBoxProps = {
        fieldName: '关键词',
        onSearch,
        operatorOptions: [
            {value: 'like', label: '包含'},
            {value: 'nlike', label: '不包含'},
        ],
        resetProps: {
            width: 120,
            placeholder: '包含/不包含',
            size: 'small',
        },
    };

    const regionProps = {
        onConfirmRegion: setRegionIds,
        selectedValue: region,
        footer: <KeywordFooter selectedSettings={selectedSettings} setSettings={setSelectedSettings} />,
    };
    const settingProps = {
        value: selectedSettings,
        onChange: setSelectedSettings,
    };

    const filterListProps = {
        filters,
        deleteFilterByIndex,
        changeFilterByIndex,
        getFilterContentByField,
        className: 'keyword-generator-filter-list',
    };

    const {customColumns, columnConfigs, columnCategories} = columnConfiguration;
    const customFieldsProps = {
        customColumns,
        summary: {},
        defaultColumns,
        columnConfigs,
        columnCategories,
        modConfiguration,
        isShowTransSelect: false,
        resetProps: {
            buttonProps: {
                size: 'small',
                icon: <IconAdjust />,
            },
            isUseMarkFields: false,
            buttonTitle: '',
        },
    };

    const emptyText = generatorType === GENERATOR_KEYWORD_TYPE.SEEDWORD
        ? '未能帮您找到关键词，请输入种子词重新拓词'
        : '暂未帮您生成关键词，请修改业务描述重新生成';
    const {onSelectChange, selectAll, getSelectedInfo, resetRowSelection} = selectionOperations;
    const rowSelectionProps = useMemo(
        () => toOneUIRowSelectionProps(
            {selection, onSelectChange, selectAll},
            recommendList,
            {getId: getKey, multiPageSelection: false}
        ),
        [selection, onSelectChange, selectAll, recommendList],
    );

    const tableProps: TableProps = {
        className: 'generator-table',
        columns: columns as TableProps['columns'],
        dataSource: recommendList,
        pagination: {
            showPageJumper: false, showTotal: false,
            pageNo,
            pageSize,
            size: 'small',
            total: recommendList.length,
            onPageNoChange: e => {
                setPageNo(e.target.value);
            },
            onPageSizeChange(e) {
                setPageSize(e.target.value);
                setPageNo(1);
            },
        },
        style: {
            marginTop: 12,
            borderTopLeftRadius: 6,
            borderTopRightRadius: 6,
            overflowX: 'hidden',
        },
        size: 'small',
        loading: getRecommendListLoading,
        locale: {emptyText} as TableProps['locale'],
        scroll: {
            y: `${tableContentHeight - 36}px`,
        },
        onSortClick: onSort as TableProps['onSortClick'],
        rowSelection: rowSelectionProps,
        rowKey: 'keyword',
    };

    const currentPaginationList = useMemo(() => {
        return recommendList.filter(item => selection.value.includes(item.keyword));
    }, [recommendList, selection.value]);

    const isAllDisabled = recommendList.every(item => item.disabled);
    const isCurrentPageDisabled = currentPaginationList.every(item => item.disabled);
    const {selectedCount} = getSelectedInfo();

    return (
        <>
            <div className="keyword-generator-left-top">
                <div className="operations">
                    <SearchBox {...searchBoxProps} />
                    <RegionSelector {...regionProps} />
                    <Setting {...settingProps} />
                    <Tooltip title="自定义列">
                        <div>
                            <CustomFieldsSelector {...customFieldsProps} />
                        </div>
                    </Tooltip>
                    <Tooltip title="下载所有关键词">
                        <div>
                            <Button
                                onClick={() => {
                                    onDownload();
                                    showGuide();
                                    sendMonitor('click', {
                                        field: `gen_kw_${generatorType}_download_all`,
                                    });
                                }}
                                size="small"
                                id="new-kr-download"
                                icon={<IconDownload />}
                                disabled={!recommendList.length}
                                loading={downloadLoading}
                            />
                        </div>
                    </Tooltip>
                    <OneButton
                        type="text-strong"
                        size="small"
                        disabled={isAllDisabled}
                        onClick={() => {
                            sendMonitor('click', {field: `gen_kw_${generatorType}_accept_all`});
                            addToBuffer(recommendList);
                        }}
                    >
                        {isAllDisabled ? '已添加全部' : '添加全部'}{recommendList.length}个关键词
                    </OneButton>
                </div>
            </div>
            <div style={{position: 'relative'}}>
                <FilterList {...filterListProps}>
                    <OneButton type="text-strong" onClick={resetFilters}>清空</OneButton>
                </FilterList>
                {/* 批量条 */}
                {
                    !!selectedCount && (
                        <BatchToolbar
                            className="batch-operation-bar"
                            selectedCount={selectedCount}
                            totalCount={recommendList.length}
                            onSelectAll={selectAll}
                            onClear={resetRowSelection}
                            size="small"
                            tools={(
                                <div className="batch-operations">
                                    <OneButton
                                        size="small"
                                        onClick={() => {
                                            sendMonitor('click', {field: `new_kw_${generatorType}_accept_page`});
                                            addToBuffer(currentPaginationList);
                                            resetRowSelection();
                                        }}
                                        disabled={isCurrentPageDisabled}
                                    >
                                        添加
                                    </OneButton>
                                    <OneButton
                                        size="small"
                                        onClick={() => {
                                            sendMonitor('click', {field: `new_kw_${generatorType}_accept_all`});
                                            addToBuffer(recommendList);
                                            resetRowSelection();
                                        }}
                                        disabled={isAllDisabled}
                                    >
                                        添加全部
                                    </OneButton>
                                </div>
                            )}
                        />
                    )
                }
                <Table {...tableProps} />
            </div>
        </>
    );
}
