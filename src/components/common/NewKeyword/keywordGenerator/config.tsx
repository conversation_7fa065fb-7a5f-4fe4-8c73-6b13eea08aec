import {getLengthInBytes} from '@/utils/string';
import {
    QueryWordParams,
} from '@/api/campaignOrAdgroupSelector';
import {
    etaTextValidator,
} from '@/utils/wildcard';

export enum MIX_MATCH_TYPE_ENUM {
    accurate = '1-1', // 精确匹配
    phrase = '2-1', // 短语匹配
    intelligence = '3-1', // 智能匹配
    intelligenceCore = '2-3', // 智能匹配-核心词
}

export const MIX_MATCH_TYP_TEXT_MAP = {
    [MIX_MATCH_TYPE_ENUM.accurate]: '精确匹配',
    [MIX_MATCH_TYPE_ENUM.phrase]: '短语匹配',
    [MIX_MATCH_TYPE_ENUM.intelligence]: '智能匹配',
    [MIX_MATCH_TYPE_ENUM.intelligenceCore]: '智能匹配',
};


export const MIX_MATCH_TYPE_OPTIONS = [
    {
        label: MIX_MATCH_TYP_TEXT_MAP[MIX_MATCH_TYPE_ENUM.accurate],
        value: MIX_MATCH_TYPE_ENUM.accurate,
    },
    {
        label: MIX_MATCH_TYP_TEXT_MAP[MIX_MATCH_TYPE_ENUM.phrase],
        value: MIX_MATCH_TYPE_ENUM.phrase,
    },
    {
        label: MIX_MATCH_TYP_TEXT_MAP[MIX_MATCH_TYPE_ENUM.intelligence],
        value: MIX_MATCH_TYPE_ENUM.intelligence,
    },
];


export function matchTypeCustomTarget(value: MIX_MATCH_TYPE_ENUM) {
    return (
        <span>
            <span style={{
                color: '#848B99',
                fontSize: 12,
                marginRight: 12,

            }}
            >
                匹配方式:
            </span>
            <span>{MIX_MATCH_TYP_TEXT_MAP[value]}</span>
        </span>
    );
}


export enum GENERATOR_KEYWORD_TYPE {
    PROMPT = 'prompt',
    SEEDWORD = 'seedword',
}

export enum KeywordAddFrom {
    WORD_VALUE = 151, // 新建单元 - 种子词
    URL_VALUE = 152, // 新建单元 - 落地页
    BUSINESS_VALUE = 153, // 新建单元 - 业务拓词
    QINGGE_PROMPT = 154, // 新建单元 - 轻舸prompt拓词
    DEFAULT_TOOL = 155, // 关键词规划师 - 默认推荐
    DEFAULT = 156, // 新建单元 - 默认推荐
    WORD_VALUE_TOOL = 181, // 关键词规划师 - 种子词
    URL_VALUE_TOOL = 182, // 关键词规划师 - 落地页
    BUSINESS_VALUE_TOOL = 183, // 关键词规划师 - 业务拓词
    WORD_VALUE_BOT = 171, // 智能助手 - 种子词
    URL_VALUE_BOT = 172, // 智能助手 - 落地页
    BUSINESS_VALUE_BOT = 173 // 智能助手 - 业务拓词
}

export const keywordAddFromMap = {
    [GENERATOR_KEYWORD_TYPE.PROMPT]: KeywordAddFrom.QINGGE_PROMPT,
    [GENERATOR_KEYWORD_TYPE.SEEDWORD]: KeywordAddFrom.WORD_VALUE,
};


const addFromConfig = {
    bot: {
        wordQuerys: KeywordAddFrom.WORD_VALUE_BOT,
        businessPoints: KeywordAddFrom.BUSINESS_VALUE_BOT,
        urlQuerys: KeywordAddFrom.URL_VALUE_BOT,
        promptQuerys: KeywordAddFrom.QINGGE_PROMPT,
    },
    manageCenter: {
        defaultRecommend: KeywordAddFrom.DEFAULT,
        wordQuerys: KeywordAddFrom.WORD_VALUE,
        businessPoints: KeywordAddFrom.BUSINESS_VALUE,
        urlQuerys: KeywordAddFrom.URL_VALUE,
        promptQuerys: KeywordAddFrom.QINGGE_PROMPT,
    },
    toolsCenter: {
        defaultRecommend: KeywordAddFrom.DEFAULT_TOOL,
        wordQuerys: KeywordAddFrom.WORD_VALUE_TOOL,
        businessPoints: KeywordAddFrom.BUSINESS_VALUE_TOOL,
        urlQuerys: KeywordAddFrom.URL_VALUE_TOOL,
        promptQuerys: KeywordAddFrom.QINGGE_PROMPT,
    },
};

export const getAddFrom = ({queryParams, isDefaultRecommend}: {
    queryParams: Omit<
        QueryWordParams, 'addFroms' | 'recommendType' | 'recommendStrategy' | 'transMonitorId'
    >;
    isDefaultRecommend: boolean;
}) => {
    const isManageCenter = window.location.pathname.includes('manageCenter');
    const isToolsCenter = window.location.pathname.includes('toolsCenter');
    const isCopilotBot = window.location.search.includes('bot=copilot');
    // 种子词、落地页、业务点三种虽然能够排列组合，但是组合的数量过多不方便管理。
    // 因此，采取优先级的方式，默认推荐 > 种子词 > prompt > 业务点 > 落地页，维护3种枚举。
    const {prompt, businessPoints, querys, displayUrl} = queryParams;
    const getQueryType = () => {
        if (isDefaultRecommend) {
            return 'defaultRecommend';
        }
        if (querys?.length) {
            return 'wordQuerys';
        }
        if (prompt) {
            return 'promptQuerys';
        }
        if (businessPoints?.length) {
            return 'businessPoints';
        }
        if (displayUrl) {
            return 'urlQuerys';
        }
        return null;
    };

    const queryType = getQueryType();
    if (!queryType) {
        return null;
    }

    // 原来的逻辑，只有toolcenter的colilot才有addfrom
    if (isCopilotBot) {
        return isToolsCenter ? addFromConfig.bot[queryType] : null;
    }

    return isManageCenter ? addFromConfig.manageCenter[queryType]
        : isToolsCenter ? addFromConfig.toolsCenter[queryType]
            : null;
};

export interface KeywordApiItem {
    keyword: string;
    price: number;
    matchType: string;
    phraseType: string;
}

export interface FcKeywordType {
    keyword: string;
    price?: number;
    originalPrice?: number;
    addFrom?: KeywordAddFrom;
    matchType?: string;
    wmatchprefer: number;
    phraseType: string;
    mixMatchType?: MIX_MATCH_TYPE_ENUM;
    suggestSource?: number;
    recommendStrategy?: string;
    recommendPrice?: number; // 点击指导价
}

export interface TableKeywordItem {
    id: string;
    keyValue: string;
    price?: number;
    originalPrice?: number;
    addFrom?: KeywordAddFrom;
    suggestSource?: number;
    disabled: boolean;
    error?: string;
    errorCode?: number;
    levelMessage?: string;
    mixMatchType?: MIX_MATCH_TYPE_ENUM;
    selected: boolean;
    recommendStrategy?: string;
    isFromUpload?: boolean;
}

export function normalizeTableToKeyWord(
    keyword: TableKeywordItem,
    extra: Pick<FcKeywordType, 'phraseType' | 'wmatchprefer' | 'matchType'> & Partial<FcKeywordType>
): FcKeywordType {
    return {
        keyword: keyword.keyValue,
        price: keyword.price,
        addFrom: keyword.addFrom,
        suggestSource: keyword.suggestSource,
        ...extra,
    };
}


export const GENERATOR_KEYWORD_TYPE_TEXT = {
    [GENERATOR_KEYWORD_TYPE.PROMPT]: '关键词生成',
    [GENERATOR_KEYWORD_TYPE.SEEDWORD]: '种子词拓词',
};

export const MAX_KEYWORD_COUNT = 3000;

export const bufferValidator = {
    keyword: (keyword: string) => {
        const value = keyword.trim();
        const valueLen = getLengthInBytes(value);
        const etaTextError = etaTextValidator(value, '关键词');
        if (valueLen > 40) {
            return '关键词长度不能超过40个字符';
        }
        if (etaTextError) {
            return etaTextError;
        }
    },
    price: (price: number) => {
        if (price && (price < 0.01 || price > 999.99)) {
            return '范围：0.01 - 999.99';
        }
    },
};
