// @ts-check
import {useBoolean} from 'huse';
import {useCallback, useMemo, useState} from 'react';
import {Popover, Dropdown} from '@baidu/one-ui';
import MatchType from './editor/MatchType';
import Price from './editor/Price';

const popoverContentMap = {
    price: Price,
    matchType: MatchType
};
const batchOptionsConfigMap = {
    price: {
        value: 'price',
        label: '修改点击出价'
    },
    matchType: {
        value: 'matchType',
        label: '修改匹配模式'
    }
};
const BATCH_OPTION = ['matchType'];

const BatchOperation = ({
    onCheckAll,
    isShowPrice,
    onBatchMatchType,
    onBatchPrice,
    resetRecommendPrice
}: {
    onCheckAll: (isCheckAll: boolean) => void;
    isShowPrice: boolean;
    onBatchPrice: (value: number) => void;
    onBatchMatchType: ({key}: {key: string}) => void;
    resetRecommendPrice: () => void;
}) => {
    const [visible, {on, off, toggle}] = useBoolean();
    const [column, setColumn] = useState('');
    const handleOptionChange = e => {
        setColumn(e.key);
        on();
    };
    const datasource = useMemo(() => {
        const dynamic = [];
        if (isShowPrice) {
            dynamic.push('price');
        }
        return BATCH_OPTION.concat(dynamic).map(column => batchOptionsConfigMap[column]);
    }, [isShowPrice]);
    const dropdownProps = {
        options: datasource,
        title: '批量编辑',
        handleMenuClick: handleOptionChange,
        trigger: ['click'],
        getPopupContainer: () => document.getElementsByClassName('batch-operation-popover')[0],
        size: 'small',
        disabled: visible,
    };
    const DisplayContent = popoverContentMap[column];
    const reset = useCallback(() => {
        onCheckAll(false);
        off();
    }, [off, onCheckAll]);
    const editorProps = {
        off,
        reset,
        onBatchMatchType,
        onBatchPrice,
        resetRecommendPrice
    };
    return (
        <div className="batch-operation-popover">
            <Popover
                placement="bottomLeft"
                trigger="click"
                visible={visible}
                onVisibleChange={toggle}
                content={<DisplayContent {...editorProps} />}
            >
                <Dropdown.Button {...dropdownProps} />
            </Popover>
        </div>
    );
};

export default BatchOperation;
