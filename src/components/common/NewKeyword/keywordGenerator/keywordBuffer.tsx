import {Form, NumberInput, Button as OneButton, Popover, ProviderConfig, Dropdown, Dialog} from '@baidu/one-ui';
import {TextTable} from '@baidu/one-ui-pro';
import {useMemo, useCallback} from 'react';
import {IconZoomIn, IconZoomOut} from 'dls-icons-react';
import {useBoolean, useElementSize} from 'huse';
import classNames from 'classnames';
import {sendMonitor} from '@/utils/logger';
import {UploadKeywordsButton} from '../uploadKeywords';
import BatchOperation from './BatchOperation';
import {bufferValidator, MAX_KEYWORD_COUNT, MIX_MATCH_TYPE_ENUM, TableKeywordItem} from './config';
import EditBid from './editBid';
import {generateItem} from './util';
import {BufferMethodsType} from './hooks/useKeywordBufferRows';
import MatchTypeRender from './columns/matchType';

const getTextTableProps = ({
    isShowPrice, spliceRows, updateRow, onToppingError,
    fetchBidAndUpdateDebounce, preValidate,
    fetchBidAndUpdate, onSelectChange, onCheckAll, onModValueByIndex,
    seletedIndex, onBatchDelete: onBatchDeleteFn, rows
}: {
    isShowPrice: boolean;
    seletedIndex: number[];
    rows: TableKeywordItem[];
} & BufferMethodsType) => {

    const resetRecommendPrice = () => {
        seletedIndex.map(index => {
            return updateRow({
                index,
                value: {
                    price: rows[index].originalPrice,
                    error: ''
                }
            });
        });
    };
    const onBatchMatchType = (e: {key: number | string}) => {
        const value = e.key;
        onModValueByIndex(seletedIndex, {mixMatchType: value, error: ''});
        onCheckAll(false);
        sendMonitor('click', {source: 'new_keyword', target: 'on_batch_edit_buffer_match_type'});
    };

    const onBatchPrice = (value: number) => {
        onModValueByIndex(seletedIndex, {price: value, error: ''});
        onCheckAll(false);
        sendMonitor('click', {source: 'new_keyword', target: 'on_batch_edit_buffer_price'});
    };
    const onBatchDelete = () => {
        onBatchDeleteFn(seletedIndex);
        onCheckAll(false);
    };
    const batchOperationProps = {
        onCheckAll,
        isShowPrice,
        onBatchMatchType,
        onBatchPrice,
        resetRecommendPrice
    };
    return {
        title: '关键词',
        placeholder: '请输入或粘贴',
        selectable: true,
        onSelectChange,
        onCheckAll,
        // selectedRowKeys: [],
        maxLen: 40,
        maxLine: MAX_KEYWORD_COUNT,
        width: 100,
        customColumns: [
            {
                title: '匹配模式',
                className: 'text-table-match-type',
                render: (record: TableKeywordItem, index: number) => {
                    const {keyValue, mixMatchType} = record;
                    return (
                        <MatchTypeRender
                            keyValue={keyValue}
                            mixMatchType={mixMatchType}
                            onModMatchType={({index, value}: {index: number, value: any}) => {
                                updateRow({
                                    index,
                                    value: {
                                        mixMatchType: value,
                                        error: ''
                                    },
                                });
                            }}
                            index={index}
                        />
                    );
                }
            },
            ...(
                isShowPrice ? [{
                    title: '点击出价',
                    className: 'text-table-price',
                    render: (record: TableKeywordItem, index: number) => {

                        if (!record.keyValue) {
                            return null;
                        }

                        return (
                            <EditBid
                                price={record.price}
                                index={index}
                                onModBid={({index, value}) => {
                                    const error = bufferValidator.price(value);
                                    updateRow({
                                        index,
                                        value: {
                                            price: value,
                                            error,
                                        },
                                    });
                                }}
                            />
                        );
                    },
                }] : []
            ),
        ],
        onInputChange: (values: string[], options: [
            deleteIndex: number,
            deleteCount: number,
            ...addValues: string[]
        ]) => {
            const [index, count, value] = options;
            const error = bufferValidator.keyword(value);
            updateRow({
                index,
                value: {
                    keyValue: value,
                    mixMatchType: MIX_MATCH_TYPE_ENUM.intelligenceCore,
                    error,
                },
            });
            isShowPrice && fetchBidAndUpdateDebounce({indexs: [index], keywords: [value]});
        },
        // 敲回车的回调，作用是加入新的一行
        onRowChange: async (values: string[], options: [
            deleteIndex: number,
            deleteCount: number,
            ...addValues: string[]
        ]) => {

            const [deleteIndex, deleteCount, ...addValues] = options;
            const indexArray: number[] = [];
            const items = addValues.map((text, i) => {
                indexArray.push(deleteIndex + i);
                const newRows = generateItem({keyword: text});
                return newRows;
            });

            spliceRows({
                spliceParams: [deleteIndex, deleteCount, ...items],
            });

        },
        batchOperation: [
            <BatchOperation key="operation" {...batchOperationProps} />,
            <OneButton
                key="delete"
                size="small"
                type="normal"
                onClick={onBatchDelete}
            >
                删除
            </OneButton>,
        ],
        // 粘贴
        onPaste: async (
            _: string[], options: [
                deleteIndex: number,
                deleteCount: number,
                ...addValues: string[]
            ]
        ) => {
            const [deleteIndex, deleteCount, ...rows] = options;
            const indexArray: number[] = [];
            const items = rows.map((text: string, i) => {
                const error = bufferValidator.keyword(text);
                indexArray.push(deleteIndex + i);
                return {
                    ...generateItem({
                        keyword: text,
                    }),
                    error,
                };
            });
            const {changedRowIndexes} = spliceRows({
                spliceParams: [deleteIndex, deleteCount, ...items],
            });
            const keywords = items.map(item => item.keyValue);
            fetchBidAndUpdate({indexs: changedRowIndexes, keywords});
            sendMonitor('click', {source: 'new_keyword', target: 'on_paste_keywords_to_buffer', count: items.length});
        },
        // 删除
        onDelete: (_: string[], deleteIndex: number[]) => {
            spliceRows({
                spliceParams: [deleteIndex[0], deleteIndex.length],
            });
        },
        // 查看错误
        onToppingError: (
            values: string[],
            errorIndexArray: number[],
        ) => {
            onToppingError({errorIndexArray});
        },
        // 失去焦点
        onBlur: (
            e: React.FocusEvent<HTMLInputElement> & {target: HTMLInputElement & {dataset: {index: string}}},
            hasChanged: boolean) => {
            hasChanged && preValidate();
        },
    };
};


interface KeywordBufferProps {
    rows: TableKeywordItem[];
    bufferMethods: BufferMethodsType;
    campaignId?: number;
    adgroupId?: number;
    isShowPrice: boolean;
}

export function KeywordBuffer({
    bufferMethods,
    rows,
    isShowPrice,
}: KeywordBufferProps) {

    const [ref, size] = useElementSize();
    const textTableHeight = size?.height ? size.height - 37 : 300;

    // 已选择关键词列表
    const {
        preValidate,
        fetchBidAndUpdate,
        fetchBidAndUpdateDebounce,
        spliceRows,
        onToppingError,
        updateRow,
        onSelectChange,
        onBatchDelete,
        onCheckAll,
        onModValueByIndex,
    } = bufferMethods;

    const seletedIndex = useMemo(() => {
        return rows.reduce<number[]>((acc, item, index) => {
            if (item.selected) {
                acc.push(index);
            }
            return acc;
        }, []);
    }, [rows]);

    const textTableProps = useMemo(() => getTextTableProps({
        spliceRows,
        updateRow,
        onToppingError,
        fetchBidAndUpdate,
        fetchBidAndUpdateDebounce,
        preValidate,
        isShowPrice,
        onSelectChange,
        onCheckAll,
        onModValueByIndex,
        seletedIndex,
        onBatchDelete,
        rows
    }), [
        updateRow, spliceRows, onToppingError, onSelectChange, onCheckAll,
        fetchBidAndUpdate, preValidate, isShowPrice, fetchBidAndUpdateDebounce,
        onModValueByIndex, seletedIndex, onBatchDelete, rows
    ]);

    const [keywordBufferVisible, {on: onOpenKeywordBuffer, off: onOffKeywordBuffer}] = useBoolean(false);

    const onClickClearAll = () => {
        spliceRows({
            spliceParams: [0, rows.length, generateItem()],
        });
    };

    const isBatchBtnDisabled = rows.length === 1 && !rows[0].keyValue || !rows.length;

    const cls = classNames('keyword-generator-right', {'is-show-price': isShowPrice});

    const addUploadKeywordsToBuffer = useCallback(
        (keywords: any[] = []) => {
            // let itemsToAdd = keywords.filter(item => !rows.some(row => row.keyValue === item.keyword));
            const newItems = keywords.map(generateItem).map(item => ({...item, isFromUpload: true}));
            spliceRows({spliceParams: [rows.length, 0, ...newItems]});
        },
        [rows, spliceRows]
    );

    return (
        <div className={cls} ref={ref}>
            <div className="keyword-generator-right-top">
                <div>
                    <span className="keyword-generator-right-top-title">
                        已选（{rows.length}/{MAX_KEYWORD_COUNT}）
                    </span>
                    <UploadKeywordsButton
                        addUploadKeywordsToBuffer={addUploadKeywordsToBuffer}
                        isShowPrice={isShowPrice}
                    />
                </div>
                <div className="keyword-generator-right-top-operations" style={{position: 'relative'}}>
                    <OneButton
                        disabled={isBatchBtnDisabled}
                        type="text-strong"
                        size="small"
                        onClick={onClickClearAll}
                    >
                        清空
                    </OneButton>
                    <OneButton
                        icon={IconZoomIn}
                        type="text-strong"
                        size="small"
                        onClick={onOpenKeywordBuffer}
                    >
                        放大
                    </OneButton>
                </div>
            </div>
            {/* 先注释，后面的需求马上还要加上height: textTableHeight */}
            <div style={{overflowY: 'auto'}}>
                <TextTable
                    {...textTableProps}
                    dataSource={rows}
                    className="text-table"
                    contentHeight={textTableHeight - 85}
                />
            </div>
            <Dialog
                title={
                    <div>
                        已选关键词（{rows.length}/{MAX_KEYWORD_COUNT}）
                    </div>
                }
                visible={keywordBufferVisible}
                footer={[]}
                destroyOnClose
                width={1110}
                needCloseIcon={false}
                className="kr-keyword-buffer-enlarge-dialog"
            >
                <div className="kr-keyword-buffer-enlarge-dialog-content">
                    <OneButton
                        icon={IconZoomOut}
                        type="text-strong"
                        size="small"
                        onClick={onOffKeywordBuffer}
                        className="shrink"
                    >缩小
                    </OneButton>
                    <div style={{overflowY: 'auto'}}>
                        <TextTable
                            {...textTableProps}
                            dataSource={rows}
                            className="text-table"
                            contentHeight={textTableHeight - 85}
                        />
                    </div>
                </div>
            </Dialog>
        </div>
    );
}


