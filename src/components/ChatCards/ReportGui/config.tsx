/* eslint-disable max-lines */
import {lazy} from 'react';
import {isEqual} from 'lodash-es';
import {Button, Typography} from '@baidu/light-ai-react';
import {IconAppsSettings} from 'dls-icons-react';
import {getDisplayInfo} from 'commonLibs/utils/creativePreview';
import CreativePreview from 'commonLibs/CreativePreview';
import {timeUnitOptions, timeUnitOptionsWithHour, ReportType, PRODUCT_TYPE_CONFIG} from '@/config/report';
import {isVideoBlockUser} from '@/utils/getFlag';
import {Instructions} from '@/utils/instructions';
import {RecordProps} from '@/utils/handleSummary';
import {getMinuteStatus, getTargetDateRealtimeIp} from '@/utils/report';
import {PageType} from '@/dicts/pageType';
import {ImageColumn} from '@/components/ChatCards/Table/ImageReport/columns/image';
import {QueryWordColumn} from '@/components/ChatCards/Table/QueryWordReport/columns/queryWord';
import {TitleColumn} from '@/components/ChatCards/Table/TextReport/columns/title';
import {VideoColumn} from '@/components/ChatCards/Table/VideoReport/columns/video';
import WInfoNameStatus from '@/components/ChatCards/Table/MarketPointReport/columns/wInfoNameStatus';
import {FetchParams, FilterItem} from '@/interface/report';
import {fetchQueryWordsIsInclude} from '@/api/getReportData';
import {InvalidClickInlineOpt} from '@/components/ChatCards/ReportGui/InvalidClickInlineOpt';
import {QueryInlineOpt} from '@/components/ChatCards/ReportGui/QueryInlineOpt';
import {videoIdeaTypeNameMap, videoIdeaTypeValues} from '@/components/AiReportSDK/reportController/pureConfig';
import {useAdRoute} from '../../../modules/Ad/routes';
import {QueryWordColumnWithFeedback} from './QueryWordColumnWithFeedback';
import {CampaignShareBudget} from './table/realTimeReportColumns/shareBudget';

const RealtimeDashboard = lazy(() => import('../Dashboard/realtime'));
const InvalidClickDashboard = lazy(() => import('../Dashboard/invalidClick'));
const InvalidClueDashboard = lazy(() => import('../Dashboard/invalidClue'));
const RealtimeChart = lazy(() => import('../Chart/RealtimeChart'));
const BatchCreativeVideo = lazy(() => import('./table/batchOperation/creativeVideo'));
const OfflineTimeRender = lazy(() => import('./table/realTimeReportColumns/offlineTimeRender'));
const RealTimeReportBid = lazy(() => import('./table/realTimeReportColumns/realTimeReportBid'));

export type ReportTypeConfig = {
    [key in ReportType]: {
        /**
         * 报表名称
         */
        name: string;
        /**
         * 是否有summary行，默认false
         */
        hasSummary?: boolean;
        /**
         * 分时选择options
         */
        timeUnitOptions?: typeof timeUnitOptions | typeof timeUnitOptionsWithHour;
        /**
         * 是否有项目筛选器，默认false
         */
        showProjectFilter?: boolean;
        /**
         * 是否有产品线筛选器，默认false
         */
        showProductFilter?: boolean;
        /**
         * 产品线选择器是否互斥（如图片报告不能同时筛选搜索+信息流），默认false
         */
        productFilterConflict?: boolean;
        /**
         * 是否有计划筛选器，默认true
         */
        showCampaignFilter?: boolean;
        /**
         * 是否有日期选择器，默认true
         */
        showDatePicker?: boolean;
        /**
         * 是否有创意类型选择，默认false
         */
        showVideoIdeaTypeFilter?: boolean;
        /**
         * 是否有项目类型：标准版/极速版
         */
        showProjectTypeFilter?: boolean;
        /**
         * 是否有设备筛选器，默认false
         */
        showDeviceFilter?: boolean;
        /**
         * 是否有下载，默认true
         */
        showDownload?: boolean;
        /**
         * 是否有细分功能，默认false
         */
        supportSplitCascader?: boolean;
        /**
         * 固定最大行数限制，比如搜索词报告最多只展示TopN，其他的长尾数据不披露
         */
        maxRowCountLimit?: number;
        /**
         * 自定义列渲染
         */
        customColumnRender?: ((r: any) => Record<string, any>) | Record<string, any>;
        /**
         * 自定义渲染
         */
        customCardRender?: Record<string, React.FC<any>>;
        /**
         * 自定义filters
         */
        customFilterConfig?: {
            getFilters: () => {
                value: FilterItem[];
                label?: string;
            };
        };
        tableProps?: {
            rowKey?: string | ((record: RecordProps) => string);
            operationBar?: any;
            showBatchOperationBar?: (props?: any) => boolean;
            getCheckBoxEnabled?: (record: any) => boolean;
        };
        extraInfoRequest?: (params: any) => Promise<any>;
        /**
         * 额外支持的产品线，比如爱采购
         */
        supportProductList?: PRODUCT_TYPE_CONFIG[];
        /**
         * 支持筛选来源,仅账户整体报告、营销方案报告才有 默认false
         * 自定义fields
         */
        customerFields?: object[];
        /**
         * 需要额外请求field
         */
        customRequestFields?: string[];
        /**
         * 是否需要构造虚拟的id，用于表格的选中
         */
        needVirtualId?: boolean;
        /**
         * 自定义的操作栏内容
         */
        customOperations?: React.FC;
        /**
         * 对应 GUI 的路由
         */
        pageType?: PageType;
    }
};

const ACCOUNT_REPORT_COMMOM_CONFIG = {
    name: '账户整体报告',
    hasSummary: true,
    showProductFilter: true,
    showCampaignFilter: false,
    timeUnitOptions: timeUnitOptionsWithHour,
    supportProductList: [
        PRODUCT_TYPE_CONFIG.ACCOUNT, PRODUCT_TYPE_CONFIG.FC, PRODUCT_TYPE_CONFIG.FEED,
        PRODUCT_TYPE_CONFIG.B2B_PROMOTION, PRODUCT_TYPE_CONFIG.FC_CPL, PRODUCT_TYPE_CONFIG.FEED_CPL,
        PRODUCT_TYPE_CONFIG.RFQ,
    ],
    productFilterConflict: true,
    pageType: PageType.AccountReport,
};

const CAMPAIGN_REPORT_COMMON_CONFIG = {
    name: '营销方案报告',
    hasSummary: true,
    showProductFilter: true,
    timeUnitOptions: timeUnitOptionsWithHour,
    supportProductList: [
        PRODUCT_TYPE_CONFIG.FC, PRODUCT_TYPE_CONFIG.FEED, PRODUCT_TYPE_CONFIG.B2B_PROMOTION, PRODUCT_TYPE_CONFIG.FC_CPL,
        PRODUCT_TYPE_CONFIG.FEED_CPL,
    ],
    productFilterConflict: true,
    pageType: PageType.CampaignReport,
};

const PROJECT_REPORT_COMMON_CONFIG = {
    name: '项目报告',
    hasSummary: true,
    showProductFilter: true,
    timeUnitOptions: timeUnitOptionsWithHour,
    supportProductList: [PRODUCT_TYPE_CONFIG.FC, PRODUCT_TYPE_CONFIG.FEED],
    showProjectFilter: true,
    showCampaignFilter: false,
    productFilterConflict: true,
    pageType: PageType.ProjectReport,
};

const QUERY_WORD_REPORT_COMMON_CONFIG = {
    name: '搜索词报告',
    hasSummary: true,
    showProductFilter: true,
    timeUnitOptions: timeUnitOptions,
    productFilterConflict: true,
    supportProductList: [PRODUCT_TYPE_CONFIG.FC, PRODUCT_TYPE_CONFIG.B2B_PROMOTION],
    maxRowCountLimit: 150,
    pageType: PageType.QueryWordReport,
    extraInfoRequest: fetchQueryWordsIsInclude,
    customColumnRender: ({instructions, guiReport}: {instructions: Instructions, guiReport: boolean}) => ({
        queryWord: {
            title: '搜索词',
            dataIndex: 'queryWord',
            key: 'queryWord',
            width: 220,
            render: (value: any, record: any) => {
                if (!value) {
                    return '-';
                }
                const {queryWord, status} = record;
                if (guiReport) {
                    return <QueryWordColumnWithFeedback queryWord={queryWord} data={record} />;
                }
                return <QueryWordColumn status={status} queryWord={queryWord} instructions={instructions} />;
            },
        },
        ideaInfo: {
            title: '创意',
            dataIndex: 'ideaInfo',
            key: 'ideaInfo',
            render: (value: string, record: Record<string, any>) => {
                if (!value) {
                    return '-';
                }
                const info = getDisplayInfo(record.ideaInfo);
                const {
                    title, description1, description2, pcDisplayUrl,
                    mobileDisplayUrl, portraitPicUrl = '', segmentBinds,
                } = info;
                const previewProps = {
                    title,
                    description1,
                    description2,
                    mobileDisplayUrl: mobileDisplayUrl,
                    pcDisplayUrl: pcDisplayUrl,
                    segmentBinds,
                    portraitPicUrl,
                };
                return <CreativePreview {...previewProps} />;
            },
        },
        wInfoNameStatus: {
            render: (value: string) => (value && <WInfoNameStatus text={value} />) || '-',
        },
    }),
};

function FeedVideoCustomOperation() {
    const {linkTo} = useAdRoute();
    return (
        <Button
            variant="strong"
            icon={<IconAppsSettings />}
            onClick={() => linkTo('/ad/manageCenter/creativeList/dynamicContentBlock')}
        >
            管理创意视频
        </Button>
    );
}

export const GUI_REPORT_CONFIG: ReportTypeConfig = {
    // 全账户整体消费数据
    [ReportType.ACCOUNT_ASSEMBLE]: ACCOUNT_REPORT_COMMOM_CONFIG,
    // 账户整体报表- 搜索
    [ReportType.FC_ACCOUNT]: ACCOUNT_REPORT_COMMOM_CONFIG,
    // 账户整体报告- 信息流
    [ReportType.FEED_ACCOUNT]: ACCOUNT_REPORT_COMMOM_CONFIG,
    [ReportType.FC_CPL]: ACCOUNT_REPORT_COMMOM_CONFIG,
    [ReportType.FEED_CPL]: ACCOUNT_REPORT_COMMOM_CONFIG,
    [ReportType.ACG_ACCOUNT]: ACCOUNT_REPORT_COMMOM_CONFIG,
    [ReportType.RFQ_ACCOUNT]: ACCOUNT_REPORT_COMMOM_CONFIG,

    // 营销方案报告- 搜索
    [ReportType.FC_CAMPAIGN]: CAMPAIGN_REPORT_COMMON_CONFIG,
    // 营销方案报告- 信息流
    [ReportType.FEED_CAMPAIGN]: CAMPAIGN_REPORT_COMMON_CONFIG,
    // 营销方案报告- 搜索服务直达
    [ReportType.CAMPAIGN_FC_CPL]: CAMPAIGN_REPORT_COMMON_CONFIG,
    // 营销方案报告- 信息流服务直达
    [ReportType.CAMPAIGN_FEED_CPL]: CAMPAIGN_REPORT_COMMON_CONFIG,
    // 营销方案报告- 爱采购
    [ReportType.CAMPAIGN_ACG]: CAMPAIGN_REPORT_COMMON_CONFIG,

    // 项目报告- 搜索
    [ReportType.FC_PROJECT]: PROJECT_REPORT_COMMON_CONFIG,
    // 项目报告- 信息流
    [ReportType.FEED_PROJECT]: PROJECT_REPORT_COMMON_CONFIG,
    // 搜索词报告
    [ReportType.SEARCH]: {
        ...QUERY_WORD_REPORT_COMMON_CONFIG,
        needVirtualId: true,
        tableProps: {
            rowKey: 'virtualId',
            operationBar: lazy(() => import('./table/batchOperation/queryWord')),
        },
        customerFields: [{
            accessorKey: 'operate',
            field: 'operate',
            title: '操作',
            dataIndex: 'operate',
            width: 120,
            fixed: 'right',
            render: (value: string, record: any, index: number) => (index && <QueryInlineOpt record={record} />) || '-',
        }],
        customRequestFields: ['planId', 'unitId', 'queryId', 'matchType', 'phraseType', 'reWinfoType', 'userId'],
    },
    [ReportType.ACG_SEARCH]: {
        ...QUERY_WORD_REPORT_COMMON_CONFIG,
        customFilterConfig: {
            getFilters: () => {
                return {
                    value: [
                        {
                            column: 'productLine',
                            operator: 'IN',
                            values: [3],
                        },
                    ],
                };
            },
        },
    },
    // 营销要点报告
    [ReportType.MARKET_POINT]: {
        name: '营销要点报告',
        hasSummary: true,
        showProductFilter: true,
        productFilterConflict: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC],
        timeUnitOptions: timeUnitOptionsWithHour,
        pageType: PageType.MarketingPointReport,
        customColumnRender: () => ({
            wInfoNameStatus: {
                width: 150,
                render: (text: string, record: RecordProps) => {
                    const {permanent} = record;
                    return <WInfoNameStatus text={text} permanent={permanent} />;
                },
            },
        }),
        tableProps: {
            rowKey: (row: any) => {
                if (row?.wInfoId) {
                    return `${row.wInfoId}_${row.date}`;
                }
                return '';
            },
            operationBar: lazy(() => import('./table/batchOperation/marketPoint')),
        },
        customRequestFields: ['permanent'],
    },
    // 视频报告-feed
    [ReportType.VIDEO_FEED]: {
        name: '视频报告',
        showProductFilter: true,
        needVirtualId: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC, PRODUCT_TYPE_CONFIG.FEED],
        timeUnitOptions: timeUnitOptionsWithHour,
        showVideoIdeaTypeFilter: true,
        productFilterConflict: true,
        pageType: PageType.CreativeVideoReport,
        tableProps: {
            rowKey: 'virtualId',
            operationBar: BatchCreativeVideo,
            getCheckBoxEnabled(record: any) {
                return record.videoShieldStatus !== '已屏蔽'
                    && record.videoIdeaType === videoIdeaTypeNameMap.auto;
            },
            showBatchOperationBar({downloadParams}) {
                const filters = downloadParams.filters as FetchParams['filters'];
                const filter = filters!.reduce((res = {}, {column, values}) => {
                    return {
                        ...res,
                        [column]: values,
                    };
                }, {} as Record<string, any>);

                return isVideoBlockUser() && (
                    !filter.videoIdeaType || isEqual(filter.videoIdeaType, [videoIdeaTypeValues.auto])
                );
            },
        },
        customColumnRender: ({guiReport}) => ({
            videoInfo: {
                title: '视频内容预览',
                dataIndex: 'videoInfo',
                key: 'videoInfo',
                width: 130,
                render: (text: string, record: any) => {
                    const {videoInfo} = record;
                    return (
                        <VideoColumn
                            videoInfo={videoInfo}
                            supportShield={
                                guiReport
                                && isVideoBlockUser()
                                && record.videoIdeaType === videoIdeaTypeNameMap.auto
                            }
                            isShielded={
                                videoInfo.videoShieldStatus === '已屏蔽'
                            }
                        />
                    );
                },
            },
            videoIdeaType: {
                title: '素材类型',
                dataIndex: 'videoIdeaType',
                key: 'videoIdeaType',
                width: 130,
                render(value: string) {
                    if (!value) {
                        return '-';
                    }
                    const isAuto = value === videoIdeaTypeNameMap.auto;
                    const tip = isAuto
                        // eslint-disable-next-line max-len
                        ? '系统根据对用户搜索个性化需求的分析和理解，基于账户内提交的口播视频、数字人视颜进行优化或自动生成，选择与用户搜索意图最相匹配的数字人视频配置于您的创意中。自动创意视频支持屏蔽，如您对视频内容及效果不满意，可筛选自动创意类型，勾选后添加屏蔽操作，屏蔽后账户将不再投放该视频。'
                        : '为客户自行上传至投放平台的视频内容。';
                    return <Typography.Text tip={tip} size="small">{value}</Typography.Text>;
                },
            },
        }),
        customOperations: isVideoBlockUser() ? FeedVideoCustomOperation : undefined,
    },
    // 视频报告-fc
    [ReportType.VIDEO_FC]: {
        name: '视频报告',
        showProductFilter: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC, PRODUCT_TYPE_CONFIG.FEED],
        timeUnitOptions: timeUnitOptions,
        productFilterConflict: true,
        pageType: PageType.CreativeVideoReport,
        tableProps: {
            rowKey: 'id',
        },
        customColumnRender: {
            videoInfo: {
                title: '视频内容预览',
                dataIndex: 'videoInfo',
                key: 'videoInfo',
                width: 130,
                render: (text: string, record: any) => {
                    const {videoInfo} = record;
                    return <VideoColumn videoInfo={videoInfo} />;
                },
            },
        },
    },
    // 图片报告
    [ReportType.IMAGE_FC]: {
        name: '图片报告',
        showProductFilter: true,
        productFilterConflict: true,
        timeUnitOptions: timeUnitOptions,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC, PRODUCT_TYPE_CONFIG.FEED],
        pageType: PageType.CreativeImageReport,
        customColumnRender: {
            picUrl: {
                title: '图片',
                dataIndex: 'picUrlStatus',
                key: 'picUrlStatus',
                width: 130,
                render: (text: string, record: any) => {
                    return <ImageColumn {...record} />;
                },
            },
        },
    },
    [ReportType.IMAGE_FEED_FAST]: {
        name: '图片报告',
        showProductFilter: true,
        productFilterConflict: true,
        timeUnitOptions: timeUnitOptionsWithHour,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC, PRODUCT_TYPE_CONFIG.FEED],
        pageType: PageType.CreativeImageReport,
        showProjectTypeFilter: true,
        customColumnRender: {
            picUrlStatus: {
                title: '图片',
                dataIndex: 'picUrlStatus',
                key: 'picUrlStatus',
                width: 130,
                render: (text: string, record: any) => {
                    return <ImageColumn {...record} />;
                },
            },
        },
    },
    [ReportType.IMAGE_FEED_STANDARD]: {
        name: '图片报告',
        showProductFilter: true,
        productFilterConflict: true,
        timeUnitOptions: timeUnitOptionsWithHour,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC, PRODUCT_TYPE_CONFIG.FEED],
        pageType: PageType.CreativeImageReport,
        showProjectTypeFilter: true,
        customColumnRender: {
            picUrlStatus: {
                title: '图片',
                dataIndex: 'picUrlStatus',
                key: 'picUrlStatus',
                width: 130,
                render: (text: string, record: any) => {
                    return <ImageColumn {...record} />;
                },
            },
        },
    },
    // 文案报告
    [ReportType.TEXT_FC]: {
        name: '创意报告',
        showProductFilter: true,
        productFilterConflict: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC],
        pageType: PageType.CreativeTextReport,
        timeUnitOptions: timeUnitOptions,
        customColumnRender: {
            ideaInfo: {
                title: '文案',
                dataIndex: 'ideaInfo',
                key: 'ideaInfo',
                width: 210,
                render: (text: string, record: any) => {
                    return <TitleColumn {...record} />;
                },
            },
        },
    },
    [ReportType.CREATIVE_TITLE]: {
        name: '标题报告',
        showProductFilter: true,
        productFilterConflict: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC],
        timeUnitOptions: timeUnitOptions,
        pageType: PageType.CreativeTitleReport,
    },
    [ReportType.CREATIVE_DESC]: {
        name: '描述报告',
        showProductFilter: true,
        productFilterConflict: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC],
        timeUnitOptions: timeUnitOptions,
        pageType: PageType.CreativeDescReport,
    },
    [ReportType.TEXT_FEED]: {
        name: '创意报告',
        showProductFilter: true,
        productFilterConflict: true,
        timeUnitOptions: timeUnitOptions,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC, PRODUCT_TYPE_CONFIG.FEED],
        pageType: PageType.CreativeTextReport,
        customColumnRender: {
            ideaInfo: {
                title: '文案',
                dataIndex: 'ideaInfo',
                key: 'ideaInfo',
                width: 210,
                render: (text: string, record: any) => {
                    return <TitleColumn {...record} />;
                },
            },
        },
    },
    // 定向报告
    [ReportType.TARGET_REGION]: {
        name: '分地域报告',
        hasSummary: true,
        showProductFilter: true,
        timeUnitOptions: timeUnitOptionsWithHour,
        supportProductList: [PRODUCT_TYPE_CONFIG.B2B_PROMOTION],
        productFilterConflict: true,
        pageType: PageType.SplitRegionReport,
    },
    [ReportType.LIFT_BUDGET_PROJECT]: {
        name: '一键起量报告',
        hasSummary: true,
        showProductFilter: true,
        productFilterConflict: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC],
        timeUnitOptions: timeUnitOptionsWithHour,
        pageType: PageType.LiftBudgetReport,
    },
    [ReportType.LIFT_BUDGET_CAMPAIGN]: {
        name: '一键起量报告',
        hasSummary: true,
        showProductFilter: true,
        productFilterConflict: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC],
        timeUnitOptions: timeUnitOptionsWithHour,
        pageType: PageType.LiftBudgetReport,
    },
    [ReportType.INVALID_CLICK_CHART]: {
        name: '无效线索报告',
        hasSummary: false,
        showProductFilter: true,
        productFilterConflict: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC],
        timeUnitOptions: timeUnitOptions,
        customCardRender: {
            InvalidClueDashboard,
        },
        pageType: PageType.InvalidClueReport,
    },
    [ReportType.INVALID_CLUE_SYSTEM]: {
        name: '无效线索报告',
        hasSummary: false,
        showProductFilter: true,
        productFilterConflict: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC],
        timeUnitOptions: timeUnitOptions,
        customCardRender: {
            InvalidClueDashboard,
        },
        pageType: PageType.InvalidClueReport,
    },
    // [ReportType.INVALID_CLUE_REPAY]: {
    //     name: '无效线索报告',
    //     hasSummary: false,
    //     timeUnitOptions: timeUnitOptionsWithHour,
    //     customCardRender: {
    //         InvalidClueDashboard,
    //     },
    // },
    [ReportType.INVALID_CLICK_CHARGE_IP]: {
        name: '无效点击报告',
        hasSummary: false,
        showProductFilter: true,
        productFilterConflict: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC],
        timeUnitOptions: timeUnitOptions,
        pageType: PageType.InvalidClickReport,
        customCardRender: {
            InvalidClickDashboard,
        },
        customerFields: [{
            accessorKey: 'operate',
            field: 'operate',
            title: '操作',
            dataIndex: 'operate',
            width: 120,
            render: (text: string, record: any) => <InvalidClickInlineOpt record={record} />,
        }],
    },
    [ReportType.INVALID_CLICK_CHARGE_REALTIME_IP]: {
        name: '无效点击报告',
        hasSummary: false,
        showProductFilter: true,
        productFilterConflict: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC],
        timeUnitOptions: timeUnitOptionsWithHour,
        pageType: PageType.InvalidClickReport,
        customCardRender: {
            InvalidClickDashboard,
        },
        customFilterConfig: {
            getFilters: () => {
                const {start, end} = getTargetDateRealtimeIp();
                return {
                    value: [
                        {
                            column: 'dateTimeSec',
                            operator: 'GT',
                            values: [start],
                        },
                        {
                            column: 'dateTimeSec',
                            operator: 'LT',
                            values: [end],
                        },
                    ],
                    label: `时间范围：${start}至${end}`,
                };
            },
        },
        customerFields: [{
            accessorKey: 'operate',
            field: 'operate',
            title: '操作',
            dataIndex: 'operate',
            width: 120,
            render: (text: string, record: any) => <InvalidClickInlineOpt record={record} />,
        }],
    },
    // [ReportType.LANDPAGE_FC]: {
    //     name: '落地页报告',
    //     hasSummary: true,
    //     timeUnitOptions: timeUnitOptionsWithHour,
    // },
    // [ReportType.LANDPAGE_FEED]: {
    //     name: '落地页报告',
    //     hasSummary: true,
    //     timeUnitOptions: timeUnitOptionsWithHour,
    // },
    [ReportType.REALTIME_MARKET_POINT]: {
        name: '实时报告',
        hasSummary: false,
        productFilterConflict: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC],
        showDatePicker: false,
        showDownload: false,
        customCardRender: {
            RealtimeChart,
            RealtimeDashboard,
        },
        pageType: PageType.RealtimeReport,
        customFilterConfig: {
            getFilters: () => {
                const {beforeMinute, minute} = getMinuteStatus();
                return {
                    value: [
                        {
                            column: 'minuteOfDay',
                            operator: 'GT',
                            values: [beforeMinute],
                        },
                        {
                            column: 'minuteOfDay',
                            operator: 'LTE',
                            values: [minute],
                        },
                    ],
                };
            },
        },
    },
    [ReportType.REALTIME_KEYWORD_COST]: {
        name: '实时报告',
        hasSummary: false,
        productFilterConflict: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC],
        showDatePicker: false,
        showDownload: false,
        showCampaignFilter: false,
        showDeviceFilter: true,
        supportSplitCascader: true,
        customCardRender: {
            RealtimeChart,
            RealtimeDashboard,
        },
        pageType: PageType.RealtimeReport,
        customFilterConfig: {
            getFilters: () => {
                const {beforeMinute, minute} = getMinuteStatus();
                return {
                    value: [
                        {
                            column: 'minuteOfDay',
                            operator: 'GT',
                            values: [beforeMinute],
                        },
                        {
                            column: 'minuteOfDay',
                            operator: 'LTE',
                            values: [minute],
                        },
                    ],
                };
            },
        },
        customColumnRender: {
            bidNew: {
                render: (text: string, record: any) => {
                    return <RealTimeReportBid {...record} />;
                },
            },
        },
        customRequestFields: [
            'userId',
            'keywordId',
            'adgroupPrice',
            'bidPrefer',
            'adgroupPcPriceFactor',
            'adgroupMobilePriceFactor',
            'campaignMobilePriceFactor',
            'campaignPcPriceFactor',
            'keywordNameStatus',
            'wInfoStatus',
        ],
    },
    [ReportType.REALTIME_MARKET_COST]: {
        name: '实时报告',
        hasSummary: false,
        productFilterConflict: true,
        supportProductList: [PRODUCT_TYPE_CONFIG.FC],
        showDatePicker: false,
        showDownload: false,
        showCampaignFilter: false,
        showDeviceFilter: true,
        supportSplitCascader: true,
        customCardRender: {
            RealtimeChart,
            RealtimeDashboard,
        },
        customColumnRender: {
            offlineTime: {
                render: (text: string, record: any) => {
                    return <OfflineTimeRender {...record} />;
                },
            },
        },
        customRequestFields: [
            'wbudget',
            'budgetCumulateStatus',
            'sharedBudgetId',
            'sharedBudgetName',
            'sharedBudgetType',
            'sharedBudget',
        ],
        customerFields: [{
            accessorKey: 'sharedBudget',
            field: 'sharedBudget',
            title: '预算',
            dataIndex: 'sharedBudget',
            width: 190,
            render: (text: string, record: any) => {
                const campaignBudgetProps = {
                    record: {
                        ...record,
                        budget: record?.wbudget,
                        type: record.sharedBudgetType,
                    },
                };
                return <CampaignShareBudget {...campaignBudgetProps} />;
            },
        }],
        pageType: PageType.RealtimeReport,
    },
    [ReportType.ATTENTION_CROWD]: {
        name: '关注人群报告',
    },
    // 爱采购商品报告
    [ReportType.ACG_GOODS]: {
        name: '爱采购商品报告',
        hasSummary: true,
        timeUnitOptions: timeUnitOptionsWithHour,
        pageType: PageType.AcgGoodsReport,
    },
};


export const getGuiEntryOptions = () => {
    const GUI_REPORT_LIST = [
        {
            pageType: PageType.ReportWanHuaTong,
            pageName: '自定义报告',
        },
        GUI_REPORT_CONFIG[ReportType.ACCOUNT_ASSEMBLE].name,
        GUI_REPORT_CONFIG[ReportType.FC_PROJECT].name,
        GUI_REPORT_CONFIG[ReportType.FC_CAMPAIGN].name,
        GUI_REPORT_CONFIG[ReportType.MARKET_POINT].name,
        GUI_REPORT_CONFIG[ReportType.SEARCH].name,
        GUI_REPORT_CONFIG[ReportType.TARGET_REGION].name,
        GUI_REPORT_CONFIG[ReportType.TARGET_AGE].name,
        GUI_REPORT_CONFIG[ReportType.TARGET_SEX].name,
        GUI_REPORT_CONFIG[ReportType.VIDEO_FC].name,
        GUI_REPORT_CONFIG[ReportType.TEXT_FC].name,
        GUI_REPORT_CONFIG[ReportType.CREATIVE_TITLE].name,
        GUI_REPORT_CONFIG[ReportType.CREATIVE_DESC].name,
        GUI_REPORT_CONFIG[ReportType.IMAGE_FC].name,
        GUI_REPORT_CONFIG[ReportType.REALTIME_MARKET_POINT].name,
        // GUI_REPORT_CONFIG[ReportType.LANDPAGE_FC].name,
        GUI_REPORT_CONFIG[ReportType.INVALID_CLICK_CHARGE_IP].name,
        GUI_REPORT_CONFIG[ReportType.INVALID_CLUE_SYSTEM].name,
        GUI_REPORT_CONFIG[ReportType.LIFT_BUDGET_PROJECT].name,
        GUI_REPORT_CONFIG[ReportType.ACG_GOODS].name,
    ];
    return GUI_REPORT_LIST.map(item => {
        if (typeof item === 'string') {
            return {
                label: item,
                value: item,
            };
        }
        return {
            label: item.pageName,
            value: item.pageType,
        };
    });
};
