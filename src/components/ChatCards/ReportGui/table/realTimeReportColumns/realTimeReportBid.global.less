.fc-cube-sdk-keyword-edit-bid-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .bid {
        margin-right: 8px;
    }

    .edit-bid {
        color: #282c33;
        margin-bottom: -4px;
        display: none;
        cursor: pointer;

        .edit-bid-icon {
            color: #848b99;
        }
    }
}

.one-table-tbody > tr:hover {
    .fc-cube-sdk-keyword-edit-bid-container {
        .edit-bid {
            display: inline-block;
        }
    }
}

.fc-cube-sdk-keyword-edit-bid-popover-layer .fc-cube-sdk-keyword-edit-bid-popover {
    width: 410px;

    .radio-group-container {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        margin-bottom: 26px;

        .price-container {
            margin-left: 16px;
        }
    }

    .bid-factor {
        margin-bottom: 14px;

        .one-ui-icon-svg {
            margin-left: 4px;
        }
    }

    .bid-remain {
        margin-bottom: 30px;

        .one-ui-icon-svg {
            margin-left: 4px;
        }
    }
}

.fc-cube-sdk-keyword-edit-bid-popover-layer {
    .one-popover-inner-content {
        max-width: 450px;
    }
}
