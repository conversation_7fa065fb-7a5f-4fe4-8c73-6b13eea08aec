/* eslint-disable complexity */
import {Popover, Link} from '@baidu/one-ui';
import {IconEdit} from 'dls-icons-react';
import {useMemo, useState} from 'react';
import {EditBid} from 'dataCenter/components';
import {getUserId} from '@/utils';
import {appendQuery} from '@/utils/route';
import {formatBid, getKeywordPrice} from '../util';

import './realTimeReportBid.global.less';

const RealTimeReportBid = (record: any) => {
    const {
        userId: keywordUserId,
        bidNew,
        keywordId,
        adgroupPrice,
        bidPrefer,
        adgroupPcPriceFactor,
        adgroupMobilePriceFactor,
        campaignMobilePriceFactor,
        campaignPcPriceFactor,
        priceStrategy = {},
        wInfoStatus,
    } = record || {};
    const [visible, setVisible] = useState(false);
    const [currentBid, setCurrentBid] = useState<number[]>([bidNew]);
    const {
        isTransCampaign,
        isBindEcpc,
        effectiveStrategyType,
    } = getKeywordPrice({record});

    const isHideEdit =
        isTransCampaign
        || effectiveStrategyType
        || isBindEcpc;


    const targetBid = currentBid && currentBid[0];
    const newBid = formatBid(targetBid);
    const currentUserId = getUserId();
    const isSameUserId = currentUserId === String(keywordUserId);


    const info = {
        keywordId,
        price: targetBid,
        adgroupMobilePriceFactor,
        adgroupPcPriceFactor,
        campaignPcPriceFactor,
        campaignMobilePriceFactor,
        bidPrefer,
        adgroupPrice,
    };

    const editBidProps = {
        info,
        onVisibleChange: setVisible,
        onSetCurrentBid: (value?: number) => {
            if (value) {
                setCurrentBid([value]);
            }
        },
    };
    const projectName = priceStrategy?.project?.strategyName || '';
    const projectListUrl = useMemo(() => {
        if (projectName) {
            return appendQuery(
                '/ad/manageCenter/projectList',
                {
                    userId: getUserId(),
                    globalProduct: 1,
                    filters: {
                        column: 'projectName',
                        operator: 'like',
                        values: [projectName],
                    },
                }
            );
        }
        return appendQuery(
            '/ad/manageCenter/projectList',
            {
                userId: getUserId(),
                globalProduct: 1,
            }
        );
    }, [projectName]);

    return (
        <div className="fc-cube-sdk-keyword-edit-bid-container">

            {
                newBid && wInfoStatus
                    ? (
                        effectiveStrategyType && !isBindEcpc
                            ? (
                                <Link
                                    toUrl={projectListUrl}
                                    type="strong"
                                    size="small"
                                    target="_blank"
                                    isAtag
                                >
                                    前往项目设置
                                </Link>
                            ) : (
                                <div>
                                    <span className="bid">{newBid}</span>
                                    {isSameUserId && !isHideEdit && (
                                        <Popover
                                            content={<EditBid {...editBidProps} />}
                                            trigger="click"
                                            placement="left"
                                            overlayClassName="fc-cube-sdk-keyword-edit-bid-popover-layer"
                                            visible={visible}
                                            onVisibleChange={setVisible}
                                            overlayStyle={{minWidth: 452}}
                                        >
                                            <span className="edit-bid">
                                                <IconEdit className="edit-bid-icon" />
                                            </span>
                                        </Popover>
                                    )}
                                </div>
                            )
                    )
                    : '-'
            }
        </div>
    );
};

export default RealTimeReportBid;
