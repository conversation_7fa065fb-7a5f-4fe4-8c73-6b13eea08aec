/* eslint-disable @typescript-eslint/prefer-for-of */
import {useState, useCallback} from 'react';
import {useRequestCallback} from 'huse';
import {isNull} from 'lodash-es';
import {getPriceText} from 'manageCenter/keywords/columns/price';
import {effectiveStrategyTypeFunc, isUseProject, isUseBindEcpc} from 'manageCenter/campaigns/list/campaignBid';
import {isOcpcCampaign} from 'commonLibs/utils/campaign';

const initialData = {rows: [], summary: {}, rowCount: 0, totalRowCount: 0};

// 报告新增支持立即刷新与刷新时间
// 参照老平台对useRequestCallback做了个封装
export function useRequestRankListData(task: (params: any) => Promise<any>, params?: any) {
    const [refreshTime, setRefreshTime] = useState(new Date());
    const refreshTask = useCallback(payload => {
        setRefreshTime(new Date());
        return task(payload);
    }, [task]);
    const [refresh, {data = initialData, pending, error}] = useRequestCallback(refreshTask, params);

    return {
        refreshTime,
        request: refresh,
        data,
        error,
        pending,
    };
}

// 报告支持细分
export const getColumnConfigurationWithSplit = (columnConfiguration: any, splitColumn: string) => {
    const {tableColumns, columnConfigs, splitColumnItems = [], ...rest} = columnConfiguration;

    if (splitColumn && splitColumnItems.length > 0) {
        const splitColumnItem = splitColumnItems.find((item: any) => item.dataIndex === splitColumn);
        if (splitColumnItem) {
            return {
                ...rest,
                tableColumns: [...tableColumns, splitColumnItem],
                columnConfigs: {
                    ...columnConfigs,
                    [splitColumn]: columnConfigs[splitColumn] || {},
                },
                splitColumnItems,
            };
        }
    }

    return columnConfiguration;
};

export function getDepsFields(customColumns: string[], depColumnsObj: Record<string, string[]>) {
    const depsFields: string[] = [];

    customColumns.forEach(column => {
        if (depColumnsObj[column]) {
            depsFields.push(...depColumnsObj[column]);
        }
    });

    return Array.from(new Set(depsFields));
}

export const isSummaryRecord = (record: any) => {
    return Array.isArray(record.isSummaryRow)
        ? record.isSummaryRow[0]
        : record.isSummaryRow;
};

export const formatSplitApiData = ({
    rows = [],
}: any, columns: string[] = [], splitColumn: string, extraColumns: string[] = []) => {
    let datasource = [...rows];
    datasource = datasource.map((item: any) => {
        let arr: any[] = [];
        const {parentRow, subRows = []} = item;
        if (parentRow) {
            arr = [...arr, parentRow, ...subRows];
        }
        else {
            arr.push(item);
        }
        return arr;
    });

    // 对处理后的数据进行最终格式化，将分组数据转换为表格可用的格式,这里直接照搬了cube-sdk的代码
    // sample:
    // 接口返回数据格式：
    // rows:[{
    //   parentRow:{cpc:xxx,name:xxx},
    //   subRows:[{hour:1, cpc:xxx},{hour:2, cpc:xxx},...]
    // }]
    // format后数据格式：
    // [{rowIndex: 0,hour: [1,2,3....],cpc: [xxx,xxx,xxx....],name:xxx},
    // {rowIndex: 1,hour: [1,2,3....],cpc: [xxx,xxx,xxx....],name:xxx},...]
    datasource = datasource.reduce((ret: any[], item: any[], index: number) => {
        const sortedItem: any = {
            rowIndex: index,
        };
        if (item[0]) {
            const targetItem = item[1] || item[0];
            targetItem[splitColumn] = targetItem[splitColumn] === 0
                ? targetItem[splitColumn]
                : targetItem[splitColumn] || '-';
            [...columns, splitColumn].forEach((key: string) => {
                if (extraColumns.includes(key)) {
                    sortedItem[key] = targetItem[key];
                }
                else {
                    sortedItem[key] = [];
                    for (let i = 0; i < item.length; i++) {
                        if (!isNull(item[i][key])) {
                            sortedItem[key].push(item[i][key]);
                        }
                    }
                }
            });
            ret.push(sortedItem);
        }
        return ret;
    }, []);
    return datasource;
};

export const formatBid = (bid: number, precision = 2) => {
    if (isNaN(bid) || (!bid && bid !== 0)) {
        return '';
    }
    return bid.toFixed(precision);
};

export const getKeywordPrice = ({record}: {record: any}) => {
    const {
        campaignBidType,
        price,
        adgroupPrice,
        campaignBid,
        priceStrategy = {},
    } = record;
    const isTransCampaign = isOcpcCampaign({campaignBidType});
    const effectiveStrategyType = effectiveStrategyTypeFunc(priceStrategy);
    const isBindEcpc = isUseBindEcpc(priceStrategy);

    return {
        effectiveStrategyType,
        isTransCampaign,
        isBindEcpc,
        isUseBindProject: isUseProject(priceStrategy),
        priceValue: getPriceText({price, adgroupPrice, campaignBid, isTransCampaign}),
    };
};
