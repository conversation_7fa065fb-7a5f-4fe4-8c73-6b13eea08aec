/**
 * 基于落地页搭建表单config
*/

import {FormConfig, FormFieldConfig, GroupComponentProps} from '@baidu/react-formulator';
import WithDrawerInput from '@/components/common/formItems/WithDrawerInput';
import {
    INPUT_WIDTH,
    AIBuildParams,
    AIBuildFormData,
    formatCommonBackendToFormData,
    formatCommonFormDataToBackend,
    Field$$BusinessAdvantages,
    Field$$MobileUrl,
    Field$$PcUrl,
    Field$$Keywords,
    Field$$AvoidBusiness,
    Field$$BidType,
    Field$$Budget,
    Field$$BudgetMock,
    Field$$TransTypes,
    Field$$OcpcBid,
    Field$$CampaignBid,
    Field$$Region,
    Field$$ScheduleValue,
    Field$$BrandInfo,
    Use$$BusinessAdvantages,
    Use$$Keywords,
    Watch$$BuildForm,
} from '../AiBuildForm/config';
import '../AiBuildForm/index.global.less';
import BusinessProfile from './BusinessProfile';

export interface AIBuildBusinessParams extends Omit<AIBuildParams, 'businessAdvantages' | 'keywords'> {
    // 本次新增字段：业务点画像信息
    businessProfileList: Array<{
        /** 推广业务 */
        business: string;
        /** 业务优势 */
        businessAdvantages: string[];
        /** 推广关键词 */
        keywords: string[];
    }>;
}

export function formatBackendToFormData(data: AIBuildBusinessParams) {
    const {
        businessProfileList = [],
        ...restData
    } = data;

    const commonData = formatCommonBackendToFormData(restData as AIBuildParams);

    return {
        businessProfileList: businessProfileList.map(item => ({
            ...item,
            businessAdvantages: item.businessAdvantages[0],
        })),
        ...commonData,
    };
}

export type AIBuildBusinessFormData = ReturnType<typeof formatBackendToFormData>;

export function formatFormDataToBackend(data: AIBuildBusinessFormData): AIBuildBusinessParams {
    const {
        businessProfileList = [],
        ...resetData
    } = data;

    const commonData = formatCommonFormDataToBackend(resetData as AIBuildFormData);

    return {
        ...commonData,
        businessProfileList: businessProfileList.map(item => ({
            ...item,
            businessAdvantages: [item.businessAdvantages],
        })),
        business: businessProfileList.map(item => item.business),
    };
}

function Group({children}: GroupComponentProps) {
    return (
        <div>
            <div className="ai-build-form-group-container">
                {children}
            </div>
            <div className="ai-build-form-group-tip">
                当前结果由大模型解析生成，请核对后使用。
            </div>
        </div>
    );
}

const fields: Array<FormFieldConfig<AIBuildBusinessFormData>> = [
    {
        group: 'group',
        use: [Group],
        fields: [
            {
                field: 'businessProfileList',
                use: [BusinessProfile, {
                    max: 10,
                    min: 1,
                    valueToAdd: {
                        keywords: [],
                        businessAdvantages: [],
                    },
                }],
                fieldArrayConfig: [
                    {
                        ...Field$$Keywords,
                        use: [WithDrawerInput, {
                            ...Use$$Keywords,
                            readOnlyTextProps: {
                                width: 360,
                                btnVisible: true,
                                placeholder: '请输入种子词',
                            },
                        }],
                        componentProps: ({disabled}) => ({
                            disabled,
                        }),
                    },
                    {
                        ...Field$$BusinessAdvantages,
                        use: [WithDrawerInput, {
                            formConfig: {
                                fields: [{
                                    field: 'businessAdvantages',
                                    label: null,
                                    use: ['TextArea', {width: 600, placeholder: '请输入产品/服务优势', minRows: 6}],
                                    validators: (value: string) => {
                                        if (!value || value.trim().length === 0) {
                                            return '请输入产品/服务优势';
                                        }
                                        if (value.length > 2000) {
                                            return '内容长度不能超过2000个字符';
                                        }
                                    },
                                }],
                            },
                            readOnlyTextProps: {
                                width: 360,
                                btnVisible: true,
                                placeholder: '请输入产品/服务优势',
                            },
                            drawerProps: {
                                width: 650,
                                title: '修改产品/服务优势',
                            },
                        }],
                        componentProps: ({disabled}) => ({
                            disabled,
                        }),
                    },
                ],
                componentProps: ({disabled, selectedTab}) => ({
                    disabled,
                    selectedTab,
                }),
            },
        ],
    },
    {
        ...Field$$MobileUrl,
        use: ['Input', {placeholder: '请输入移动落地页URL', width: INPUT_WIDTH}],
    },
    {
        ...Field$$PcUrl,
        use: ['Input', {placeholder: '请输入计算机落地页URL', width: INPUT_WIDTH}],
    },
    Field$$AvoidBusiness,
    Field$$BidType,
    Field$$Budget,
    Field$$BudgetMock,
    Field$$TransTypes,
    Field$$OcpcBid,
    Field$$CampaignBid,
    Field$$Region,
    Field$$ScheduleValue,
    Field$$BrandInfo,
];


export const formConfig: FormConfig<AIBuildBusinessFormData> = {
    fields,
    watch: Watch$$BuildForm,
};
