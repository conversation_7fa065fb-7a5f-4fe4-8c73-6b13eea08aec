/**
 * @file 基于落地页搭建表单组件
 * @date 2025/08/26
 * <AUTHOR>
*/

import {IconCheck} from 'dls-icons-react';
import {<PERSON><PERSON>, Button, Typography} from '@baidu/light-ai-react';
import React, {useEffect, useMemo, useState} from 'react';
import {useQuickForm, FormProvider, createReactiveData} from '@baidu/react-formulator';
import {useRequestCallback} from 'huse';
import {ProviderConfig} from '@baidu/one-ui';
import {useCardComplete, useRecordByComponent} from '@/hooks';
import {useAIBuildTaskInfo} from '@/api/aiBuild';
import {AI_BUILD_RECORD_CONFIG} from '@/config/aiBuildRecordConfig';
import PrevButton from '../../PrevButton';
import {AiProps} from '../interface';
import ReasoningSteps from '../ReasoningSteps';
import '../AiBuildForm/index.global.less';
import {
    AIBuildBusinessFormData,
    AIBuildBusinessParams,
    formatBackendToFormData,
    formatFormDataToBackend,
    formConfig,
} from './config';
import './index.global.less';

const Text = Typography.Text;
interface TaskInfo {
    taskId: number;
    taskType: number;
}

interface AiBuildFormProps extends AiProps {
    field: string;
    cardParameters: {
        keyInformation: AIBuildBusinessParams;
        taskInfo?: TaskInfo;
        allowPreviousStep?: boolean;
        previousStepField?: string;
    };
}

interface AiBuildFormBaseProps extends AiProps {
    keyInformation: AIBuildBusinessParams;
    field: string;
    allowPreviousStep?: boolean;
    previousStepField?: string;
}

interface AiBuildFormWithTaskProps extends AiProps {
    taskInfo: TaskInfo;
    field: string;
    allowPreviousStep?: boolean;
    previousStepField?: string;
}


function AiBuildFormBase({
    keyInformation,
    instructions,
    field,
    aiChat,
    allowPreviousStep,
    previousStepField,
}: AiBuildFormBaseProps) {

    const [{setCompleted}, {completed, expired}] = useCardComplete(instructions, field);

    const initialData = useMemo(() => {
        return formatBackendToFormData(keyInformation);
    }, [keyInformation]);

    const [Form, {validateFields, setFieldsValue, getFieldError}] = useQuickForm();
    const [formData] = useState(() => createReactiveData({
        ...initialData,
        selectedTab: 0,
    }));

    const [submit, {pending}] = useRequestCallback(async () => {
        let validatedFormData: AIBuildBusinessFormData;
        try {
            validatedFormData = await validateFields();
        } catch (error) {
            const tabIndex = formData.businessProfileList.findIndex((item, index) => {
                const keys = Object.keys(item);
                return keys.some(key => getFieldError(`businessProfileList.${index}.${key}`));
            });
            if (tabIndex !== -1) {
                setFieldsValue({selectedTab: tabIndex});
            }
            console.error('表单验证失败', error);
            throw error;
        }
        const apiData = formatFormDataToBackend(validatedFormData);
        await aiChat.pushMessage(
            'user',
            '确认以上所选应用使用到广告中。',
            {
                custom: {
                    currentField: field,
                    currentFieldValue: [{keyInformation: apiData}],
                },
                trigger: 'card',
            }
        );
        setFieldsValue({disabled: true});
        setCompleted();
    }, {scrollToError: true});


    const disabled = completed || expired;

    return (
        <div className="ai-build-form">
            <div className="title">我要推广的业务</div>
            <FormProvider
                value={{
                    inputErrorClassName: 'one-ai-invalid',
                    showFirstError: true,
                }}
            >
                <ProviderConfig theme="light-ai">
                    <Form
                        config={formConfig}
                        data={formData}
                        className="ai-build-form-element use-rf-preset-form-ui use-horizontal use-label-top"
                    />
                </ProviderConfig>
            </FormProvider>
            <div className="footer">
                {
                    completed
                        ? <Text disabled><IconCheck /> 已确认使用</Text>
                        : (
                            <Button
                                variant="primary"
                                disabled={disabled}
                                onClick={submit}
                                loading={pending}
                            >
                                {expired ? '已失效' : '确认使用'}
                            </Button>
                        )
                }
                <PrevButton
                    disabled={disabled}
                    field={field}
                    allowPreviousStep={allowPreviousStep}
                    previousStepField={previousStepField}
                    aiChat={aiChat}
                />
            </div>
        </div>
    );
}

function AiBuildFormWithTask({
    field,
    aiChat,
    instructions,
    taskInfo,
    ...rest
}: AiBuildFormWithTaskProps) {
    const [isReasoning, setIsReasoning] = useState(true);

    const {
        steps,
        currentStep,
        taskResult,
        isPassed,
        isFailed,
        errorMessages,
    } = useAIBuildTaskInfo({taskId: taskInfo.taskId, taskType: taskInfo.taskType});

    useEffect(() => {
        if (isPassed) {
            setTimeout(() => {
                setIsReasoning(false);
            }, 2000);
        }
        else if (isFailed) {
            setIsReasoning(false);
        }
    }, [isPassed, isFailed]);


    if (isReasoning) {
        return (
            <ReasoningSteps
                steps={steps}
                currentStep={currentStep}
            />
        );
    }

    if (isFailed) {
        return (
            <Alert status="error">
                <Typography.List items={['新建失败，请检查后重试', ...errorMessages]} />
            </Alert>
        );
    }

    return (
        <AiBuildFormBase
            keyInformation={taskResult.content}
            field={field}
            aiChat={aiChat}
            instructions={instructions}
            {...rest}
        />
    );
}
function AiBuildBusinessForm({
    field,
    aiChat,
    cardParameters,
    instructions,
    ...rest
}: AiBuildFormProps) {
    const {keyInformation, taskInfo, allowPreviousStep, previousStepField} = cardParameters;
    useRecordByComponent({tag: AI_BUILD_RECORD_CONFIG.AI_BUILD_BUSINESS_FORM});
    if (taskInfo && taskInfo.taskId) {
        return (
            <AiBuildFormWithTask
                field={field}
                aiChat={aiChat}
                instructions={instructions}
                taskInfo={taskInfo}
                allowPreviousStep={allowPreviousStep}
                previousStepField={previousStepField}
                {...rest}
            />
        );
    }
    return (
        <AiBuildFormBase
            keyInformation={keyInformation}
            field={field}
            aiChat={aiChat}
            instructions={instructions}
            allowPreviousStep={allowPreviousStep}
            previousStepField={previousStepField}
            {...rest}
        />
    );
}


export default AiBuildBusinessForm;
