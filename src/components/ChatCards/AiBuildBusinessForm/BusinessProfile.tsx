/**
 * @file 基于落地页搭建 业务、种子词、优势部分
 * @date 2025-08-27
 * <AUTHOR>
*/

import {useState, useEffect} from 'react';
import {Tabs, Toast} from '@baidu/one-ui';
import {partial, cloneDeep} from 'lodash-es';
import {FormConfig, FieldArrayComponentProps, useFormContext, useFormErrors} from '@baidu/react-formulator';
import {InlinePopoverEditor} from '@/components/common/materialList/InlinePopoverEditor';
import {CommonFormEditor, CommonFormEditorProps} from '@/components/common/materialList/commonFormEditor';

const TabPane = Tabs.TabPane;

interface BusinessFormData {
    business: string;
}

const businessFormConfig: FormConfig<BusinessFormData> = {
    fields: [
        {
            field: 'business',
            label: '业务名称',
            use: ['Input', {
                placeholder: '请输入业务名称',
                minLen: 1,
            }],
            rules: [
                ['required', '请输入业务名称'],
            ],
            validators: (value: string) => {
                if (!value || value.trim().length === 0) {
                    return '请输入业务名称';
                }
                if (value.trim().length > 40) {
                    return '业务名称长度不能超过40个字符';
                }
            },
            showFirstError: true,
        },
    ],
};

export default function BusinessProfile(props: FieldArrayComponentProps) {
    const {
        fieldArrayChildList = [],
        arrayFieldMethods,
        valueToAdd,
        max,
        min,
        disabled,
        value,
        onChange,
        selectedTab,
    } = props;

    // 获取所有业务tab的id
    const ids = fieldArrayChildList.map(items => {
        return items.id;
    });

    const isAddDisabled = max && value.length >= max || disabled;

    // 当前选中的tab索引
    const [tabIndex, setTabIndex] = useState(selectedTab);

    const activeKey = ids[tabIndex];

    // 获取表单校验错误信息
    const {verificationResults} = useFormContext();
    const errorsMap = useFormErrors(verificationResults); // 错误的key eg：businessProfileList.1.keywords
    const formErrorsStatus = Object.keys(errorsMap)
        .filter(key => key.includes('businessProfileList'))
        .map(item => item.split('.')[1])
        .reduce((prev: boolean[], cur: string) => {
            prev[cur] = true;
            return prev;
        }, []);

    // 添加业务
    const onAddTab = () => {
        arrayFieldMethods.push({
            ...valueToAdd,
            business: `业务${value.length + 1}`,
        });
        setTabIndex(value.length);
    };

    // 删除业务
    const onDeleteTab = (key: string) => {
        const idx = ids.indexOf(key);
        arrayFieldMethods.remove(idx);

        if (tabIndex > idx || (tabIndex === value.length - 1)) {
            setTabIndex(tabIndex - 1);
        }
    };

    // 切换业务tab
    const onChangeTab = (key: string) => {
        setTabIndex(ids.indexOf(key));
    };

    // 修改业务名称
    const onSave = (idx, {business}) => {
        const newValue = cloneDeep(value);
        newValue[idx].business = business;
        onChange(newValue);
    };

    useEffect(() => {
        // 当提交表单时若tab项有错误则切换到该tab项
        if (selectedTab !== tabIndex) {
            setTabIndex(selectedTab);
        }
    }, [selectedTab]);

    return (
        <div className="ai-build-form-tabs-container">
            <Tabs
                type="simple"
                activeKey={activeKey}
                onChange={onChangeTab}
                onDelete={onDeleteTab}
                onAdd={onAddTab}
                showAdd
                showAddDisabled={isAddDisabled}
            >
                {fieldArrayChildList.map((items, idx) => {
                    const tabPaneLabel = (
                        <div className="ai-build-form-tabs-title">
                            <span className="ai-build-form-tabs-title-text">{value[idx]?.business}</span>
                            <InlinePopoverEditor<Omit<CommonFormEditorProps<BusinessFormData>, 'closeEditor'>>
                                renderEditor={CommonFormEditor}
                                initialData={{
                                    business: value[idx]?.business,
                                }}
                                formConfig={businessFormConfig}
                                onSave={partial(onSave, idx)}
                            />
                        </div>
                    );
                    return (
                        <TabPane
                            key={items.id}
                            tab={tabPaneLabel}
                            status={formErrorsStatus[idx] ? 'error' : undefined}
                            closable={fieldArrayChildList.length > min}
                            className="ai-build-form-tabs-pane"
                        >
                            <div className="form-items-group-container">
                                {items.map(({FormItem, field}) => {
                                    return (
                                        <FormItem key={field} initialData={value[idx]} />
                                    );
                                })}
                            </div>
                        </TabPane>
                    );
                })}
            </Tabs>
        </div>
    );
}
