.ai-build-form {
    .title {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: #0e0f11;
        margin-bottom: 4px;
    }

    .one-ai-tabs-medium.one-ai-tabs-simple > .one-ai-tabs-bar {
        padding: 0;
    }

    .ai-build-form-group-container > .rf-form-item {
        width: 100%;
        padding-bottom: 0;
    }

    .ai-build-form-group-container {
        border-radius: 8px;
        border: 1px solid #6086C733;
        padding: 16px 16px 0 16px;
        width: 100%;

        .ai-build-form-tabs-container {
            width: 736px;

            .ai-build-form-tabs-title {
                max-width: 210px;
                display: inline-flex;

                .ai-build-form-tabs-title-text {
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }

            .form-items-group-container {
                display: flex;
                flex-wrap: wrap;
                gap: 16px;
            }

            .inline-operation-icon {
                display: none;
                cursor: pointer;
                padding-left: 2px;
                color: #848b99;
                font-size: 14px;
                flex-shrink: 0;
            }

            .ai-build-form-tabs-pane:hover {
                .inline-operation-icon {
                    display: inline-block;
                    visibility: visible;
                }
            }
        }
    }

    .ai-build-form-group-tip {
        font-size: 12px;
        line-height: 16px;
        color: #848b99;
        margin: 8px 0 16px 0;
    }
}
