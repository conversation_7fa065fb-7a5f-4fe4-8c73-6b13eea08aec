import React, {ComponentProps, lazy} from 'react';
import {Loading, ProviderConfig} from '@baidu/one-ui';
import {withBoundary} from 'react-suspense-boundary';
import {captureError} from '@/utils/logger';
import {WithWeirwoodCaughtBoundaryConfigProvider} from '@/entries/hoc/WithWeirwoodCaughtBoundaryConfigProvider';
import {LoggersContext} from '@/logger';
import {loggers} from '@/logger';
import {RadioGroup} from './RadioGroup';
import {Decision} from './Decision';
import {RadioTagGroups} from './RadioTagGroups';
import {Example} from './Example';

/* eslint-disable max-len */
const FCLandPageSelector = lazy(() => import('./FcCards/FcLandPage'));
const FCAppSelector = lazy(() => import('./FcCards/FcAppSelector'));
const FeedUrl = lazy(() => import('./Feed/Url'));
const LandPageSelector = lazy(() => import('./LandPage'));
const SingleLandingPage = lazy(() => import('./SingleLandPage'));
const CreativeTextsCheckboxGroup = lazy(() => import('./CreativeTexts/common'));
const ProgramCreativeTextsSelector = lazy(() => import('./CreativeTexts/program'));
const CreativeTextsGroup = lazy(() => import('./CreativeTexts/creativeTextGroup'));
const CreativeImageSelector = lazy(() => import('./CreativeImages'));
const CreativeVideosSelector = lazy(() => import('./CreativeVideos'));
const RegionMap = lazy(() => import('./Region/region'));
const ExcludeRegion = lazy(() => import('./Region/excludeRegion'));
const ProductCheckboxGroup = lazy(() => import('./Product'));
const IcgGoodsSelector = lazy(() => import('./IcgGoods'));
const IcgGoodsMultipleSelector = lazy(() => import('./IcgGoods/MultipleSelector'));
const RecommendVideo = lazy(() => import('./RecommendVideo').then(module => ({default: module.RecommendVideo})));
const FinishedGuide = lazy(() => import('./FinishedGuide'));
const StreamText = lazy(() => import('./StreamText'));
const Layout = lazy(() => import('./Layout'));
const TransAssets = lazy(() => import('./TransAssets'));
const CrowdSelector = lazy(() => import('./CrowdSelector'));
const CarCornerSelector = lazy(() => import('./CarCornerSelector'));
const ProjectSelector = lazy(() => import('./ProjectSelector'));
const MigrationSelector = lazy(() => import('./MigrationSelector'));
const ProjectPrompt = lazy(() => import('./ProjectPrompt'));
const IcgGoodsWatch = lazy(() => import('./IcgGoods/IcgGoodsWatch'));
const Text = lazy(() => import('./text'));
const CrowdTagRecommend = lazy(() => import('./CrowdTagSelector'));
const CrowdTagRecommendEdit = lazy(() => import('./CrowdTagSelector/Edit'));
const ExternalPlugin = lazy(() => import('./ExternalPlugin'));
const CampaignSelector = lazy(() => import('./CampaignSelector'));
const AnchorSelector = lazy(() => import('./Anchor'));
const AnchorPreview = lazy(() => import('./Anchor').then(module => ({default: module.AnchorPreview})));
const BcpAgent = lazy(() => import('./BcpAgent'));
const ReportGuiController = lazy(() => import('../AiReportSDK/reportController'));
const UniversalReport = lazy(() => import('./UniversalReport'));
const LiftBudget = lazy(() => import('./LiftBudget'));
const RemovedMarketPoint = lazy(() => import('./MarketPoint/removedMarketPoint'));
const ClueValidityOptimize = lazy(() => import('./ClueValidityOptimize'));
const MarketGuide = lazy(() => import('./MarketGuide'));
const ClueBusinessPoint = lazy(() => import('./ClueBussinessPoint'));
const Protocol = lazy(() => import('./Protocol'));
const FeedAiMax = lazy(() => import('./FeedAiMax'));
const FeedCreativeTexts = lazy(() => import('./Feed/CreativeTexts'));
const FeedCreativeImages = lazy(() => import('./Feed/CreativeImages'));
const FeedCreativeVideos = lazy(() => import('./Feed/CreativeVideos'));
const FeedUrlsAndTrack = lazy(() => import('./Feed/UrlsAndTrack'));
const FeedAudience = lazy(() => import('./Feed/Audience'));
const AiIncubator = lazy(() => import('./AiIncubator'));
const TimeSection = lazy(() => import('./TimeSection'));
const AiIncubatorEdit = lazy(() => import('./AiIncubator/aiIncubatorEdit'));
const FeedFlowType = lazy(() => import('./Feed/FlowType'));
const InvalidCluesTableSelector = lazy(() => import('./InvalidCluesTableSelector'));
const DateRangeClickInput = lazy(() => import('./DateRangeClickInput'));
const DiagnoseAgent = lazy(() => import('./DiagnoseAgent'));
const ConsumerInfo = lazy(() => import('./ConsumerInfo'));
const AiBuildUploader = lazy(() => import('./AiBuildUploader'));
const AiBuildStructure = lazy(() => import('./AiBuildStructure'));
const ModCreativeImages = lazy(() => import('./CreativeImagesEdit'));
const ModCreativeVideos = lazy(() => import('./CreativeVideosEdit'));
const AiBuildPreview = lazy(() => import('./AiBuildPreview'));
const AiBuildForm = lazy(() => import('./AiBuildForm'));
const AiBuildSuccess = lazy(() => import('./AiBuildSuccess'));
const FeedMonitorUrl = lazy(() => import('./Feed/MonitorUrls'));
const FeedDeeplink = lazy(() => import('./Feed/NaUrls'));
const FeedProduct = lazy(() => import('./Feed/Product'));
const FeedLpUrl = lazy(() => import('./Feed/Url'));
const FeedNewDownloadUrl = lazy(() => import('./Feed/NewDownloadUrl'));
const FeedCustomCreative = lazy(() => import('./FeedCustomCreative'));
const CommonTable = lazy(() => import('./Table/CommonTable'));
const FeedIdeaPlugin = lazy(() => import('./Feed/IdeaPlugin'));
const BrandSDKLauncherBtn = lazy(() => import('./BrandSDKLauncher/button'));
const FeedAutoCreative = lazy(() => import('./Feed/AutoCreative'));
const DiagnosisDate = lazy(() => import('./DiagnosisDate'));
const FeedBroadCastInfo = lazy(() => import('./Feed/BroadCastInfo'));
const FeedLiftBudget = lazy(() => import('./Feed/LiftBudget'));
const FeedInheritCampaign = lazy(() => import('./Feed/InheritCampaign'));
const ProjectMultiBidCard = lazy(() => import('./ProjectMultiBid'));
const ROITable = lazy(() => import('./FcCards/RoiTable'));
const CampaignSubmitCard = lazy(() => import('./CampaignSubmitCard'));
const BindProject = lazy(() => import('./BindProject'));
const CreativeTextEtaEdit = lazy(() => import('./CreativeTextEtaEdit'));
const FcTargetCrowd = lazy(() => import('./FcTargetCrowd'));
const CreativeTextRsaEdit = lazy(() => import('./CreativeTextRsaEdit'));
const CreativeTextRsaRecommend = lazy(() => import('./CreativeTextRsaRecommend'));
const CreativeTextEtaRecommend = lazy(() => import('./CreativeTextEtaRecommend'));
const SelectedCampaignAdgroup = lazy(() => import('./SelectedCampaignAdgroup'));
const FeedCampaignAppSelector = lazy(() => import('./AppSelector/feedCampaignAppSelector'));
const FeedAppSelector = lazy(() => import('./AppSelector/feedAppSelector'));
const FeedCampaignSelector = lazy(() => import('./CampaignSelector/feedCampaignSelector'));
const FcProjectIncludeCampaign = lazy(() => import('./FcProjectIncludeCampaign').then(module => ({default: module.FcProjectIncludeCampaign})));
const SingleProjectCard = lazy(() => import('./Project'));
const FeedTransAssertSelector = lazy(() => import('./TransAssets/feedTransAssets'));
const FcAiMax = lazy(() => import('./FcAiMax').then(module => ({default: module.FcAiMax})));
const ConflictNegativeWords = lazy(() => import('./NegativeWords/ConflictNegative').then(module => ({default: module.ConflictNegativeWords})));
const MarketingPointDiagnose = lazy(() => import('./MarketingPointDiagnose').then(module => ({default: module.MarketingPointDiagnose})));
const AssembleReport = lazy(() => import('./Report/AssembleReport'));
const SlotCard = lazy(() => import('./Report/slot').then(module => ({default: module.SlotCard})));
const DemandHotPointDescription = lazy(() => import('./DemandHotPoint').then(module => ({default: module.DemandHotPointDescription})));
const BanjiaReport = lazy(() => import('./Report/banjiaReport'));
const Tempad = lazy(() => import('./Tempad'));
const SpringActivity = lazy(() => import('./SpringActivity'));
const MarketPoint = lazy(() => import('./MarketPoint').then(module => ({default: module.MarketPoint})));
const ErrorsCard = lazy(() => import('./Errors').then(module => ({default: module.ErrorsCard})));
const NegativeWordsSelector = lazy(() => import('./NegativeWords').then(module => ({default: module.NegativeWordsSelector})));
const GuiTable = lazy(() => import('./ReportGui/table'));
const StackedLineRows = lazy(() => import('./UniversalReport/StackedLineRows'));
const LineRows = lazy(() => import('./UniversalReport/LineRows'));
const UniversalReportTable = lazy(() => import('./UniversalReport/Table'));
const UniversalReportLine = lazy(() => import('./UniversalReport/Line'));
const UniversalReportCoreIndex = lazy(() => import('./UniversalReport/CoreIndex'));
const YimeiLandingPage = lazy(() => import('./LandPage/yimei'));
const Business = lazy(() => import('./Business'));
const AppSelector = lazy(() => import('./AppSelector').then(module => ({default: module.AppSelector})));
const TextReport = lazy(() => import('./Table/TextReport'));
const ImageReport = lazy(() => import('./Table/ImageReport'));
const VideoReport = lazy(() => import('./Table/VideoReport'));
const QueryWordReport = lazy(() => import('./Table/QueryWordReport'));
const DiagnoseOptimize = lazy(() => import('./DiagnoseOptimize').then(module => ({default: module.DiagnoseOptimize})));
const CommonLineReport = lazy(() => import('./Chart/CommonLineReport').then(module => ({default: module.CommonLineReport})));
const IndustryInsights = lazy(() => import('./Chart/DataInsights/industry').then(module => ({default: module.IndustryInsights})));
const DataInsights = lazy(() => import('./Chart/DataInsights').then(module => ({default: module.DataInsights})));
const HorizontalBarChart = lazy(() => import('./Chart/horizontalBar').then(module => ({default: module.HorizontalBarChart})));
const Indicator = lazy(() => import('./Chart/Indicator').then(module => ({default: module.Indicator})));
const ModNameTip = lazy(() => import('./ModOptAndTip').then(module => ({default: module.ModNameTip})));
const PieChart = lazy(() => import('./Chart/Pie').then(module => ({default: module.PieChart})));
const BarChart = lazy(() => import('./Chart/Bar').then(module => ({default: module.BarChart})));
const SingleLineChart = lazy(() => import('./Chart/LineChart').then(module => ({default: module.SingleLineChart})));
const FeedBrandSelector = lazy(() => import('./Feed/Brand').then(module => ({default: module.FeedBrandSelector})));
const BrandSelector = lazy(() => import('./Brand').then(module => ({default: module.BrandSelector})));
const FCBrandSelector = lazy(() => import('./Brand').then(module => ({default: module.FCBrandSelector})));
const CrowdAge = lazy(() => import('./CrowdAge').then(module => ({default: module.CrowdAge})));
const CreativeMaterials = lazy(() => import('./CreativeMaterials').then(module => ({default: module.CreativeMaterials})));
const Schedule = lazy(() => import('./Schedule'));
const TransTypes = lazy(() => import('./TransType').then(module => ({default: module.TransTypes})));
const AppItem = lazy(() => import('@/components/common/app/appItem').then(module => ({default: module.AppItem})));
const WrappedDescAndLinksForAgent = lazy(() => import('@/components/common/Link').then(module => ({default: module.WrappedDescAndLinksForAgent})));
const ShopAgent = lazy(() => import('./ShopAgent'));
const FeedShortPlay = lazy(() => import('./Feed/ShortPlay'));
const BJHPayPanel = lazy(() => import('./Feed/PayPanel'));
const AiBuildSelectSource = lazy(() => import('./AiBuildSelectSource'));
const AiRefreshForm = lazy(() => import('./AiRefreshForm'));
const AiRefreshStructure = lazy(() => import('./AiRefreshStructure'));
const AiRefreshPreview = lazy(() => import('./AiRefreshPreview'));
const AiBuildBusinessForm = lazy(() => import('./AiBuildBusinessForm'));
export enum CardType {
    LandingPage = 100,
    RadioValue = 101,
    DecisionValue = 102,
    ExampleValue = 103,
    ScheduleValue = 104,
    ModOptAndTip = 105,
    CreativeImageSelector = 106,
    CreativeTextsCheckboxGroup = 107,
    CreativeTextsGroup = 108,
    CreativeMaterials = 109,
    RegionSelector = 110,
    ProductSelector = 111, // 产品库
    CrowdAgeSelector = 112,
    CreativeVideosSelector = 113,
    CreativeRecommendVideo = 114,
    FinishedGuide = 115,
    StreamText = 116,
    SingleLandingPage = 117,
    TransTypes = 118, //
    BrandSelector = 119,
    BarChart = 121,
    PieChart = 122,
    Indicator = 123,
    QueryWordReport = 124,
    HorizontalBarChart = 125,
    DataInsights = 126,
    IndustryInsights = 128,
    VideoReport = 129,
    IcgGoods = 130,
    BaiduIndex = 131,
    DescAndLinks = 132,
    Layout = 133,
    AppSelector = 134,
    AppItem = 135,
    NegativeWordsPacket = 136,
    ImageReport = 137,
    TextReport = 138,
    ErrorInfos = 139,
    Business = 140, // 业务点
    YimeiLandingPage = 141, // 医美落地页
    TransAssets = 142,
    CrowdSelector = 143,
    IcgGoodsMultiple = 144,
    ProjectSelector = 145,
    ProjectPrompt = 146,
    CommonReport = 147,
    RadioTagGroupsValue = 148,
    CrowdTagRecommend = 149,
    CarCorner = 153,
    Text = 150,
    CrowdTagRecommendEdit = 151,
    DiagnoseOptimize = 152,
    MarketPoint = 155,
    UniversalReport = 156,
    UniversalReportLine = 157,
    UniversalReportCoreIndex = 158,
    UniversalReportTable = 159,
    LineRows = 161,
    ReportGuiTable = 162,
    IcgGoodsWatch = 164,
    ReportGuiController = 165, // todo 这个可能要和智能体配合改一下
    SingleLineChart = 170,
    ExternalPlugin = 166,
    StackedLineRows = 167,
    CampaignSelector = 168,
    SpringActivity = 169,
    BcpAgent = 171,
    BanjiaReport = 178,
    MigrationSelector = 172,
    DemandHotPointDescription = 173, // 需求热点描述
    RemovedMarketPoint = 174, // 已去除的营销要点
    Anchor = 175,
    AnchorPreview = 176,
    ExcludeRegion = 179,
    LiftBudget = 180,
    SlotCard = 181,
    MarketGuide = 184,
    ProgramCreativetextSelector = 186,
    ClueValidityOptimize = 187,
    Protocol = 189,
    AssembleReport = 190,
    ClueBusinessPoint = 191,
    MarketingPointDiagnose = 192,
    ConflickNegative = 193,
    FcAiMax = 194,
    InvalidCluesTableSelector = 195,
    DateRangeClickInput = 196,
    FeedTransAssert = 197,
    SingleProjectCard = 198,
    FcProjectIncludeCampaign = 199,
    FeedCampaignSelector = 200,
    FeedAppSelector = 201,
    FeedAiMax = 202,
    FCBrandSelector = 206,
    SelectedCampaignAdgroup = 209,
    FeedUrlsAndTrack = 213,
    CreativeTextEtaRecommend = 214,
    CreativeTextRsaRecommend = 215,
    FcTargetCrowd = 216,
    BindProject = 217,
    CreativeTextRsaEdit = 218,
    ModCreativeImages = 219,
    CreativeTextEtaEdit = 220,
    ModCreativeVideos = 221,
    CampaignSubmitCard = 224,
    ProjectMultiBidCard = 225,
    ShopAgent = 226,
    FeedUrl = 260, // todo
    FCLandPageSelector = 1000,
    AiIncubator = 1001,
    AiIncubatorEdit = 1002,
    TimeSection = 1003,
    FCAppSelector = 1004,
    FeedAudience = 1007,
    FeedCreativeImages = 1008,
    FeedCreativeVideos = 1009,
    FeedBrandSelector = 1010,
    FeedFlowType = 1011,
    FeedCampaignAppSelector = 1012,
    FeedProduct = 1013,
    FeedLpUrl = 1014,
    FeedMonitorUrl = 1016,
    FeedDeeplink = 1017,
    DiagnoseAgent = 1018,
    ConsumerInfo = 1019,
    AiBuildUploader = 1020,
    AiBuildForm = 1021,
    AiBuildStructure = 1022,
    AiBuildPreview = 1024,
    AiBuildSuccess = 1026,
    FeedCreativeTexts = 1023,
    FeedNewDownloadUrl = 1027,
    FeedCustomCreative = 1029,
    FeedIdeaPlugin = 1028,
    FeedInheritCampaign = 1033,
    FeedLiftBudget = 1034,
    BrandSDKLauncherBtn = 1030,
    CommonTable = 1031,
    RoiTable = 1032,
    FeedAutoCreative = 1035,
    FeedShortPlay = 1036,
    BJHPayPanel = 1037,
    DiagnosisDate = 1038,
    FeedBroadCastInfo = 1039,
    AiBuildSelectSource = 1040,
    AIRefreshForm = 1041,
    AiRefreshStructure = 1042,
    AiRefreshPreview = 1043,
    AiBuildBusinessForm = 1044,
    Tempad = 10000,
}

const CARD_COMPONENT_MAP = {
    [CardType.LandingPage]: LandPageSelector,
    [CardType.RadioValue]: RadioGroup,
    [CardType.TransTypes]: TransTypes,
    [CardType.DecisionValue]: Decision,
    [CardType.ExampleValue]: Example,
    [CardType.ScheduleValue]: Schedule,
    [CardType.ModOptAndTip]: ModNameTip,
    [CardType.CreativeImageSelector]: CreativeImageSelector,
    [CardType.CreativeTextsCheckboxGroup]: CreativeTextsCheckboxGroup,
    [CardType.CreativeTextsGroup]: CreativeTextsGroup,
    [CardType.CreativeVideosSelector]: CreativeVideosSelector,
    [CardType.CreativeRecommendVideo]: RecommendVideo,
    [CardType.CreativeMaterials]: CreativeMaterials,
    [CardType.RegionSelector]: RegionMap,
    [CardType.ExcludeRegion]: ExcludeRegion,
    [CardType.ProductSelector]: ProductCheckboxGroup,
    [CardType.CrowdAgeSelector]: CrowdAge,
    [CardType.FinishedGuide]: FinishedGuide,
    [CardType.StreamText]: StreamText,
    [CardType.SingleLandingPage]: SingleLandingPage,
    [CardType.BrandSelector]: BrandSelector,
    [CardType.FCBrandSelector]: FCBrandSelector,
    [CardType.FeedBrandSelector]: FeedBrandSelector,
    [CardType.BarChart]: BarChart,
    [CardType.PieChart]: PieChart,
    [CardType.QueryWordReport]: QueryWordReport,
    [CardType.Indicator]: Indicator,
    [CardType.HorizontalBarChart]: HorizontalBarChart,
    [CardType.DataInsights]: DataInsights,
    [CardType.DiagnoseOptimize]: DiagnoseOptimize,
    [CardType.IndustryInsights]: IndustryInsights,
    [CardType.VideoReport]: VideoReport,
    [CardType.ImageReport]: ImageReport,
    [CardType.TextReport]: TextReport,
    [CardType.BaiduIndex]: ExternalPlugin,
    [CardType.ExternalPlugin]: ExternalPlugin,
    [CardType.IcgGoods]: IcgGoodsSelector,
    [CardType.Layout]: Layout,
    [CardType.DescAndLinks]: WrappedDescAndLinksForAgent,
    [CardType.AppSelector]: AppSelector,
    [CardType.AppItem]: AppItem,
    [CardType.Business]: Business,
    [CardType.YimeiLandingPage]: YimeiLandingPage,
    [CardType.ErrorInfos]: ErrorsCard,
    [CardType.TransAssets]: TransAssets,
    [CardType.NegativeWordsPacket]: NegativeWordsSelector,
    [CardType.CommonReport]: CommonLineReport,
    [CardType.IcgGoodsMultiple]: IcgGoodsMultipleSelector,
    [CardType.CrowdSelector]: CrowdSelector,
    [CardType.ProjectSelector]: ProjectSelector,
    [CardType.ProjectPrompt]: ProjectPrompt,
    [CardType.RadioTagGroupsValue]: RadioTagGroups,
    [CardType.CarCorner]: CarCornerSelector,
    [CardType.UniversalReport]: UniversalReport,
    [CardType.UniversalReportLine]: UniversalReportLine,
    [CardType.UniversalReportCoreIndex]: UniversalReportCoreIndex,
    [CardType.UniversalReportTable]: UniversalReportTable,
    [CardType.LineRows]: LineRows,
    [CardType.StackedLineRows]: StackedLineRows,
    [CardType.Text]: Text,
    [CardType.MarketPoint]: MarketPoint,
    [CardType.CrowdTagRecommend]: CrowdTagRecommend,
    [CardType.CrowdTagRecommendEdit]: CrowdTagRecommendEdit,
    [CardType.ReportGuiTable]: GuiTable,
    [CardType.SingleLineChart]: SingleLineChart,
    [CardType.IcgGoodsWatch]: IcgGoodsWatch,
    [CardType.CampaignSelector]: CampaignSelector,
    [CardType.SpringActivity]: SpringActivity,
    [CardType.Anchor]: AnchorSelector,
    [CardType.AnchorPreview]: AnchorPreview,
    [CardType.DemandHotPointDescription]: DemandHotPointDescription,
    [CardType.MigrationSelector]: MigrationSelector,
    [CardType.BcpAgent]: BcpAgent,
    [CardType.ReportGuiController]: ReportGuiController,
    [CardType.LiftBudget]: LiftBudget,
    [CardType.Tempad]: Tempad,
    [CardType.BanjiaReport]: BanjiaReport,
    [CardType.ProgramCreativetextSelector]: ProgramCreativeTextsSelector,
    [CardType.RemovedMarketPoint]: RemovedMarketPoint,
    [CardType.ClueValidityOptimize]: ClueValidityOptimize,
    [CardType.MarketGuide]: MarketGuide,
    [CardType.SlotCard]: SlotCard,
    [CardType.Protocol]: Protocol,
    [CardType.AssembleReport]: AssembleReport,
    [CardType.ClueBusinessPoint]: ClueBusinessPoint,
    [CardType.MarketingPointDiagnose]: MarketingPointDiagnose,
    [CardType.ConflickNegative]: ConflictNegativeWords,
    [CardType.FcAiMax]: FcAiMax,
    [CardType.FeedTransAssert]: FeedTransAssertSelector,
    [CardType.SingleProjectCard]: SingleProjectCard,
    [CardType.FcProjectIncludeCampaign]: FcProjectIncludeCampaign,
    [CardType.FeedCampaignSelector]: FeedCampaignSelector,
    [CardType.FeedAppSelector]: FeedAppSelector,
    [CardType.FeedCampaignAppSelector]: FeedCampaignAppSelector,
    [CardType.FeedAiMax]: FeedAiMax,
    [CardType.FeedCreativeTexts]: FeedCreativeTexts,
    [CardType.FeedCreativeImages]: FeedCreativeImages,
    [CardType.FeedCreativeVideos]: FeedCreativeVideos,
    [CardType.FeedUrlsAndTrack]: FeedUrlsAndTrack,
    [CardType.FeedUrl]: FeedUrl,
    [CardType.FeedAudience]: FeedAudience,
    [CardType.AiIncubator]: AiIncubator,
    [CardType.TimeSection]: TimeSection,
    [CardType.AiIncubatorEdit]: AiIncubatorEdit,
    [CardType.InvalidCluesTableSelector]: InvalidCluesTableSelector,
    [CardType.DateRangeClickInput]: DateRangeClickInput,
    [CardType.DiagnoseAgent]: DiagnoseAgent,
    [CardType.ConsumerInfo]: ConsumerInfo,
    [CardType.AiBuildUploader]: AiBuildUploader,
    [CardType.AiBuildStructure]: AiBuildStructure,
    [CardType.SelectedCampaignAdgroup]: SelectedCampaignAdgroup,
    [CardType.CreativeTextEtaRecommend]: CreativeTextEtaRecommend,
    [CardType.CreativeTextRsaRecommend]: CreativeTextRsaRecommend,
    [CardType.CreativeTextRsaEdit]: CreativeTextRsaEdit,
    [CardType.ModCreativeImages]: ModCreativeImages,
    [CardType.FcTargetCrowd]: FcTargetCrowd,
    [CardType.CreativeTextEtaEdit]: CreativeTextEtaEdit,
    [CardType.BindProject]: BindProject,
    [CardType.FeedFlowType]: FeedFlowType,
    [CardType.FCLandPageSelector]: FCLandPageSelector,
    [CardType.FCAppSelector]: FCAppSelector,
    [CardType.ModCreativeVideos]: ModCreativeVideos,
    [CardType.CampaignSubmitCard]: CampaignSubmitCard,
    [CardType.AiBuildPreview]: AiBuildPreview,
    [CardType.AiBuildForm]: AiBuildForm,
    [CardType.AiBuildSuccess]: AiBuildSuccess,
    [CardType.AiBuildSelectSource]: AiBuildSelectSource,
    [CardType.FeedProduct]: FeedProduct,
    [CardType.FeedMonitorUrl]: FeedMonitorUrl,
    [CardType.FeedDeeplink]: FeedDeeplink,
    [CardType.FeedLpUrl]: FeedLpUrl,
    [CardType.FeedInheritCampaign]: FeedInheritCampaign,
    [CardType.FeedLiftBudget]: FeedLiftBudget,
    [CardType.FeedNewDownloadUrl]: FeedNewDownloadUrl,
    [CardType.FeedCustomCreative]: FeedCustomCreative,
    [CardType.CommonTable]: CommonTable,
    [CardType.RoiTable]: ROITable,
    [CardType.ProjectMultiBidCard]: ProjectMultiBidCard,
    [CardType.ShopAgent]: ShopAgent,
    [CardType.FeedIdeaPlugin]: FeedIdeaPlugin,
    [CardType.BrandSDKLauncherBtn]: BrandSDKLauncherBtn,
    [CardType.FeedAutoCreative]: FeedAutoCreative,
    [CardType.FeedShortPlay]: FeedShortPlay,
    [CardType.BJHPayPanel]: BJHPayPanel,
    [CardType.DiagnosisDate]: DiagnosisDate,
    [CardType.FeedBroadCastInfo]: FeedBroadCastInfo,
    [CardType.AIRefreshForm]: AiRefreshForm,
    [CardType.AiRefreshStructure]: AiRefreshStructure,
    [CardType.AiRefreshPreview]: AiRefreshPreview,
    [CardType.AiBuildBusinessForm]: AiBuildBusinessForm,
} as const;

export type CardComponentMap = typeof CARD_COMPONENT_MAP;

const CARDS = {} as CardComponentMap;
Object.entries(CARD_COMPONENT_MAP).forEach(([cardType, Card]) => {
    (CARDS as any)[cardType] = wrappedFallback(Card);
});

interface CardBase<
    Type extends CardType,
    Payload extends Record<string, any>
> {
    type: Type;
    payload?: Payload;
}

export type Card = {
    [K in keyof CardComponentMap]: CardBase<K, ComponentProps<CardComponentMap[K]>>
}[keyof CardComponentMap];

export default CARDS;

type InferProps<T> = T extends React.ComponentType<infer Props> ? Props : never;

export function addFallback<T extends React.ComponentType<any>>(Component: T) {
    type Props = InferProps<T>;
    const ComponentWithFallback = withBoundary({
        pendingFallback() {
            return <Loading />;
        },
        renderError(error) {
            captureError(error);
            return <div>服务异常，请稍后重试</div>;
        },
    })(Component);

    const Res: React.ComponentType<Props> = props => <ComponentWithFallback {...props} />;

    return Res;
}

export function wrappedFallback<T extends React.ComponentType<any>>(Component: T) {
    type Props = InferProps<T>;
    const WrappedComponent = Component.displayName && Component.displayName.startsWith('withBoundary')
        ? Component
        : withBoundary({
            pendingFallback() {
                return <Loading />;
            },
            renderError(error) {
                captureError(error);
                return <div>服务异常，请稍后重试</div>;
            },
        })(Component);
    const WrappedFallbackComponent: React.ComponentType<Props> = props => {
        return (
            <LoggersContext.Provider value={{loggers}}>

                <WithWeirwoodCaughtBoundaryConfigProvider>
                    <ProviderConfig theme="light-d22">
                        <WrappedComponent {...props} />
                    </ProviderConfig>
                </WithWeirwoodCaughtBoundaryConfigProvider>
            </LoggersContext.Provider>
        );
    };
    return WrappedFallbackComponent;
}
