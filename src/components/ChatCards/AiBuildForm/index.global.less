@import '../../ChatCards/common.global.less';

.ai-build-form {
    max-width: 800px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    border-radius: 10px;
    border: 1px solid #6692DE26;
    background-color: #fff;

    .brand-name-usage-list {
        margin-top: 4px;
    }

    .ai-build-form-element {
        column-gap: 16px;

        .rf-form-item {
            width: calc(50% - 8px);

            .rf-form-item-label .rf-form-item-label-content {
                padding-right: 0;
            }
        }

        --rf-form-item-horizontal-margin-left: 0;
    }

    .comprehensive-clue-tip {
        display: none;
    }
}

.ai-build-form-textline {
    .one-ai-ui-pro-textline-error {
        display: none;
    }
}
