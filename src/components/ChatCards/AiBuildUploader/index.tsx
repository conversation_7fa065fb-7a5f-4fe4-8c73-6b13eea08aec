import {useCallback, useEffect, useMemo, useState} from 'react';
import {groupBy, partial} from 'lodash-es';
import classNames from 'classnames';
import {
    Uploader,
    UploaderProps,
    UploadFile,
    Button as OneButton,
    tools,
    Progress,
    Popover,
    Tabs,
    ProviderConfig,
} from '@baidu/one-ui';
import {uploadFile} from '@baidu/hairuo-client';
import {useQuickForm, FormProvider, createReactiveData, useLiveData} from '@baidu/react-formulator';
import {IconCheck, IconUpload, IconTimesCircleSolid, IconVideo} from 'dls-icons-react';
import {Button, Typography, Image} from '@baidu/light-ai-react';
import {v4 as uuidv4} from 'uuid';
import {useDropzone} from 'react-dropzone';
import {getUserId, getOperatorId, getToken} from '@/utils';
import {createError} from '@/utils/error';
import {toFixed} from '@/utils/number';
import {sendScreenRecord, useCardComplete} from '@/hooks';
import {OPERATION_TYPE} from '@/dicts/fieldOperationType';
import {useKeyOrientedArray} from '@/hooks/collection/array';
import ExcelIcon from '@/styles/assets/ExcelIcon.svg?react';
import {isAIBuildFastBrandBuildUser} from '@/utils/getFlag';
import {AI_BUILD_RECORD_CONFIG} from '@/config/aiBuildRecordConfig';
import {useRecordByComponent} from '@/hooks';
import {sendMonitor} from '@/utils/logger';
import {isSmartBuildBusinessProfileUser} from '@/utils/getFlag';
import {AiBuildType} from '../AiBuildForm/config';
import {AiProps} from '../interface';
import {
    UPLOADER_TYPE,
    UPLOADER_ACCEPT_TYPE,
    DROP_ACCEPT_TYPE,
    urlFormConfig,
    urlFormInitialData,
    formatFormDataToBackend,
    DownloadTemplateBtn,
} from './config';
import './index.global.less';

const Text = Typography.Text;
const TabPane = Tabs.TabPane;
const {originStatus} = tools.uploader;

type UploadFileType = UploadFile & {fileUrl?: string};
const getKey = (file: UploadFileType) => file.uid;

const MAX_FILE_SIZE = 2 * 1024 * 1024;
const MAX_FILE_LENGTH = 50;
interface AiBuildUploaderProps extends AiProps {
    field: string;
    sessionId: string;
    cardParameters: {
        marketingTargetId: number;
    };
}

const IMAGE_TIP_TEXT = '图片文件命名：经营业务-业务描述.格式。例如：家政-日常清洁.jpg';

// eslint-disable-next-line complexity
function AiBuildUploader({sessionId, instructions, field, aiChat, cardParameters}: AiBuildUploaderProps) {
    useRecordByComponent({tag: AI_BUILD_RECORD_CONFIG.AI_BUILD_UPLOADER});

    // tabs
    const [activeKey, setActiveKey] = useState<UPLOADER_TYPE>(UPLOADER_TYPE.TEMPLATE);
    const onChangeTab = (key: UPLOADER_TYPE) => {
        setActiveKey(key);
    };

    // 基于模版上传
    const [
        mediaFileList,
        {push: addMediaFile, set: setMediaFile, updateByKey: updateMediaFileByKey, removeByKey: removeMediaFileByKey},
    ] = useKeyOrientedArray([], {getKey});

    const [docUploading, setDocUploading] = useState<boolean>(false);

    // 基于落地页上传
    const [
        imageList,
        {push: addImage, set: setImageList, updateByKey: updateImageByKey, removeByKey: removeImageByKey},
    ] = useKeyOrientedArray([], {getKey});

    const [imageuploading, setImageuploading] = useState<boolean>(false);

    // 根据activeKey确定对应上传的api
    const isTemplateActive = activeKey === UPLOADER_TYPE.TEMPLATE;
    const fileList = isTemplateActive ? mediaFileList : imageList;
    const addFile = isTemplateActive ? addMediaFile : addImage;
    const setFileList = isTemplateActive ? setMediaFile : setImageList;
    const updateFileByKey = isTemplateActive ? updateMediaFileByKey : updateImageByKey;
    const removeFileByKey = isTemplateActive ? removeMediaFileByKey : removeImageByKey;
    const setUploading = isTemplateActive ? setDocUploading : setImageuploading;
    const uploading = isTemplateActive ? docUploading : imageuploading;

    const [Form, {validateFields, setFieldsValue}] = useQuickForm();
    const [formData] = useState(() => createReactiveData({
        ...urlFormInitialData,
        marketingTargetId: cardParameters.marketingTargetId,
    }));

    useEffect(() => {
        sendScreenRecord('ai_build_uploader');
    }, []);

    const successFileList = fileList.filter(file => file.status === 'success');
    const hasSuccessFile = successFileList.length > 0;

    // @ts-ignore
    const handleUpload = async ({file, onProgress, onError, onSuccess}) => {
        try {
            setUploading(true);
            const uploadResult = await uploadFile(
                file.originFile,
                {
                    methodPath: 'aurora/MOD/AixFileUploadService/uploadFile',
                    userid: getUserId(),
                    optid: getOperatorId(),
                    token: getToken(),
                    partParallel: 5,
                    customData: {
                        sessionId,
                        fileType: 1,
                    },
                    onprogress: ({progress}: {progress: number}) => {
                        onProgress({percent: Math.floor(progress * 100)}, file);
                    },
                }
            );
            onSuccess({...file, status: 'success'});
            updateFileByKey(file.uid, uploadResult || {});
            setUploading(false);
        }
        catch (err) {
            console.error('error', err);
            const error = createError(err);
            onError({...file, status: 'error', errorMessage: ['上传失败，请重试']});
            setUploading(false);
        }
    };

    const onDrop = useCallback(
        (acceptedFiles, unAcceptedFiles) => {
            acceptedFiles.forEach((file: any) => {
                const uid = uuidv4();
                const fileItem = {
                    status: 'uploading' as const,
                    name: file.name,
                    originFile: file,
                    uid,
                    type: file.type,
                    errorMessage: [],
                    progressStep: 0,
                };
                addFile(fileItem);
                handleUpload({
                    file: fileItem,
                    onProgress: ({percent}: {percent: number}) => {
                        updateFileByKey(uid, {progressStep: percent} as UploadFile);
                    },
                    onError: (file: any) => updateFileByKey(uid, file),
                    onSuccess: (file: any) => updateFileByKey(uid, file),
                });
            });
            unAcceptedFiles.forEach(file => {
                const uid = uuidv4();
                const fileItem = {
                    status: 'error' as const,
                    name: file.file.name,
                    originFile: file.file,
                    uid,
                    type: file.file.type,
                    errorMessage: ['上传失败，请确认文件格式与大小符合要求后重试'],
                    progressStep: 0,
                };
                addFile(fileItem);
            });
        },
        [addFile, handleUpload, updateFileByKey]
    );

    const [{setCompleted}, {completed, expired}] = useCardComplete(instructions, field);
    const disabled = completed || expired;

    const onOk = async (target: string) => {
        sendMonitor('click', {
            source: 'aibuild-uploader',
            target,
        });
        await aiChat.pushMessage(
            'user',
            '确认使用以上资料',
            {
                custom: {
                    currentField: field,
                    currentFieldOperate: OPERATION_TYPE.MOD,
                    currentFieldValue: [{
                        keyInformationFileUrls: successFileList.map(({fileUrl}: any) => fileUrl),
                    }],
                },
                trigger: 'card',
            }
        );
        setCompleted();
    };

    const onFastBrandBuild = async (target: string) => {
        sendMonitor('click', {
            source: 'aibuild-uploader',
            target,
        });
        await aiChat.pushMessage(
            'user',
            '快速搭建品牌计划',
            {
                custom: {
                    currentField: field,
                    currentFieldOperate: OPERATION_TYPE.MOD,
                    currentFieldValue: [{
                        keyInformationFileUrls: successFileList.map(({fileUrl}: any) => fileUrl),
                        buildType: AiBuildType.BRAND,
                    }],
                },
                trigger: 'card',
            }
        );
        setCompleted();
    };

    const onBusinessBuild = async (target: string) => {
        sendMonitor('click', {
            source: 'aibuild-uploader',
            target,
        });
        let validatedFormData;
        try {
            validatedFormData = await validateFields();
        } catch (error) {
            console.error('表单验证失败', error);
            throw error;
        }
        await aiChat.pushMessage(
            'user',
            '确认使用以上资料',
            {
                custom: {
                    currentField: field,
                    currentFieldOperate: OPERATION_TYPE.MOD,
                    currentFieldValue: [{
                        keyInformationFileUrls: successFileList.map(({fileUrl}: any) => fileUrl),
                        buildType: AiBuildType.BUSINESS,
                        ...formatFormDataToBackend(validatedFormData),
                    }],
                },
                trigger: 'card',
            }
        );
        setFieldsValue({disabled: true});
        setCompleted();
    };

    const {getRootProps, getInputProps, isDragActive} = useDropzone({
        onDrop,
        noClick: false,
        disabled,
        multiple: true,
        accept: DROP_ACCEPT_TYPE[UPLOADER_TYPE.TEMPLATE],
        maxSize: MAX_FILE_SIZE,
        maxFiles: MAX_FILE_LENGTH,
    });

    const {
        getRootProps: getImageRootProps,
        getInputProps: getImageInputProps,
        isDragActive: isImageDragActive
    } = useDropzone({
        onDrop,
        noClick: false,
        disabled,
        multiple: true,
        accept: DROP_ACCEPT_TYPE[UPLOADER_TYPE.BUSINESS],
        maxSize: MAX_FILE_SIZE,
        maxFiles: MAX_FILE_LENGTH,
    });

    const uploadProps: UploaderProps = {
        // @ts-ignore
        uploader: handleUpload,
        fileList,
        multiple: true,
        maxFileLength: MAX_FILE_LENGTH,
        maxSize: MAX_FILE_SIZE,
        accept: UPLOADER_ACCEPT_TYPE[activeKey],
        onChange: ({fileList}) => setFileList(fileList),
        onRemove: ({fileList}) => setFileList(fileList),
        // @ts-ignore
        CustomUploadPicker,
        className: 'ai-build-uploader',
        disabled,
    };

    const isEmpty = fileList.length === 0;

    const uploaderContainerCls = classNames({
        'uploader-container': true,
        'is-impty': isEmpty,
    });

    const hasImageFile = fileList.filter(file => {
        return /\.(png|jpeg|jpg)$/i.test(file.name);
    }).length;


    const hoverText = isEmpty
        ? '请上传物料'
        : hasImageFile
            ? null
            : (
                <div>建议添加图片素材，进一步优化账户搭建效果<br />{IMAGE_TIP_TEXT}</div>
            );

    const {pcUrl, mobileUrl} = useLiveData(formData);
    const hasUrl = pcUrl || mobileUrl;

    return (
        <div className="ai-build-uploader-card">
            <div className="title">营销资料</div>
            <Tabs
                activeKey={activeKey}
                onChange={onChangeTab}
                className="ai-build-uploader-tabs"
            >
                <TabPane key={UPLOADER_TYPE.TEMPLATE} tab="基于模版搭建">
                    <div className="template-upload-container">
                        {
                            !isEmpty && (
                                <div className="ai-build-uploader-control-container">
                                    <Uploader {...uploadProps} />
                                    <span className="download-template">
                                        {DownloadTemplateBtn}
                                    </span>
                                </div>
                            )
                        }
                        <div className={uploaderContainerCls} {...getRootProps()}>
                            <input {...getInputProps()} />
                            {isDragActive && <DragMask />}
                            {
                                isEmpty
                                    ? <Empty type={UPLOADER_TYPE.TEMPLATE} />
                                    : (
                                        <FileList
                                            fileList={fileList}
                                            removeFileByKey={removeFileByKey}
                                            disabled={disabled}
                                        />
                                    )
                            }
                        </div>
                        <div className="ai-build-uploader-footer">
                            {
                                completed
                                    ? <Text disabled><IconCheck /> 已确认使用</Text>
                                    : (
                                        <Popover content={hoverText} placement="topLeft" size="small">
                                            {/* 加一个 span 因为 disabled 时无法触发 popover */}
                                            <span>
                                                <Button
                                                    variant="primary"
                                                    disabled={expired
                                                        || !hasImageFile
                                                        || uploading
                                                        || !hasSuccessFile
                                                    }
                                                    onClick={partial(onOk, 'upload-build')}
                                                >
                                                    {expired ? '已失效' : '确认使用'}
                                                </Button>
                                            </span>
                                        </Popover>
                                    )
                            }
                            {
                                !hasImageFile && !completed && (
                                    <OneButton
                                        type="basic"
                                        onClick={partial(onOk, 'form-build')}
                                        disabled={expired || uploading}
                                    >
                                        不上传{isEmpty ? '物料' : '图片'}直接搭建
                                    </OneButton>
                                )
                            }
                            {
                                !completed && isAIBuildFastBrandBuildUser() && (
                                    <OneButton
                                        type="basic"
                                        onClick={partial(onFastBrandBuild, 'fast-brand-build')}
                                        disabled={disabled}
                                    >
                                        快速搭建品牌计划
                                    </OneButton>
                                )
                            }
                        </div>
                    </div>
                </TabPane>
                {isSmartBuildBusinessProfileUser() && (
                    <TabPane key={UPLOADER_TYPE.BUSINESS} tab="基于落地页搭建">
                        <div className="business-upload-container">
                            <div className="business-url-container">
                                <FormProvider
                                    value={{
                                        inputErrorClassName: 'one-ai-invalid',
                                        showFirstError: true,
                                    }}
                                >
                                    <ProviderConfig theme="light-ai">
                                        <Form
                                            config={urlFormConfig}
                                            data={formData}
                                            className="use-rf-preset-form-ui use-horizontal use-label-top"
                                        />
                                    </ProviderConfig>
                                </FormProvider>
                            </div>
                            <div className="business-upload-header">上传图片素材</div>
                            {
                                !isEmpty && (
                                    <div className="ai-build-uploader-control-container">
                                        <Uploader {...uploadProps} />
                                    </div>
                                )
                            }
                            <div className={uploaderContainerCls} {...getImageRootProps()}>
                                <input {...getImageInputProps()} />
                                {isImageDragActive && <DragMask />}
                                {
                                    isEmpty
                                        ? <Empty type={UPLOADER_TYPE.BUSINESS} />
                                        : (
                                            <FileList
                                                fileList={fileList}
                                                removeFileByKey={removeFileByKey}
                                                disabled={disabled}
                                            />
                                        )
                                }
                            </div>
                            <div className="ai-build-uploader-footer">
                                {
                                    completed
                                        ? <Text disabled><IconCheck /> 已确认使用</Text>
                                        : (
                                            <Popover
                                                content={hasUrl ? '' : '计算机/移动落地页链接至少填写一个'}
                                                placement="topLeft"
                                                size="small"
                                            >
                                                <span>
                                                    <Button
                                                        variant="primary"
                                                        disabled={expired || uploading || !hasUrl}
                                                        onClick={partial(onBusinessBuild, 'business-build')}
                                                    >
                                                        {expired ? '已失效' : '确认使用'}
                                                    </Button>
                                                </span>
                                            </Popover>
                                        )
                                }
                            </div>
                        </div>
                    </TabPane>
                )}
            </Tabs>
        </div>
    );
}

export default AiBuildUploader;

interface CustomUploadPickerProps {
    onClick: () => void;
    loading?: boolean;
    disabled?: boolean;
}
function CustomUploadPicker({onClick, loading, disabled}: CustomUploadPickerProps) {
    const buttonProps = {
        onClick,
        loading,
        disabled,
    };

    return (
        <Button variant="strong" {...buttonProps}>
            <IconUpload /> 上传图片和资料
        </Button>
    );
}

function FileList({fileList, removeFileByKey, disabled}: {
    fileList: UploadFileType[]; removeFileByKey: (uid?: string) => void; disabled?: boolean;
}
) {
    const groupedFiles = useMemo(
        () => groupBy(fileList, ({type}) => {
            if (
                type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                || type === 'application/vnd.ms-excel'
            ) {
                return 'excel';
            }
            if (type === 'image/png' || type === 'image/jpeg' || type === 'image/jpg') {
                return 'image';
            }
            if (type?.includes('video/')) {
                return 'video';
            }
            return 'unknown';
        }),
        [fileList]
    );

    return (
        <div className="file-list">
            {
                !!groupedFiles.excel?.length && (
                    <div className="file-list-group">
                        {groupedFiles.excel.map(fileItem => (
                            <FileItem
                                key={fileItem.uid}
                                Icon={ExcelIcon}
                                {...fileItem}
                                onDelete={() => removeFileByKey(fileItem.uid)}
                                disabled={disabled}
                            />
                        ))}
                    </div>
                )
            }
            {
                !!groupedFiles.image?.length && (
                    <Image.Group className="image-list-group" gap="small">
                        {
                            groupedFiles.image.map(item => (
                                <div
                                    key={item.uid}
                                    className="image-list-group-item"
                                    style={{position: 'relative', cursor: 'pointer'}}
                                >
                                    <Image
                                        src={URL.createObjectURL(item.originFile!)}
                                        height={52}
                                    />
                                    <IconTimesCircleSolid
                                        className="delete-btn"
                                        onClick={e => {
                                            e.stopPropagation();
                                            removeFileByKey(item.uid);
                                        }}
                                    />
                                    {
                                        item.status === originStatus.ERROR
                                            && item.errorMessage
                                            && item.errorMessage.length
                                            ? <div className="image-item-message">{item.errorMessage.join('，')}</div>
                                            : null
                                    }
                                </div>
                            ))
                        }
                    </Image.Group>
                )
            }
            {
                !!groupedFiles.video?.length && (
                    <div className="file-list-group">
                        {
                            groupedFiles.video.map(item => (
                                <FileItem
                                    key={item.uid}
                                    Icon={IconVideo}
                                    {...item}
                                    onDelete={() => removeFileByKey(item.uid)}
                                    disabled={disabled}
                                />
                            ))
                        }
                    </div>
                )
            }
        </div>
    );
}

function Empty({type}: {type: UPLOADER_TYPE}) {
    return (
        <div className="empty-content">
            <div className="title">点击或拖放文件上传</div>
            {type === UPLOADER_TYPE.TEMPLATE && <div className="download-template-btn">{DownloadTemplateBtn}</div>}
            <div>
                <div className="tips">文件大小：单个文件不超过2M   文件数量：最多50个</div>
                <div className="tips">支持文件类型：{UPLOADER_ACCEPT_TYPE[type].join('、')}</div>
                {type === UPLOADER_TYPE.TEMPLATE && <div className="tips">{IMAGE_TIP_TEXT}</div>}
            </div>
        </div>
    );
}

function DragMask() {
    return (
        <div className="drag-mask">
            松开鼠标可上传资料
        </div>
    );
}

function FileItem({
    name,
    status,
    progressStep,
    errorMessage,
    Icon,
    originFile,
    onDelete,
    disabled,
}: UploadFile & {Icon?: any, onDelete: () => void, disabled?: boolean}) {

    const cls = classNames({
        'file-item': true,
        'disabled': disabled,
    });

    const ext = name.split('.').pop();
    const filename = ext?.length ? name.slice(0, -ext.length - 1) : name;

    return (
        <div className={cls}>
            <div className="file-item-container">
                {Icon && <Icon className="file-item-icon" />}
                <div className="file-item-info">
                    <div className="file-item-name">
                        <div className="file-item-label">
                            {filename}
                        </div>
                        <div className="file-item-ext">
                            .{ext}
                        </div>
                    </div>

                    <div className="file-item-size">{toFixed((originFile?.size || 0) / 1024)}KB</div>
                </div>
                {
                    !disabled && (
                        <div
                            className="delete-btn"
                            onClick={e => {
                                e.stopPropagation();
                                onDelete();
                            }}
                        >
                            <IconTimesCircleSolid />
                        </div>
                    )
                }
            </div>
            {
                status === originStatus.UPLOADING
                    ? (
                        <Progress
                            size="small"
                            width={183}
                            percent={progressStep}
                            showInfo={false}
                            className="file-item-progress"
                        />
                    )
                    : null
            }
            {
                status === originStatus.ERROR && errorMessage && errorMessage.length
                    ? <div className="file-item-message">{errorMessage.join('，')}</div>
                    : null
            }
        </div>
    );
}
