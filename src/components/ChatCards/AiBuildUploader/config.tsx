import {
    Button as OneButton,
} from '@baidu/one-ui';
import {IconDownload} from 'dls-icons-react';
import WithDrawerInput from '@/components/common/formItems/WithDrawerInput';
import PageUrl from '@/components/PageUrl';
import FinalUrl from '@/components/FinalUrl';
import {Field$$PcUrl, Field$$MobileUrl} from '../AiBuildForm/config';


export enum UPLOADER_TYPE {
    TEMPLATE = 'template',
    BUSINESS= 'business',
}

export const UPLOADER_ACCEPT_TYPE = {
    [UPLOADER_TYPE.TEMPLATE]: ['.xlsx', '.xls', '.png', '.jpeg', '.jpg'],
    [UPLOADER_TYPE.BUSINESS]: ['.png', '.jpeg', '.jpg'],
};

export const DROP_ACCEPT_TYPE = {
    [UPLOADER_TYPE.TEMPLATE]: {
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
        'application/vnd.ms-excel': ['.xls'],
        'image/*': ['.png', '.jpeg', '.jpg', '.PNG', '.JPEG', '.JPG'],
    },
    [UPLOADER_TYPE.BUSINESS]: {
        'image/*': ['.png', '.jpeg', '.jpg', '.PNG', '.JPEG', '.JPG'],
    },
};

export const DownloadTemplateBtn = (
    <OneButton
        type="text-strong"
        onClick={e => {
            e.stopPropagation();
            window.open('https://fc-file.cdn.bcebos.com/%E8%BD%BB%E8%88%B8%E6%99%BA%E8%83%BD%E6%90%AD%E5%BB%BA-%E8%90%A5%E9%94%80%E8%B5%84%E6%96%99%E6%A8%A1%E6%9D%BF.xlsx');
        }}
    >
        <IconDownload /> 资料模板下载
    </OneButton>
);

export const urlFormInitialData = {
    mobileUrl: '',
    pcUrl: '',
    disabled: false,
};

export const urlFormConfig = {
    fields: [
        {
            ...Field$$MobileUrl,
            use: [WithDrawerInput, {
                formConfig: {
                    fields: [{
                        field: 'mobileUrl',
                        label: null,
                        rules: [['required', '请选择落地页链接']],
                        use: [PageUrl, {showType: 'mobile'}],
                        componentProps: ({marketingTargetId}) => ({
                            marketingTargetId,
                        }),
                    }],
                },
                isReadOnlyText: false,
                inputTextProps: {
                    width: 376,
                    chatBtnText: '选择',
                    placeholder: '请输入落地页链接或选择已有落地页',
                    field: 'mobileUrl',
                    isDirectShow: true,
                },
                drawerProps: {
                    width: 658,
                    title: '选择落地页',
                    className: 'page-url-select-drawer',
                },
            }],
            componentProps: ({disabled, mobileUrl, marketingTargetId}) => ({
                disabled,
                initialData: {
                    mobileUrl,
                    marketingTargetId,
                },
            }),
            transform: {
                parse: ({mobileUrl}) => mobileUrl,
            },
        },
        {
            ...Field$$PcUrl,
            use: [FinalUrl, {
                placeholder: '请输入落地页链接或选择已有落地页',
                width: 376,
                showType: 'pc',
                containerClassName: 'qg-form-input-text',
                inputClassName: 'qg-form-input',
                hasCustomDropdownBtn: true,
            }],
        },
    ],
};

export function formatFormDataToBackend(data) {
    const {
        mobileUrl,
        pcUrl,
    } = data;
    return {
        mobileUrls: mobileUrl ? [mobileUrl] : [],
        pcUrls: pcUrl ? [pcUrl] : [],
    };
}
