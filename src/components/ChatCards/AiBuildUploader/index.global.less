.ai-build-uploader-card {
    width: 800px;
    padding: 16px;
    border-radius: 10px;
    border: 1px solid #6692DE26;
    background-color: #fff;

    .ai-build-uploader-tabs > .one-tabs-bar {
        padding: 0;
    }

    .template-upload-container,
    .business-upload-container {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .ai-build-uploader-control-container {
            margin-bottom: -4px;
        }
    }

    .business-upload-container {
        .business-upload-header {
            margin-bottom: -4px;
            margin-top: -20px;
        }

        .qg-form-input-text {
            .qg-form-input-text-btn {
                top: -1px;
            }

            input {
                padding-right: 70px;
            }
        }

        form {
            gap: 16px;
        }

        .rf-form-item {
            width: calc(50% - 8px);
        }

        --rf-form-item-horizontal-margin-left: 0;
    }

    .title {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: #0E0F11;
    }

    .ai-build-uploader-control-container {
        position: relative;
    }

    .uploader-container {
        border-radius: 6px;
        position: relative;

        &.is-impty {
            border: 1px dashed #729CFE;
            padding: 24px;
            background-color: var(--Translucent-1, #6D9FF712);

            &:hover {
                background-color: var(--Translucent-2, #6C9CF01A);
            }

            &:active {
                background-color: var(--Translucent-3, #6692DE26);
            }
        }

        .file-list {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .file-list-group {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 16px;
            }

            .image-list-group {
                flex-wrap: wrap;

                .image-item-message {
                    margin-top: 4px;
                    color: #d9150b;
                    font-size: 12px;
                    word-break: break-all;
                    max-width: 123px;
                }
            }

            .delete-btn {
                position: absolute;
                right: -6px;
                top: -8px;
                color: #848B99;
                display: none;
            }

            .file-item {
                position: relative;
                cursor: pointer;

                &.disabled {
                    cursor: not-allowed;
                }

                .file-item-container {
                    height: 52px;
                    padding: 8px;
                    border-radius: 6px;
                    background: #6D9FF712;
                    display: flex;
                    gap: 8px;

                    & > svg {
                        flex-shrink: 0;
                    }
                }

                .file-item-progress {
                    position: absolute;
                    font-size: 0;
                    top: 51px;
                }

                .file-item-message {
                    margin-top: 4px;
                    color: #d9150b;
                    font-size: 12px;
                    word-break: break-all;
                    max-width: 183px;
                }

                .file-item-info {
                    width: 134px;

                    .file-item-name {
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 16px;
                        color: #191B1E;
                        display: flex;

                        .file-item-label {
                            text-overflow: ellipsis;
                            overflow: hidden;
                            white-space: nowrap;
                        }

                        .file-item-ext {
                            flex: 1;
                            white-space: nowrap;
                        }
                    }

                    .file-item-size {
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 16px;
                        color: #848B99;
                    }
                }

                &:hover {
                    .delete-btn {
                        display: block;
                    }

                    .file-item-container {
                        background: #6692DE26;
                    }
                }
            }

            .image-list-group-item:hover {
                .delete-btn {
                    display: block;
                }
            }
        }
    }

    .download-template {
        position: absolute;
        top: 50%;
        left: 160px;
        transform: translateY(-50%);
    }

    .empty-content {
        text-align: center;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .title {
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            color: #191B1E;
        }

        .download-template-btn {
            margin-top: -4px;
        }

        .tips {
            font-size: 12px;
            font-weight: 400;
            color: #545B66;
            line-height: 16px;
        }
    }

    .drag-mask {
        position: absolute;
        width: 100%;
        top: 0;
        left: 0;
        height: 100%;
        background: #F6F7FA;
        opacity: 0.4;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #191B1E;
    }

    .drag-mask + .empty-content {
        visibility: hidden;
    }

    .ai-build-uploader {
        .one-uploader-list {
            width: 100%;
            display: flex;
            gap: 6px;
            flex-wrap: wrap;

            .one-uploader-file-item {
                border-radius: 6px;
            }
        }

        .one-uploader-list-file {
            display: none;
        }
    }

    .ai-build-uploader-footer {
        display: flex;
        gap: 12px;
        align-items: center;
    }
}
