import {useMemo, useState} from 'react';
import {partial, noop, difference} from 'lodash-es';
import classNames from 'classnames';
import {Skeleton} from '@baidu/light-ai-react';
import {useRequest} from 'huse';
import {getUserId} from '@/utils';
import {isAppLinkLive, isFeedCustomMultiObjective, isFeedSalesLeadTransTypeUpdate} from '@/utils/getFeedFlag';
import {getFlag} from '@/utils/getFlag';
import {useCardComplete} from '@/hooks';
import {DescAndLinks} from '@/components/common/Link';
import {useRadioItemsCard} from '@/hooks/card/useRadioItemsCard';
import {OPERATION_TYPE} from '@/dicts/fieldOperationType';
import {FeedTransAsset, TransAssetsParams, fetchFeedTransAssets} from '@/api/feedProject';
import {
    FeedProjectFormData,
    FeedProject, FeedProjectAndroidAppInfo,
    FeedProjectIOSAppInfo,
    AppSubType,
    SaleType,
} from '@/interface/aixProject/feedProject';
import {FeedCampaignForApi, TransAssetItem as TransAssetItemInfo} from '@/interface/feedEditor/campaign';
import {formatApi2FeedProjectData} from '@/components/Project/FeedEditor/util';
import globalData from '@/utils/globalData';
import {
    FeedThreeLevelFormData, formatToFeedThreeLevelFormData,
} from '@/components/FeedEditor/common/utils/normalizer';
import {checkFeedSubjectType, FeedAssetType} from '@/config/feed';
import {TRANS_TYPE_ENUM} from '@/config/feed/trans';
import {FtypeSelectionValue, ProductTypeValues} from '@/config/feed/adgroup';
import {SUBJECT_TO_APP_TYPE, TRANS_FROM_ENUM} from '@/config/feed/campaign';
import {
    RadioItemsCard,
    DefaultPager,
    DefaultSearchBox,
    DefaultSelectorButton,
} from '../../common/Card/SelectorItemsCard';
import {AiProps} from '../interface';
import {SalesleadTransCluePairs, SalesleadTransJMYPairs} from './config';
import './index.global.less';

interface TransAssetsSelectorProps extends AiProps {
    field: string;
    cardParameters: {
        feedProjectType?: FeedProject;
        campaignFeedType?: FeedCampaignForApi;
    };
}

function FeedAssetSelectorLoading() {
    return (
        <div className="feed-asset-suspense-container">
            <Skeleton.Image active style={{width: 250, height: 67}} />
            <Skeleton.Image active style={{width: 250, height: 67}} />
            <Skeleton.Image active style={{width: 250, height: 67}} />
            <Skeleton.Image active style={{width: 250, height: 67}} />
            <Skeleton.Image active style={{width: 250, height: 67}} />
            <Skeleton.Image active style={{width: 250, height: 67}} />
            <Skeleton.Image active style={{width: 250, height: 67}} />
            <Skeleton.Image active style={{width: 250, height: 67}} />
            <Skeleton.Image active style={{width: 250, height: 67}} />
        </div>
    );
}

const slots = {
    item: TransAssetItem,
    title: TransAssetsSelectorTitle,
    search: DefaultSearchBox,
    pager: DefaultPager,
    confirmBtn: DefaultSelectorButton,
    footerLink: TransAssetsSelectorConfirmLink,
    loading: FeedAssetSelectorLoading,
};

const filters = [
    (i: FeedTransAsset, {searchValue = ''}) => i.transName.includes(searchValue),
];

export enum LEVEL_TYPE {
    PROJECT = 1,
    CAMPAIGN = 2,
    ADGROUP = 3,
}


// eslint-disable-next-line complexity, max-statements
export const getFeedTransAssets = async ({
    level,
    formData,
}: {
    level: LEVEL_TYPE;
    formData: Partial<FeedProjectFormData> | Partial<FeedThreeLevelFormData>;
}) => {
    let params = {};
    const appType = SUBJECT_TO_APP_TYPE[formData?.subject];
    if (level === LEVEL_TYPE.PROJECT) {
        const {
            subject,
            saleType,
            appSubType,
            appInfo,
            eshopType,
            miniProgramType,
            bjhType,
            productType,
        } = formData as FeedProjectFormData;
        const {
            appUrl,
            channelId,
            apkName,
            docId,
        } = (appInfo as FeedProjectAndroidAppInfo & FeedProjectIOSAppInfo) || {};
        params = {
            subject,
            appSubType,
            downloadUrl: appUrl,
            isProjectLevel: true,
            saleType,
            eshopType,
            miniProgramType,
            bjhType,
            productType,
        };
        if (appSubType !== AppSubType.appAppoint) {
            params.appType = appType; // 后端@lvrui要求新游预约就不传appType\channelId\apkName\docId
            params.channelId = channelId;
            params.apkName = apkName;
            params.docId = docId;
        }
    } else if (level === LEVEL_TYPE.CAMPAIGN) {
        const {
            subject,
            saleType,
            appSubType,
            appinfo, // 方案上的app信息
            eshopType,
            miniProgramType,
            bstype,
            ftypes,
        } = (formData as FeedThreeLevelFormData).campaignFeedType;
        const {
            appUrl,
            channelId,
            apkName,
            docId,
        } = (appinfo as FeedProjectAndroidAppInfo & FeedProjectIOSAppInfo) || {};
        params = {
            subject,
            appSubType,
            downloadUrl: appUrl,
            channelId,
            apkName,
            docId,
            isProjectLevel: false,
            saleType,
            eshopType,
            miniProgramType,
            bstype,
            ftypes,
            appType,
        };
    } else if (level === LEVEL_TYPE.ADGROUP) {
        const {
            subject,
            saleType,
            appSubType,
            appinfo, // 方案上的app信息
            eshopType,
            miniProgramType,
            bstype,
            ftypes: campaignFttypes,
        } = (formData as FeedThreeLevelFormData).campaignFeedType;
        const {
            ftypes: adgroupFttypes,
            ftypeSelection,
        } = (formData as FeedThreeLevelFormData).adgroupFeedType;
        const {
            appUrl,
            channelId,
            apkName,
            docId,
        } = (appinfo as FeedProjectAndroidAppInfo & FeedProjectIOSAppInfo) || {};
        params = {
            subject,
            appSubType,
            downloadUrl: appUrl,
            channelId,
            apkName,
            docId,
            isProjectLevel: false,
            saleType,
            eshopType,
            miniProgramType,
            bstype,
            ftypes: ftypeSelection === FtypeSelectionValue.adgroup ? adgroupFttypes : campaignFttypes,
            appType,
        };
    }

    const {subject, saleType} = level === LEVEL_TYPE.PROJECT
        ? (formData as FeedProjectFormData)
        : (formData as FeedThreeLevelFormData).campaignFeedType;
    const {isSalesLeadSubject, isLinkSubject, isBjhSubject, isAppLinkSubject} = checkFeedSubjectType(subject);
    const data = await fetchFeedTransAssets(params as TransAssetsParams);

    // 销售线索双出价升级小流量
    if ((!isFeedSalesLeadTransTypeUpdate() || !(isSalesLeadSubject || isLinkSubject)) && !isBjhSubject) {
        data.forEach(item => {
            item?.transTypes.forEach(transTypeItem => {
                const {transType, transFrom} = transTypeItem;
                let {deepTransTypes} = transTypeItem;
                if (transFrom === TRANS_FROM_ENUM.API_CLUE) {
                    deepTransTypes = difference(deepTransTypes, SalesleadTransCluePairs[transType] || []);
                }
                if (transFrom === TRANS_FROM_ENUM.ICOP) {
                    deepTransTypes = difference(deepTransTypes, SalesleadTransJMYPairs[transType] || []);
                }
                transTypeItem.deepTransTypes = deepTransTypes;
            });
        });
    }

    // 销售线索扩充【自建站】-综合线索收集
    if (!isFeedCustomMultiObjective() || !isSalesLeadSubject) {
        data.forEach(item => {
            if (item.assetType === FeedAssetType.CUSTOM) {
                item.transTypes = item.transTypes.filter(transTypeItem => {
                    return transTypeItem.transType !== TRANS_TYPE_ENUM.MULTIOBJECTIVE;
                });
            }
        });
    }

    // 应用调起直播推广【直播间服务购买成功(CT131)】
    if (isAppLinkLive()
        && isAppLinkSubject
        && saleType === SaleType.live
    ) {
        data.forEach(item => {
            item.transTypes = item.transTypes.filter(transTypeItem => {
                return transTypeItem.transType === TRANS_TYPE_ENUM.LIVE_SERVICE_PURCHASE;
            });
        });
    }

    if (level === LEVEL_TYPE.PROJECT) {
        if (globalData.get('projectStore')?.formData) {
            (globalData.get('projectStore').formData as FeedProjectFormData).transAssetsOptions = data;
        }
    } else if (level === LEVEL_TYPE.CAMPAIGN) {
        if (globalData.get('feedEditorStore').formData) {
            (
                globalData.get('feedEditorStore').formData as FeedThreeLevelFormData
            ).campaignTransAssetsOptions = data;
        }
    } else if (level === LEVEL_TYPE.ADGROUP) {
        if (globalData.get('feedEditorStore').formData) {
            (
                globalData.get('feedEditorStore').formData as FeedThreeLevelFormData
            ).adgroupTransAssetsOptions = data;
        }
    }
    return data as TransAssetItemInfo[];
};


const empty: FeedTransAsset[] = [];
export default function FeedTransAssets(props: TransAssetsSelectorProps) {
    const {field, aiChat, instructions, cardParameters: {feedProjectType, campaignFeedType}} = props;
    const [{setCompleted}, {completed, expired}] = useCardComplete(instructions, field);

    const disabled = completed || expired;

    const [[level, previousFormData, initSelectedId]] = useState(() => {
        const isCampaign = !!campaignFeedType;
        const formData = isCampaign
            ? formatToFeedThreeLevelFormData({campaignFeedType})
            : formatApi2FeedProjectData({feedProjectType});
        return [
            isCampaign ? LEVEL_TYPE.CAMPAIGN : LEVEL_TYPE.PROJECT,
            formData,
            isCampaign ? formData.campaignFeedType.appTransId : formData.appTransId,
        ];
    });


    const {data: transAssets = empty, pending} = useRequest(
        getFeedTransAssets,
        {
            level,
            formData: previousFormData,
        }
    );

    const {
        searchBoxProps,
        onSelectItem: setSelectedTransId,
        currentPageData: transAssetsList,
        totalPageCount,
        selectedId: selectedTransId,
        data: filteredTransAssets,
        isSearchboxExpand,
        onExpandSearchbox,
        pageNo,
        setPageNo,
    } = useRadioItemsCard<FeedTransAsset, 'transId'>({
        initialValue: initSelectedId,
        options: transAssets, filters,
    });

    const onOk = async () => {
        await aiChat.pushMessage(
            'user',
            '确认以上所选转化资产使用到广告中。',
            {
                custom: {
                    currentField: field,
                    currentFieldOperate: OPERATION_TYPE.MOD,
                    currentFieldValue: transAssets.filter(i => {
                        return i.transId === selectedTransId;
                    }).map(item => ({appTransId: item.transId})),
                },
                trigger: 'card',
            }
        );
        setCompleted();
    };

    const searchProps = useMemo(
        () => ({isSearchboxExpand, onExpandSearchbox, searchBoxProps}),
        [isSearchboxExpand, onExpandSearchbox, searchBoxProps]
    );
    const pagerProps = useMemo(
        () => ({totalPageCount, pageNo, setPageNo, field}),
        [totalPageCount, pageNo, setPageNo, field]
    );

    return (
        <RadioItemsCard
            className="trans-assets-selector-card"
            itemKey="transId"
            slots={slots}
            isLoading={pending}
            list={transAssetsList}
            selectedId={selectedTransId}
            totalCount={filteredTransAssets.length}
            onSelect={setSelectedTransId}
            onOk={onOk}
            pagerProps={pagerProps}
            searchProps={searchProps}
            disabled={disabled}
            completed={completed}
            expired={expired}
            // slotsProps={{}}
        />
    );
}

interface TransAssetItemProps extends FeedTransAsset {
    disabled?: boolean;
    isSelected?: boolean;
    onSelect?: (transId: FeedTransAsset['transId']) => void;
}
function TransAssetItem({transName, transId, isSelected, disabled, onSelect}: TransAssetItemProps) {
    const cls = classNames('trans-asset-item', {'selected': isSelected, 'disabled': disabled});
    return (
        <div className={cls} onClick={onSelect && !disabled ? partial(onSelect, transId) : noop}>
            <div className="trans-asset-name">{transName}</div>
            <div className="trans-asset-id">资产ID：{transId}</div>
        </div>
    );
}

interface TransAssetsSelectorTitleProps {
    totalCount: number;
}
function TransAssetsSelectorTitle({totalCount}: TransAssetsSelectorTitleProps) {
    return (
        <div className="title">
            转化资产 / 转化追踪（{totalCount}）
        </div>
    );
}

interface TransAssetsSelectorConfirmLinkProps {
    disabled: boolean;
}
function TransAssetsSelectorConfirmLink({disabled}: TransAssetsSelectorConfirmLinkProps) {
    if (disabled) {
        return null;
    }
    return (
        <DescAndLinks
            descAndLinks={[
                '都不符合您的诉求？',
                ...(getFlag('conversion-tracking-remode-list') ? [{
                    text: '转化中心',
                    toUrl: `https://feedads.baidu.com/fc/assetscenter/trans/eventAsset/user/${getUserId()}`,
                }] : [{
                    text: '转化中心',
                    toUrl: `https://feedads.baidu.com/fc/toolscenter/optimize/track/user/${getUserId()}`,
                }]),
            ]}
        />
    );
}
