import {ActionParams, GLOBAL_SEARCH_CONSTANTS, ToolCommandConfig} from '../types';

export const CUSTOM_COMMAND_CONFIG: ToolCommandConfig[] = [
    // 物料搜索命令（所有平台都支持）
    {
        id: GLOBAL_SEARCH_CONSTANTS.COMMAND_ID,
        title: GLOBAL_SEARCH_CONSTANTS.TITLE,
        description: GLOBAL_SEARCH_CONSTANTS.DESCRIPTION,
        keywords: ['全局查询', '全局查找', '物料查找', '搜索', '搜索物料'],
        requiresInput: true,
        inheritInputValue: true,
        defaultDisplay: false,
        order: 9999, // 确保在列表末尾
        action: {
            execute: () => {
                // 不执行任何操作，状态切换由 hooks 处理
            },
        },
    },
    {
        id: 'tool_create_aix_campaign',
        title: '创建提示词广告',
        description: '针对提示词描述拓展人群，投放效果广告',
        keywords: ['对话'],
        requiresInput: false,
        order: -8,
        action: {
            execute: ({linkToChat}: ActionParams) => {
                linkToChat(['user', '新建提示词方案']);
            },
        },
    },
    {
        id: 'tool_jyt',
        title: '爱采购加油推',
        description: '加油推为基于爱采购商品阿拉丁卡的CPC投放产品',
        requiresInput: false,
        order: -11,
        action: {
            execute: ({linkToChat}: ActionParams) => {
                linkToChat(['user', '爱采购加油推']);
            },
        },
    },
];
