.ai-chat-new-entry-container {
    height: calc(100vh - 68px - 112px);
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;

    .ai-chat-entry-main {
        display: flex;
        justify-content: center;
        height: 100%;
        width: 100%;
        gap: 16px;
        max-width: 1808px;

        .ai-chat-entry-left {
            display: flex;
            align-items: center;
            flex-direction: column;
            gap: 16px;
            width: calc(100% - 324px);
        }

        .ai-chat-entry-right {
            width: 308px;
            display: flex;
            flex-shrink: 0;
            flex-direction: column;
            gap: 16px;
        }

        .industry-competitiveness-container {
            .radar-chart {
                .echarts-for-react {
                    height: 278px !important;
                }
            }
        }

        .account-overview-container {
            width: 100%;
            background: url('../../styles/assets/banner.png');
            background-size: calc(100% + 450px);
            background-position: -250px 0%;
            background-clip: padding-box;
            position: relative;
            border-radius: 6px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding-bottom: 32px;
            max-height: 640px;

            .ai-chat-title {
                padding: 32px 32px 0 0;

                .title1 {
                    font-size: 30px;
                    line-height: 38px;
                    font-weight: 600;
                }
            }

            .ai-chat-data-overview {
                width: calc(100% - 64px);
                max-width: 1768px;
                bottom: 32px;
                align-self: center;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }

            .report-date {
                display: flex;
                gap: 12px;
                font-size: 14px;
                line-height: 20px;
                margin: 8px 0;
                color: #191b1e;
            }

            .account-report-info {
                display: flex;
                justify-content: center;
                align-items: center;
                flex-wrap: nowrap;
                min-height: 118px;
                border-radius: 6px;
                padding: 16px;
                gap: 48px;
                background: #fff;
                cursor: pointer;

                .report-item {
                    flex: 1;
                    border-radius: 10px;
                    height: 100%;

                    &:not(:last-child) {
                        position: relative;

                        &::after {
                            content: "";
                            position: absolute;
                            right: -24px;
                            height: 73px;
                            width: 1px; // 分割线宽度为1px
                            background-color: #6086c7; // 分割线颜色
                            opacity: .2;
                            top: 50%;
                            transform: translateY(-50%);
                        }
                    }

                    .title {
                        font-size: 14px;
                        font-weight: 500;
                        line-height: 20px;
                    }

                    .data-report {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .report-number {
                            flex-shrink: 0;
                            flex-basis: 122px;
                            font-family: 'Baidu Number';

                            .value {
                                font-size: 32px;
                                line-height: 28px;
                                font-weight: 500;
                                margin-top: 6px;
                            }

                            .ratio {
                                font-size: 18px;
                                font-weight: 500;
                                line-height: 26px;
                                margin-top: 6px;
                            }
                        }

                        .report-line {
                            height: 66px;
                            width: 120px;
                            flex: 1;
                        }
                    }
                }

                .report-loading-item {
                    display: flex;
                    align-items: center;
                    gap: 32px;
                }
            }
        }
    }

    .ai-chat-entry-to-bottom-btn {
        position: fixed;
        bottom: 138px;
        border: none;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 18px;
        box-shadow: 0 8px 16px 4px rgba(0, 71, 194, 0.08), 0 7px 14px 2px rgba(0, 71, 194, 0.07), 0 1px 8px 0 rgba(0, 71, 194, 0.05);
    }
}
