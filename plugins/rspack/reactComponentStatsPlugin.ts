/* eslint-disable no-console */
import * as fs from 'fs';
import * as path from 'path';
import {Compiler} from '@rspack/core';
import {parse} from '@babel/parser';
import traverse from '@babel/traverse';
import * as t from '@babel/types';

interface ComponentStats {
    [fileName: string]: {
        [componentName: string]: number;
    };
}

interface PluginOptions {
    srcDir?: string;
    outputFile?: string;
    extensions?: string[];
}

export class ReactComponentStatsPlugin {
    private readonly options: PluginOptions;

    constructor(options: PluginOptions = {}) {
        this.options = {
            srcDir: options.srcDir || 'src',
            outputFile: options.outputFile || 'react-component-stats.json',
            extensions: options.extensions || ['.js', '.jsx', '.ts', '.tsx'],
            ...options,
        };
    }

    apply(compiler: Compiler) {
        compiler.hooks.afterEmit.tapAsync('ReactComponentStatsPlugin', (compilation, callback) => {
            try {
                const stats = this.analyzeComponents();
                this.outputStats(stats);
                console.log('📊 React Component Stats generated successfully!');
                callback();
            } catch (error) {
                console.error('❌ Error generating React Component Stats:', error);
                callback();
            }
        });
    }

    private analyzeComponents(): ComponentStats {
        const stats: ComponentStats = {};
        const srcPath = path.resolve(process.cwd(), this.options.srcDir!);

        if (!fs.existsSync(srcPath)) {
            console.warn(`⚠️  Source directory not found: ${srcPath}`);
            return stats;
        }

        this.walkDirectory(srcPath, stats);
        return this.sortStats(stats);
    }

    private walkDirectory(dir: string, stats: ComponentStats) {
        const files = fs.readdirSync(dir);

        for (const file of files) {
            const filePath = path.join(dir, file);
            const stat = fs.statSync(filePath);

            if (stat.isDirectory()) {
                this.walkDirectory(filePath, stats);
            } else if (this.isReactFile(filePath)) {
                this.analyzeFile(filePath, stats);
            }
        }
    }

    private isReactFile(filePath: string): boolean {
        const ext = path.extname(filePath);
        return this.options.extensions!.includes(ext);
    }

    private analyzeFile(filePath: string, stats: ComponentStats) {
        try {
            const content = fs.readFileSync(filePath, 'utf-8');
            const relativePath = path.relative(process.cwd(), filePath);

            // 检查是否包含 React 相关内容
            if (!this.isLikelyReactFile(content)) {
                return;
            }

            const ast = this.parseFile(content, filePath);
            if (!ast) {return;}

            const components = this.extractComponents(ast);

            if (Object.keys(components).length > 0) {
                stats[relativePath] = components;
            }
        } catch (error) {
            console.warn(`⚠️  Error analyzing file ${filePath}:`, error);
        }
    }

    private isLikelyReactFile(content: string): boolean {
        return (
            content.includes('React')
            || content.includes('jsx')
            || content.includes('JSX')
            || content.includes('<')
            || content.includes('export')
            || content.includes('function')
            || content.includes('=>')
        );
    }

    private parseFile(content: string, filePath: string) {
        try {
            const isTypeScript = /\.(ts|tsx)$/.test(filePath);

            return parse(content, {
                sourceType: 'module',
                plugins: [
                    'jsx',
                    'typescript',
                    'decorators-legacy',
                    'classProperties',
                    'objectRestSpread',
                    'asyncGenerators',
                    'functionBind',
                    'exportDefaultFrom',
                    'exportNamespaceFrom',
                    'dynamicImport',
                    'nullishCoalescingOperator',
                    'optionalChaining',
                    ...(isTypeScript ? ['typescript' as const] : []),
                ],
            });
        } catch (error) {
            console.warn(`⚠️  Parse error in ${filePath}:`, error);
            return null;
        }
    }

    private extractComponents(ast: any): { [componentName: string]: number } {
        const components: { [componentName: string]: number } = {};

        traverse(ast, {
            // 处理命名导出的函数声明: export function Component() {}
            ExportNamedDeclaration(path) {
                if (t.isFunctionDeclaration(path.node.declaration)) {
                    const func = path.node.declaration;
                    if (func.id && this.isComponentFunction(func)) {
                        const name = func.id.name;
                        components[name] = (components[name] || 0) + 1;
                    }
                }

                // 处理变量声明: export const Component = () => {}
                if (t.isVariableDeclaration(path.node.declaration)) {
                    path.node.declaration.declarations.forEach(declarator => {
                        if (t.isIdentifier(declarator.id)
                            && (t.isArrowFunctionExpression(declarator.init)
                             || t.isFunctionExpression(declarator.init))) {
                            const name = declarator.id.name;
                            if (this.isComponentName(name) && this.isComponentFunction(declarator.init)) {
                                components[name] = (components[name] || 0) + 1;
                            }
                        }
                    });
                }
            },

            // 处理默认导出: export default function Component() {}
            ExportDefaultDeclaration(path) {
                let componentName = 'default';

                if (t.isFunctionDeclaration(path.node.declaration)) {
                    const func = path.node.declaration;
                    if (this.isComponentFunction(func)) {
                        componentName = func.id?.name || 'default';
                        components[componentName] = (components[componentName] || 0) + 1;
                    }
                } else if (t.isArrowFunctionExpression(path.node.declaration)
                          || t.isFunctionExpression(path.node.declaration)) {
                    if (this.isComponentFunction(path.node.declaration)) {
                        components[componentName] = (components[componentName] || 0) + 1;
                    }
                } else if (t.isIdentifier(path.node.declaration)) {
                    // export default Component (引用已定义的组件)
                    componentName = path.node.declaration.name;
                    if (this.isComponentName(componentName)) {
                        components[componentName] = (components[componentName] || 0) + 1;
                    }
                }
            },

            // 处理函数声明 (可能被导出)
            FunctionDeclaration(path) {
                if (path.node.id && this.isComponentFunction(path.node)) {
                    // 检查是否在导出语句中
                    if (this.isExported(path)) {
                        const name = path.node.id.name;
                        if (!components[name]) { // 避免重复计数
                            components[name] = (components[name] || 0) + 1;
                        }
                    }
                }
            },
        });

        return components;
    }

    private isComponentName(name: string): boolean {
        return /^[A-Z]/.test(name);
    }

    private isComponentFunction(node: any): boolean {
        if (!node) {return false;}

        // 检查函数名是否以大写字母开头
        if (t.isFunctionDeclaration(node) && node.id) {
            if (!this.isComponentName(node.id.name)) {return false;}
        }

        // 检查函数体是否包含 JSX
        return this.hasJSXReturn(node);
    }

    private hasJSXReturn(node: any): boolean {
        let hasJSX = false;

        if (!node.body) {return false;}

        traverse(node, {
            ReturnStatement(path) {
                if (path.node.argument) {
                    if (t.isJSXElement(path.node.argument)
                        || t.isJSXFragment(path.node.argument)) {
                        hasJSX = true;
                        path.stop();
                    }
                }
            },
            JSXElement() {
                hasJSX = true;
            },
            JSXFragment() {
                hasJSX = true;
            },
        }, undefined, path => path.node === node);

        return hasJSX;
    }

    private isExported(path: any): boolean {
        let parent = path.parent;
        while (parent) {
            if (t.isExportNamedDeclaration(parent) || t.isExportDefaultDeclaration(parent)) {
                return true;
            }
            parent = parent.parent;
        }
        return false;
    }

    private sortStats(stats: ComponentStats): ComponentStats {
        const sortedStats: ComponentStats = {};

        // 按文件名排序
        const sortedFileNames = Object.keys(stats).sort();

        for (const fileName of sortedFileNames) {
            const components = stats[fileName];

            // 按组件导出次数降序排列
            const sortedComponents = Object.entries(components)
                .sort(([, a], [, b]) => b - a)
                .reduce((acc, [name, count]) => {
                    acc[name] = count;
                    return acc;
                }, {} as { [componentName: string]: number });

            sortedStats[fileName] = sortedComponents;
        }

        return sortedStats;
    }

    private outputStats(stats: ComponentStats) {
        const outputPath = path.resolve(process.cwd(), this.options.outputFile!);

        // 格式化输出
        const formattedStats = JSON.stringify(stats, null, 2);

        fs.writeFileSync(outputPath, formattedStats, 'utf-8');

        // 控制台输出统计信息
        console.log('\n📊 React Component Export Statistics:');
        console.log('=====================================');

        let totalComponents = 0;
        let totalFiles = 0;

        for (const [fileName, components] of Object.entries(stats)) {
            totalFiles++;
            const componentCount = Object.keys(components).length;
            totalComponents += componentCount;

            console.log(`\n📄 ${fileName}:`);
            for (const [componentName, count] of Object.entries(components)) {
                console.log(`   ${componentName}: ${count} export${count > 1 ? 's' : ''}`);
            }
        }

        console.log('\n📈 Summary:');
        console.log(`   Total files with React components: ${totalFiles}`);
        console.log(`   Total component exports: ${totalComponents}`);
        console.log(`   Output saved to: ${outputPath}`);
    }
}

export default ReactComponentStatsPlugin;
