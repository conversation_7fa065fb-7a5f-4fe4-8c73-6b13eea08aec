{"name": "fe-cangjie", "version": "0.0.1", "description": "AI-X生成式投放平台前端", "scripts": {"start:webpack": "SKR_LOGGING=debug NODE_OPTIONS=--max-old-space-size=8192 skr dev --config reskript.webpack.dev.ts --host dev.qingge.baidu.com", "start:webpack:preonline": "SKR_LOGGING=debug STAGE=PREONLINE NODE_OPTIONS=--max-old-space-size=8192 skr dev --config reskript.webpack.dev.ts --host dev.qingge.baidu-int.com", "start:webpack:test": "SKR_LOGGING=debug STAGE=TEST NODE_OPTIONS=--max-old-space-size=8192 skr dev --config reskript.webpack.dev.ts --host dev.aix-test.baidu.com", "start:webpack:online": "SKR_LOGGING=debug STAGE=ONLINE NODE_OPTIONS=--max-old-space-size=8192 skr dev --config reskript.webpack.dev.ts --host dev.qingge.baidu.com", "start": "NODE_OPTIONS=--max-old-space-size=8192 rspack serve -c rspack.config.dev.ts", "start:preonline": "STAGE=PREONLINE NODE_OPTIONS=--max-old-space-size=8192 rspack serve -c rspack.config.dev.ts", "start:test": "STAGE=TEST NODE_OPTIONS=--max-old-space-size=8192 rspack serve -c rspack.config.dev.ts", "start:online": "STAGE=ONLINE NODE_ENV=development NODE_OPTIONS=--max-old-space-size=8192 rspack serve -c rspack.config.dev.ts", "start:online--force": "rm -rf ./node_modules/.cache/rspack && yarn start:online", "sf": "rm -rf ./node_modules/.cache/rspack && yarn start", "spf": "rm -rf ./node_modules/.cache/rspack && yarn start:preonline", "stf": "rm -rf ./node_modules/.cache/rspack && yarn start:test", "sof": "rm -rf ./node_modules/.cache/rspack && yarn start:online", "rsdoctor": "RSDOCTOR=true rspack serve -c rspack.config.dev.ts", "build:offline": "node scripts/checkTests.mjs && npm run lint:style && STAGE=TEST NODE_OPTIONS=--max-old-space-size=12288 rspack build", "build:online": "node scripts/checkTests.mjs && npm run lint:style && STAGE=ONLINE NODE_OPTIONS=--max-old-space-size=12288 rspack build", "build:preonline": "node scripts/checkTests.mjs && npm run lint:style && STAGE=PREONLINE NODE_OPTIONS=--max-old-space-size=12288 rspack build", "build:circular": "STAGE=CIRCULAR NODE_OPTIONS=--max-old-space-size=12288 rspack build", "lint": "skr lint", "lint-staged": "node scripts/lintStaged.js", "coverage": "vitest run --coverage", "postinstall": "husky install", "eks": "sh ./scripts/build_stable.sh offline && sh ./scripts/eks_replace.sh", "eks-push-only": "sh ./scripts/eks_replace.sh", "build:lishou": "vite build --config vite.lishou.config.ts", "publish:lishou": "npm publish src/components/AiReportSDK --registry=http://registry.npm.baidu-int.com", "build:escortIncentiveSDK": "vite build --config vite.escortIncentiveSDK.config.ts", "publish:escortIncentiveSDK": "npm publish src/components/EscortIncentiveSDK --registry=http://registry.npm.baidu-int.com", "mock": "node ./scripts/mock-generator.js", "lint:style": "stylelint \"src/**/*.{css,less}\"", "test": "vitest run", "checkTest": "node scripts/checkTests.mjs", "test:watch": "vitest"}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/fc-fe/fe-cangjie"}, "author": "zhangbo35", "license": "UNLICENSED", "dependencies": {"@baidu/abtest": "0.0.15", "@baidu/agile-survey": "^2.1.1", "@baidu/ai-materials": "0.0.22-alpha.7", "@baidu/aichat": "0.3.35-alpha.47", "@baidu/cdp-bot": "0.1.31", "@baidu/cpu-footer": "^0.1.6", "@baidu/cube-sdk": "0.1.50-alpha.5", "@baidu/dom-record-screen": "^0.2.8", "@baidu/feed-preview": "0.0.23", "@baidu/guid": "^1.0.1", "@baidu/hairuo-client": "0.2.1", "@baidu/index-bot": "0.1.23", "@baidu/light-ai-card": "0.1.10", "@baidu/light-ai-react": "1.15.5-alpha.1", "@baidu/one-charts": "2.8.2", "@baidu/one-ui": "4.41.15", "@baidu/one-ui-pro": "0.2.21-alpha.5", "@baidu/react-formulator": "0.0.16-alpha.16", "@baidu/weirwood-sdk": "^1.3.16-alpha.4", "@baidu/winds-ajax": "1.1.3", "@huse/router": "^0.11.1", "@lucky-canvas/react": "^0.1.13", "@module-federation/enhanced": "^0.8.9", "antd": "^5.23.0", "axios": "^1.4.0", "bce-bos-uploader-lite": "^1.0.11", "classnames": "^2.3.2", "dexie": "^4.0.10", "dexie-react-hooks": "^1.1.7", "diff": "^5.1.0", "dls-graphics": "^1.4.0", "dls-icons-react": "^3.57.1", "dls-illustrations-react": "^1.3.3", "echarts": "^5.4.3", "echarts-for-react": "^3.0.3", "echarts-liquidfill": "^3.1.0", "fast-json-stable-stringify": "^2.1.0", "fuse.js": "^7.1.0", "html-react-parser": "^4.2.1", "js-combinatorics": "^2.1.2", "less-loader": "^12.2.0", "less-plugin-dls": "^11.10.0", "lodash-es": "^4.17.21", "lz-string": "^1.5.0", "nanoid": "^5.0.7", "pinyin-pro": "^3.26.0", "qrcode.react": "^3.1.0", "query-string": "^8.1.0", "react": "17.0.2", "react-activation": "^0.12.4", "react-dom": "17.0.2", "react-dropzone": "^14.2.3", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.5.2", "react-markdown": "9.0.0", "react-masonry-css": "^1.0.16", "react-router-dom": "5.2.0", "react-slick": "0.29.0", "react-suspense-boundary": "^2.3.1", "regenerator-runtime": "^0.13.11", "rspack-manifest-plugin": "^5.0.3", "slick-carousel": "1.8.1", "spark-md5": "^3.0.0", "swr": "2.2.1", "uuid": "^9.0.0", "xlsx": "0.18.5"}, "devDependencies": {"@baidu/cc-nav": "1.5.5-beta.4", "@module-federation/rsbuild-plugin": "^0.8.9", "@originjs/vite-plugin-federation": "^1.3.5", "@reskript/cli": "6.0.0", "@reskript/cli-build": "6.0.0", "@reskript/cli-dev": "6.0.0", "@reskript/cli-lint": "6.0.0", "@reskript/config-lint": "6.0.0", "@reskript/settings": "6.0.0", "@rsdoctor/rspack-plugin": "^0.4.13", "@rspack/cli": "1.2.6", "@rspack/core": "1.2.6", "@rspack/plugin-react-refresh": "^1.0.1", "@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "5", "@testing-library/react": "12.0.0", "@testing-library/react-hooks": "7.0.1", "@types/lodash-es": "^4.17.8", "@types/react": "^17.0.2", "@types/react-dom": "^17.0.2", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.0.7", "chokidar": "^3.5.3", "core-js": "^3.25.2", "cross-env": "^7.0.3", "dayjs": "^1.11.9", "esbuild-loader": "^4.3.0", "eslint": "^8.19.0", "glob": "^10.3.3", "huse": "2.0.4", "husky": "^8.0.1", "jsdom": "^26.0.0", "querystring": "^0.2.1", "react-refresh": "^0.14.0", "rollup-plugin-typescript2": "^0.36.0", "rspack-circular-dependency-plugin": "^0.0.4", "string-width": "4.2.0", "stylelint": "^15.10.1", "ts-node": "^10.9.2", "typescript": "^4.7.4", "vconsole": "3.15.1", "vite": "^4.4.10", "vitest": "^3.0.7", "webpack": "^5.70.0", "webpack-manifest-plugin": "^5.0.0", "wrap-ansi": "6.2.0"}, "resolutions": {"@types/react": "^17", "@baidu/cube-sdk/@baidu/one-charts": "0.0.25"}}