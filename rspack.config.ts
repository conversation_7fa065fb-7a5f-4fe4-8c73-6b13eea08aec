/* eslint-disable no-console */
/* eslint-disable max-len */
import {defineConfig} from '@rspack/cli';
import {rspack} from '@rspack/core';
import {RspackManifestPlugin} from 'rspack-manifest-plugin';
import RspackCircularDependencyPlugin from 'rspack-circular-dependency-plugin';

import {createHtmlPluginInstances, generateEntriesAndHtmlPlugins, notify, today} from './src/utils/rspack';
import sharedDeps from './src/config/sharedDeps';
import {remotesForBuild} from './debug/remote';
import {CheckESModulesLinkingWarningPlugin} from './plugins/rspack/checkESModulesLinkingWarning';
import {ReactComponentStatsPlugin} from './plugins/rspack/reactComponentStatsPlugin';
const path = require('path');
const fs = require('fs');
const {EsbuildPlugin} = require('esbuild-loader');
const appDirectory = fs.realpathSync(process.cwd());
const resolveApp = (relativePath: string) => path.resolve(appDirectory, relativePath);

const STAGE = process.env.STAGE;

// Target browsers, see: https://github.com/browserslist/browserslist
const targets = ['chrome >= 87', 'edge >= 88', 'firefox >= 78', 'safari >= 14'];
let errPaths: any[] = [];
// Rspack 插件检测的循环引用数量：189【2025-03-25】，防止其他分支已经有增量，设置宽限到200
const MAX_CIRCULAR_DEPENDENCY_NUM = 0; // 这个数值随着循环引用的减少动态调整
export default (async () => {
    const entries = await generateEntriesAndHtmlPlugins({cwd: __dirname, srcDirectory: 'src/entries'});
    const htmlPlugins = createHtmlPluginInstances({entries});

    return defineConfig({
        context: __dirname,
        devtool: STAGE === 'ONLINE' ? 'hidden-source-map' : 'source-map',
        entry: entries.reduce((webpackEntry, appEntry) => {
            webpackEntry[appEntry.name] = appEntry.file;
            return webpackEntry;
        }, {}),
        output: {
            filename: 'assets/[name].[contenthash:10].js',
            chunkFilename: 'assets/[name].[contenthash:10].chunk.js',
            assetModuleFilename: 'assets/[hash][ext]',
            cssFilename: 'assets/[name].[contenthash:10].css',
            cssChunkFilename: 'assets/[name].[contenthash:10].chunk.css',
            publicPath: '/',
        },
        resolve: {
            extensions: ['...', '.ts', '.tsx', '.jsx'],
            alias: {
                '@': resolveApp('src'),
            },
        },
        module: {
            rules: [
                {
                    test: [/\.bmp$/, /\.gif$/, /\.jpe?g$/, /\.png$/],
                    type: 'asset',
                },
                {
                    test: /\.(jsx?|tsx?)$/,
                    resolve: {fullySpecified: false},
                    use: [
                        {
                            loader: 'builtin:swc-loader',
                            options: {
                                jsc: {
                                    parser: {
                                        syntax: 'typescript',
                                        tsx: true,
                                    },
                                    transform: {
                                        react: {
                                            runtime: 'automatic',
                                            development: false,
                                            refresh: false,
                                        },
                                    },
                                },
                                env: {targets},
                            },
                        },
                    ],
                },
                {
                    test: [/\.less$/],
                    use: [
                        {
                            loader: 'less-loader',
                            options: {lessOptions: {math: 'always'}},
                        },
                    ],
                    type: 'css/auto',
                },
                {
                    test: /\.svg$/,
                    resourceQuery: {
                        not: /^\?raw|url$/,
                    },
                    oneOf: [
                        {
                            // 如果挂了`?react`的，就直接转成组件返回
                            resourceQuery: /^\?react$/,
                            use: ['@svgr/webpack'],
                        },
                        {
                            resourceQuery: {
                                not: /^\?react$/,
                            },
                            type: 'asset',
                        },
                    ],
                },
            ],
            parser: {
                'css/auto': {
                    namedExports: false,
                },
            },
        },
        plugins: [
            new CheckESModulesLinkingWarningPlugin(),
            new rspack.DefinePlugin({
                'process.env': {STAGE: JSON.stringify(STAGE)},
            }),
            new rspack.container.ModuleFederationPluginV1({
                name: 'fecangjieapp',
                remotes: remotesForBuild,
                shared: sharedDeps,
            }),
            new RspackManifestPlugin({
                fileName: 'assets/asset-manifest.json',
            }),
            ...htmlPlugins,
            new RspackCircularDependencyPlugin({
                onStart() {
                    errPaths = [];
                },
                // 排除检测符合正则的文件
                exclude: /node_modules/,
                // 向 webpack 输出错误而不是警告
                failOnError: true,
                // 允许包含异步导入的循环
                // 举例：via import(/* webpackMode: "weak" */ './file.js')
                allowAsyncCycles: true,
                onDetected({paths}: any) {
                    errPaths.push(paths);
                },
                onEnd() {
                    // 输入到文件中
                    const sortedByJson = errPaths.sort((a, b) =>
                        JSON.stringify(a).localeCompare(JSON.stringify(b))
                    );
                    // 任意分支/流水线【除 build:circular 本地调试用】只要有增量，均结束流水线构建，报错！
                    if (process.env.STAGE !== 'CIRCULAR' && sortedByJson.length > MAX_CIRCULAR_DEPENDENCY_NUM) {
                        const content = `fe-cangjie 循环引用数量：${sortedByJson.length}，超过阈值：${MAX_CIRCULAR_DEPENDENCY_NUM}【${today()}】，本地执行 yarn build:circular 后查看 circular.json 中增量的循环引用文件！`;
                        const error = new Error(content);
                        notify(content);
                        error.name = 'CircularDependencyError';
                        throw error;
                    }
                    console.log(sortedByJson.length, '--------------------------------------------------');
                    fs.writeFileSync(
                        './circular.json',
                        JSON.stringify(sortedByJson, null, 2),
                        'utf8'
                    );
                },
            }),
            new ReactComponentStatsPlugin({
                srcDir: 'src', // 源码目录，默认 'src'
                outputFile: 'react-component-stats.json', // 输出文件，默认 'react-component-stats.json'
                extensions: ['.js', '.jsx', '.ts', '.tsx'], // 文件扩展名，默认 ['.js', '.jsx', '.ts', '.tsx']
            }),
        ],
        cache: true,
        experiments: {
            css: true,
            cache: {
                type: 'persistent',
            },
        },
        optimization: {
            minimizer: [
                new rspack.SwcJsMinimizerRspackPlugin(),
                // new rspack.LightningCssMinimizerRspackPlugin({
                //     minimizerOptions: {targets},
                // }),
                new EsbuildPlugin({
                    include: /\.css$/, // 只压缩css
                    css: true, // 启用 CSS 压缩
                }),
            ],
            splitChunks: {
                chunks: 'all',
                minChunks: 1,
                maxInitialRequests: 60,
                maxAsyncRequests: 60,
                cacheGroups: {
                    vendor: {
                        test: /node_modules/,
                        name: 'vendor',
                        minChunks: 1,
                        chunks: 'initial',
                        reuseExistingChunk: true,
                        priority: 7,
                    },
                    config: {
                        test: /src\/config\//,
                        name: 'config',
                        minChunks: 2,
                        chunks: 'async',
                        reuseExistingChunk: true,
                        priority: 7,
                    },
                    oneFamily: {
                        test: /@baidu\/one-ui/,
                        name: 'one-family',
                        chunks: 'initial',
                        reuseExistingChunk: true,
                        minChunks: 1,
                        priority: 10,
                    },
                    oneFamilyAsync: {
                        test: /@baidu\/one-ui/,
                        name(module: any) {
                            if (module.resource) {
                                module.resource.match(/@baidu\/(one-ui[^/]*)\//);
                                return RegExp.$1;
                            }
                            return 'one-ui';
                        },
                        chunks: 'async',
                        reuseExistingChunk: true,
                        minChunks: 1,
                        priority: 9,
                    },
                    echarts: {
                        test: /echarts/,
                        minChunks: 1,
                        priority: 9,
                        name: 'echarts',
                        reuseExistingChunk: true,
                        chunks: 'all',
                    },
                },
            },
        },
    });
})();
